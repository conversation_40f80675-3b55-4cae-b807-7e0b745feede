#!/usr/bin/env python3
"""
测试新的窗口计算逻辑
验证向前扩展窗口的正确性
"""

def calculate_windows_per_sample(num_segments, window_size=5):
    """
    模拟修正后的窗口计算逻辑
    """
    if num_segments < window_size:
        return 0

    # 1. 计算标准非重叠窗口数量
    stride = window_size - 1  # 步长=4
    standard_windows = 0

    # 从位置0开始，每次步进4，直到无法放下完整的5段窗口
    pos = 0
    while pos + window_size <= num_segments:
        standard_windows += 1
        pos += stride

    # 2. 检查最后一段是否被包含
    # 最后一段的索引是 num_segments - 1
    last_segment_idx = num_segments - 1

    # 检查最后一个标准窗口是否包含最后一段
    if standard_windows > 0:
        last_window_start = (standard_windows - 1) * stride
        last_window_end = last_window_start + window_size - 1

        if last_segment_idx <= last_window_end:
            # 最后一段已被包含，无需额外窗口
            return standard_windows
        else:
            # 最后一段未被包含，需要向前扩展
            return standard_windows + 1
    else:
        # 没有标准窗口但有足够段数，应该不会发生
        return 1 if num_segments >= window_size else 0

def get_window_positions(num_segments, window_size=5):
    """
    获取所有窗口的起始位置（修正版）
    """
    if num_segments < window_size:
        return []

    positions = []
    stride = window_size - 1  # 步长=4

    # 1. 标准窗口
    pos = 0
    while pos + window_size <= num_segments:
        positions.append(pos)
        pos += stride

    # 2. 检查是否需要向前扩展窗口
    last_segment_idx = num_segments - 1

    if positions:
        last_window_start = positions[-1]
        last_window_end = last_window_start + window_size - 1

        if last_segment_idx > last_window_end:
            # 最后一段未被包含，需要向前扩展
            extra_window_start = num_segments - window_size
            positions.append(extra_window_start)

    return positions

def test_window_calculation():
    """
    测试不同段数情况下的窗口计算
    """
    print("=== 窗口计算逻辑测试 ===\n")
    
    test_cases = [
        4,   # 不足5段
        5,   # 正好5段
        8,   # 8段
        9,   # 9段 (标准情况)
        10,  # 10段 (需要向前扩展)
        11,  # 11段 (需要向前扩展)
        12,  # 12段 (需要向前扩展)
        13,  # 13段 (正好3个窗口)
        14,  # 14段 (需要向前扩展)
        15,  # 15段 (需要向前扩展)
        16,  # 16段 (需要向前扩展)
        17,  # 17段 (正好4个窗口)
    ]
    
    for num_segments in test_cases:
        windows_count = calculate_windows_per_sample(num_segments)
        positions = get_window_positions(num_segments)
        
        print(f"段数: {num_segments}")
        print(f"  窗口数量: {windows_count}")
        print(f"  窗口位置: {positions}")
        
        # 显示每个窗口包含的段
        if positions:
            print(f"  窗口详情:")
            for i, start_pos in enumerate(positions):
                end_pos = start_pos + 5 - 1
                segments = list(range(start_pos, start_pos + 5))
                print(f"    窗口{i}: 段{segments} (位置{start_pos}-{end_pos})")
                
                # 检查是否超出边界
                if end_pos >= num_segments:
                    print(f"      ❌ 错误：窗口超出边界！")
        
        print()

def test_specific_cases():
    """
    测试您提到的具体案例
    """
    print("=== 具体案例测试 ===\n")
    
    # 案例1：9段数据，最后剩下段9,10,11 (实际是段8，因为从0开始)
    print("案例1：假设有11段数据 (段0-10)")
    num_segments = 11
    positions = get_window_positions(num_segments)
    print(f"窗口位置: {positions}")
    
    for i, start_pos in enumerate(positions):
        segments = list(range(start_pos, start_pos + 5))
        print(f"  窗口{i}: 段{segments}")
        
        # 检查最后一个窗口是否包含最后几段
        if i == len(positions) - 1:
            if segments[-3:] == [8, 9, 10]:  # 最后三段
                print(f"    ✅ 正确：包含了最后剩余的段8,9,10")
            else:
                print(f"    ❌ 错误：没有正确处理最后剩余段")
    
    print()

if __name__ == "__main__":
    test_window_calculation()
    test_specific_cases()
    
    print("=== 总结 ===")
    print("新的窗口计算逻辑特点：")
    print("1. 标准窗口：步长=4，非重叠滑窗")
    print("2. 向前扩展：当有剩余段时，向前扩展生成额外窗口")
    print("3. 避免重复：检查额外窗口是否与最后标准窗口重复")
    print("4. 完整覆盖：确保所有段都能被包含在某个窗口中")
