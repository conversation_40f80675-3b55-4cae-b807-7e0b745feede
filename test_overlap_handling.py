#!/usr/bin/env python3
"""
测试重叠预测处理
验证重叠部分的平均值计算
"""

def merge_overlapping_predictions(window_predictions, window_positions,
                                total_segments, space_points_per_segment):
    """
    合并重叠窗口的预测结果，对重叠部分取平均值

    重要：每个窗口只预测前4段，不预测第5段（可能受下一段影响）
    """
    total_points = total_segments * space_points_per_segment
    merged_prediction = [0.0] * total_points
    count_matrix = [0] * total_points  # 记录每个点被预测的次数

    print(f"合并重叠预测：总段数={total_segments}, 每段点数={space_points_per_segment}, 总点数={total_points}")
    print(f"预测策略：每个窗口只预测前4段（窗口大小-1）")

    for i, (pred, start_pos) in enumerate(zip(window_predictions, window_positions)):
        # pred形状: (4, space_points_per_segment) - 只预测前4段
        pred_flat = []
        for row in pred:
            pred_flat.extend(row)  # 展平为一维

        # 计算在全局数组中的起始和结束位置
        global_start = start_pos * space_points_per_segment
        global_end = global_start + len(pred_flat)

        # 确保不超出边界
        global_end = min(global_end, total_points)
        pred_len = global_end - global_start

        predicted_segments = list(range(start_pos, start_pos + 4))
        print(f"  窗口{i}: 输入段{start_pos}-{start_pos+4}, 预测段{predicted_segments}, 全局位置{global_start}-{global_end}")

        # 累加预测值和计数
        for j in range(pred_len):
            merged_prediction[global_start + j] += pred_flat[j]
            count_matrix[global_start + j] += 1

    # 对重叠部分取平均
    for i in range(total_points):
        if count_matrix[i] > 0:
            merged_prediction[i] /= count_matrix[i]

    # 显示重叠统计
    overlap_points = sum(1 for c in count_matrix if c > 1)
    predicted_points = sum(1 for c in count_matrix if c > 0)
    unpredicted_points = total_points - predicted_points
    print(f"  重叠点数: {overlap_points}/{total_points} ({overlap_points/total_points*100:.1f}%)")
    print(f"  预测点数: {predicted_points}/{total_points} ({predicted_points/total_points*100:.1f}%)")
    print(f"  未预测点数: {unpredicted_points}/{total_points} ({unpredicted_points/total_points*100:.1f}%)")

    return merged_prediction, count_matrix

def test_overlap_cases():
    """
    测试不同重叠情况
    """
    print("=== 重叠预测处理测试 ===\n")
    
    # 测试参数
    space_points_per_segment = 10  # 简化：每段10个点
    
    test_cases = [
        {
            "name": "9段数据 - 无重叠",
            "total_segments": 9,
            "window_positions": [0, 4],
            "description": "标准情况，无重叠"
        },
        {
            "name": "10段数据 - 有重叠",
            "total_segments": 10,
            "window_positions": [0, 4, 5],
            "description": "窗口1预测段0-3，窗口2预测段4-7，窗口3预测段5-8，段5-7重叠"
        },
        {
            "name": "11段数据 - 有重叠",
            "total_segments": 11,
            "window_positions": [0, 4, 6],
            "description": "窗口1预测段0-3，窗口2预测段4-7，窗口3预测段6-9，段6-7重叠"
        },
        {
            "name": "14段数据 - 多重重叠",
            "total_segments": 14,
            "window_positions": [0, 4, 8, 9],
            "description": "多个窗口，多处重叠"
        }
    ]
    
    for case in test_cases:
        print(f"测试案例: {case['name']}")
        print(f"描述: {case['description']}")
        
        total_segments = case['total_segments']
        window_positions = case['window_positions']
        
        # 生成模拟预测数据
        window_predictions = []
        for i, pos in enumerate(window_positions):
            # 每个窗口预测4段，每段space_points_per_segment个点
            # 为了便于观察，让每个窗口的预测值有所不同
            pred = [[i + 1] * space_points_per_segment for _ in range(4)]
            window_predictions.append(pred)
            print(f"  窗口{i}: 起始段{pos}, 预测段{pos}-{pos+3}, 预测值={i+1}")
        
        # 合并预测
        merged, count_matrix = merge_overlapping_predictions(
            window_predictions, window_positions, 
            total_segments, space_points_per_segment
        )
        
        # 分析结果
        print(f"  结果分析:")
        for seg in range(total_segments):
            seg_start = seg * space_points_per_segment
            seg_end = (seg + 1) * space_points_per_segment
            seg_counts = count_matrix[seg_start:seg_end]
            seg_values = merged[seg_start:seg_end]
            
            unique_counts = list(set(seg_counts))
            if len(unique_counts) == 1 and unique_counts[0] == 0:
                print(f"    段{seg}: 未预测")
            elif len(unique_counts) == 1 and unique_counts[0] == 1:
                print(f"    段{seg}: 单次预测，值={seg_values[0]:.1f}")
            elif len(unique_counts) == 1 and unique_counts[0] > 1:
                print(f"    段{seg}: {int(unique_counts[0])}次重叠预测，平均值={seg_values[0]:.1f}")
            else:
                print(f"    段{seg}: 混合预测情况")
        
        print()

def test_specific_overlap():
    """
    测试具体的重叠案例：10段数据
    """
    print("=== 具体重叠案例：10段数据 ===\n")
    
    total_segments = 10
    space_points_per_segment = 5  # 每段5个点，便于观察
    window_positions = [0, 4, 5]
    
    # 创建有区别的预测值
    window_predictions = [
        [[1, 1, 1, 1, 1],  # 段0
         [2, 2, 2, 2, 2],  # 段1
         [3, 3, 3, 3, 3],  # 段2
         [4, 4, 4, 4, 4]], # 段3

        [[10, 10, 10, 10, 10],  # 段4
         [20, 20, 20, 20, 20],  # 段5
         [30, 30, 30, 30, 30],  # 段6
         [40, 40, 40, 40, 40]], # 段7

        [[100, 100, 100, 100, 100],  # 段5
         [200, 200, 200, 200, 200],  # 段6
         [300, 300, 300, 300, 300],  # 段7
         [400, 400, 400, 400, 400]]  # 段8
    ]
    
    print("窗口预测详情:")
    print("  窗口0 输入段[0,1,2,3,4] → 预测段[0,1,2,3]: 预测值 [1,2,3,4]")
    print("  窗口1 输入段[4,5,6,7,8] → 预测段[4,5,6,7]: 预测值 [10,20,30,40]")
    print("  窗口2 输入段[5,6,7,8,9] → 预测段[5,6,7,8]: 预测值 [100,200,300,400]")
    print("  重叠段: 段5(20 vs 100), 段6(30 vs 200), 段7(40 vs 300)")
    print("  未预测段: 段9 (光纤最后一段，不预测)")
    
    merged, count_matrix = merge_overlapping_predictions(
        window_predictions, window_positions, 
        total_segments, space_points_per_segment
    )
    
    print("\n合并结果:")
    for seg in range(total_segments):
        seg_start = seg * space_points_per_segment
        seg_end = (seg + 1) * space_points_per_segment
        seg_values = merged[seg_start:seg_end]
        seg_counts = count_matrix[seg_start:seg_end]
        
        if seg_counts[0] == 0:
            print(f"  段{seg}: 未预测")
        elif seg_counts[0] == 1:
            print(f"  段{seg}: 单次预测 = {seg_values[0]}")
        else:
            print(f"  段{seg}: {int(seg_counts[0])}次预测平均 = {seg_values[0]}")
    
    # 验证重叠段的平均值计算
    print("\n验证重叠段平均值:")
    print(f"  段5: (20 + 100) / 2 = {(20 + 100) / 2} (实际: {merged[25]})")
    print(f"  段6: (30 + 200) / 2 = {(30 + 200) / 2} (实际: {merged[30]})")
    print(f"  段7: (40 + 300) / 2 = {(40 + 300) / 2} (实际: {merged[35]})")

if __name__ == "__main__":
    test_overlap_cases()
    test_specific_overlap()
    
    print("=== 总结 ===")
    print("重叠处理机制:")
    print("1. 累加所有窗口的预测值")
    print("2. 记录每个点被预测的次数")
    print("3. 对重叠部分取平均值")
    print("4. 确保所有段都有预测值")
    print("5. 重叠主要发生在向前扩展窗口与前一窗口之间")
