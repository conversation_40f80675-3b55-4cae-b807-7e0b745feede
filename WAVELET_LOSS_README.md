# 小波感知损失函数 - 解决MSE"和稀泥"问题

## 🎯 问题背景

在BOTDA-BFS信号处理中，传统的MSE损失函数存在"和稀泥"特性：
- **问题**：当目标信号包含复杂的高频变化时，MSE倾向于用平滑的中间值来拟合
- **结果**：模型预测丢失高频细节，无法准确重建信号的快速变化部分
- **影响**：在sample_527的Segment 3等复杂区域，模型无法捕捉"下降→平坦→上升→平坦→下降"的精细模式

## 🔧 解决方案：小波感知损失

### 核心思想
使用**多尺度小波变换**分解信号，重点惩罚高频分量的缺失：
- **小波分解**：将信号分解为多个尺度的高频和低频分量
- **高频关注**：主要计算高频系数的损失，强化细节保持
- **多尺度权重**：细节层权重更大，粗糙层权重递减

### 技术优势
1. **时频局部化**：小波变换同时保持时域和频域信息
2. **多尺度分析**：能够捕捉不同尺度的信号特征
3. **高频敏感**：直接惩罚高频信息的丢失
4. **梯度友好**：支持端到端训练，GPU加速

## 📦 安装依赖

```bash
pip install pytorch_wavelets==1.3
```

## 🚀 使用方法

### 1. 基本使用

```python
from wavelet_loss import MultiScaleWaveletLoss

# 初始化小波损失
wavelet_criterion = MultiScaleWaveletLoss(
    wave='db4',           # Daubechies 4小波
    level=3,              # 3层分解
    loss_type='l1',       # L1损失
    base_weight=0.05,     # 基础权重5%
    decay=0.6             # 权重衰减
).to(device)

# 在训练循环中使用
main_loss = criterion(pred, target)
wave_loss = wavelet_criterion(pred, target)
total_loss = main_loss + wave_loss
```

### 2. 完整训练集成

```python
# 计算所有损失分量
main_loss = criterion(bfs_pred, bfs_target)
smooth_loss = smoothness_loss(bfs_pred, 2e-4)
grad_loss = gradient_loss(bfs_pred, bfs_target, alpha=100.0)
wave_loss = wavelet_criterion(bfs_pred, bfs_target)  # 新增

# 总损失
total_loss = main_loss + smooth_loss + grad_loss + wave_loss
```

## ⚙️ 参数配置

### 小波损失参数
- **wave**: 小波基类型
  - `'db4'`: Daubechies 4，适合信号处理
  - `'haar'`: Haar小波，计算简单
  - `'sym5'`: Symlet 5，对称性好
  
- **level**: 分解层数
  - `3`: 适合200点信号（200→25点）
  - `4`: 适合更长信号
  
- **base_weight**: 基础权重
  - `0.05`: 让小波损失占总损失的5%
  - `0.1`: 更强的高频约束
  
- **decay**: 权重衰减
  - `0.6`: 中等衰减，平衡各尺度
  - `0.8`: 慢衰减，更关注粗尺度

### 推荐配置
```python
# 对于BOTDA-BFS信号（200点）
wavelet_criterion = MultiScaleWaveletLoss(
    wave='db4',           # 信号处理标准选择
    level=3,              # 3层足够覆盖主要频率
    loss_type='l1',       # L1对异常值更鲁棒
    base_weight=0.05,     # 5%权重，不过度影响主损失
    decay=0.6,            # 让细节层权重更大
    include_lowpass=False # 只关注高频，不包含趋势
)
```

## 🔄 备用方案：频域损失

如果pytorch_wavelets安装失败，自动使用FFT频域损失：

```python
from wavelet_loss import FrequencyDomainLoss

freq_criterion = FrequencyDomainLoss(
    weight=0.05,          # 与小波损失相同权重
    high_freq_boost=3.0,  # 高频增强3倍
    loss_type='l1'
)
```

## 📊 效果验证

### 损失分解示例
```
主损失 (MSE): 1.433931
平滑损失: 0.000138
梯度损失: 283.834686
小波损失: 0.087126      ← 新增
总损失: 285.355896
```

### 高频敏感性测试
```
平滑信号小波损失: 0.000000
噪声信号小波损失: 0.007122
高频敏感性比率: 712227.77x  ← 对高频变化极其敏感
```

## 🎯 预期效果

1. **更好的细节保持**：模型能够学习到信号的高频变化
2. **减少"和稀泥"**：避免用平滑曲线拟合复杂变化
3. **提升边缘锐度**：更准确地重建信号的上升/下降沿
4. **保持训练稳定**：权重适中，不会破坏原有训练动态

## 🔧 故障排除

### 常见问题
1. **pytorch_wavelets安装失败**
   - 自动回退到FFT频域损失
   - 效果略有差异但仍有效

2. **显存占用增加**
   - 小波变换需要额外显存
   - 可以减少batch_size或降低level

3. **训练不稳定**
   - 降低base_weight（如0.02）
   - 增加decay值（如0.8）

### 调试技巧
```python
# 检查小波损失的贡献
print(f"小波损失占比: {wave_loss.item() / total_loss.item() * 100:.1f}%")

# 应该在5-10%之间，过高可能影响训练稳定性
```

## 📈 性能监控

在TensorBoard中监控各损失分量：
- `Loss/Main`: 主MSE损失
- `Loss/Smooth`: 平滑性损失  
- `Loss/Gradient`: 梯度损失
- `Loss/Wavelet`: 小波损失（新增）
- `Loss/Total`: 总损失

理想情况下，小波损失应该随训练逐渐下降，表明模型在学习高频细节。
