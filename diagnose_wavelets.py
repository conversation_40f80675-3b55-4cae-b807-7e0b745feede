#!/usr/bin/env python3
# ---------------------------------------------------------------
#  pytorch_wavelets 导入问题诊断脚本
#  
#  功能：
#  1. 详细诊断pytorch_wavelets导入失败的真正原因
#  2. 显示完整的错误堆栈信息
#  3. 提供针对性的解决方案
# ---------------------------------------------------------------

import sys
import os
import traceback

def print_section(title):
    """打印分节标题"""
    print("\n" + "="*60)
    print(f"🔍 {title}")
    print("="*60)

def check_basic_info():
    """检查基本环境信息"""
    print_section("基本环境信息")
    print(f"Python版本: {sys.version}")
    print(f"Python可执行文件: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"Python路径 (前5个):")
    for i, path in enumerate(sys.path[:5]):
        print(f"  {i+1}. {path}")

def check_package_installation():
    """检查包安装状态"""
    print_section("检查包安装状态")
    try:
        import subprocess
        result = subprocess.run([sys.executable, "-m", "pip", "show", "pytorch_wavelets"], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ pytorch_wavelets 包已安装")
            print("详细信息:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    print(f"  {line}")
        else:
            print("❌ pytorch_wavelets 包未安装")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
    except Exception as e:
        print(f"⚠️  无法检查包安装状态: {e}")

def test_pytorch_wavelets_import():
    """测试pytorch_wavelets导入"""
    print_section("测试 pytorch_wavelets 包导入")
    
    # 第一步：尝试导入包本身
    try:
        import pytorch_wavelets
        print("✅ 步骤1: pytorch_wavelets 包导入成功")
        print(f"  版本: {getattr(pytorch_wavelets, '__version__', 'Unknown')}")
        print(f"  路径: {getattr(pytorch_wavelets, '__file__', 'Unknown')}")
    except ImportError as e:
        print("❌ 步骤1: pytorch_wavelets 包导入失败 (ImportError)")
        print(f"  错误信息: {e}")
        return False
    except Exception as e:
        print("❌ 步骤1: pytorch_wavelets 包导入失败 (其他错误)")
        print(f"  错误类型: {type(e).__name__}")
        print(f"  错误信息: {e}")
        print("\n--- 详细错误追溯 ---")
        traceback.print_exc()
        return False
    
    # 第二步：尝试导入DWT1DForward
    try:
        from pytorch_wavelets import DWT1DForward
        print("✅ 步骤2: DWT1DForward 导入成功")
        print(f"  类型: {type(DWT1DForward)}")
    except ImportError as e:
        print("❌ 步骤2: DWT1DForward 导入失败 (ImportError)")
        print(f"  错误信息: {e}")
        return False
    except Exception as e:
        print("❌ 步骤2: DWT1DForward 导入失败 (其他错误)")
        print(f"  错误类型: {type(e).__name__}")
        print(f"  错误信息: {e}")
        print("\n--- 详细错误追溯 ---")
        traceback.print_exc()
        return False
    
    # 第三步：尝试创建和使用DWT1DForward
    try:
        dwt = DWT1DForward(J=3, wave='db4', mode='zero')
        print("✅ 步骤3: DWT1DForward 实例创建成功")
        
        # 测试功能
        import torch
        test_signal = torch.randn(1, 1, 200)
        Yl, Yh = dwt(test_signal)
        print(f"✅ 步骤4: 小波变换测试成功")
        print(f"  输入: {test_signal.shape}")
        print(f"  低频输出: {Yl.shape}")
        print(f"  高频层数: {len(Yh)}")
        
        return True
        
    except Exception as e:
        print("❌ 步骤3/4: DWT1DForward 使用失败")
        print(f"  错误类型: {type(e).__name__}")
        print(f"  错误信息: {e}")
        print("\n--- 详细错误追溯 ---")
        traceback.print_exc()
        return False

def check_dependencies():
    """检查相关依赖"""
    print_section("检查相关依赖")
    
    dependencies = [
        ('torch', 'PyTorch'),
        ('numpy', 'NumPy'),
        ('six', 'Six'),
    ]
    
    for module_name, display_name in dependencies:
        try:
            module = __import__(module_name)
            version = getattr(module, '__version__', 'Unknown')
            print(f"✅ {display_name}: v{version}")
        except ImportError:
            print(f"❌ {display_name}: 未安装")
        except Exception as e:
            print(f"⚠️  {display_name}: 导入异常 - {e}")

def provide_solutions(success):
    """提供解决方案"""
    print_section("解决方案建议")
    
    if success:
        print("🎉 pytorch_wavelets 工作正常！")
        print("   如果在其他脚本中仍有问题，请检查:")
        print("   1. 是否使用了相同的Python环境")
        print("   2. 是否有其他异常处理代码隐藏了真正的错误")
    else:
        print("⚠️  pytorch_wavelets 存在问题，建议:")
        print()
        print("1. 重新安装 pytorch_wavelets:")
        print("   pip uninstall pytorch_wavelets")
        print("   pip install pytorch_wavelets==1.3.0")
        print()
        print("2. 检查PyTorch版本兼容性:")
        print("   pytorch_wavelets 1.3.0 可能需要特定的PyTorch版本")
        print("   尝试: pip install torch==1.13.0")
        print()
        print("3. 如果问题持续，使用FFT替代方案:")
        print("   系统会自动回退到频域损失函数")

def main():
    """主函数"""
    print("🔍 pytorch_wavelets 导入问题诊断工具")
    print("此工具将帮助您找出pytorch_wavelets导入失败的真正原因")
    
    check_basic_info()
    check_package_installation()
    check_dependencies()
    success = test_pytorch_wavelets_import()
    provide_solutions(success)
    
    print("\n" + "="*60)
    print("🏁 诊断完成")
    print("="*60)

if __name__ == '__main__':
    main()
