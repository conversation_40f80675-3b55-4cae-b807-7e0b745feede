#!/usr/bin/env python3
# 诊断pytorch_wavelets导入问题

import sys
import os

print("🔍 诊断pytorch_wavelets导入问题...")
print("=" * 50)

# 1. 基本环境信息
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")
print()

# 2. 检查包是否安装
print("📦 检查包安装状态:")
try:
    import subprocess
    result = subprocess.run([sys.executable, "-m", "pip", "show", "pytorch_wavelets"], 
                          capture_output=True, text=True)
    if result.returncode == 0:
        print("✅ pytorch_wavelets 已安装")
        print("详细信息:")
        for line in result.stdout.split('\n')[:8]:  # 显示前8行
            if line.strip():
                print(f"  {line}")
    else:
        print("❌ pytorch_wavelets 未安装")
        print(f"错误: {result.stderr}")
except Exception as e:
    print(f"⚠️  检查安装状态失败: {e}")

print()

# 3. 尝试导入pytorch_wavelets包
print("📥 尝试导入pytorch_wavelets包:")
try:
    import pytorch_wavelets
    print("✅ pytorch_wavelets 包导入成功")
    print(f"  版本: {getattr(pytorch_wavelets, '__version__', 'Unknown')}")
    print(f"  路径: {getattr(pytorch_wavelets, '__file__', 'Unknown')}")
    
    # 检查包内容
    attrs = [attr for attr in dir(pytorch_wavelets) if not attr.startswith('_')]
    print(f"  包含 {len(attrs)} 个公共属性/函数")
    print(f"  主要内容: {attrs[:5]}...")
    
except ImportError as e:
    print(f"❌ pytorch_wavelets 包导入失败")
    print(f"  ImportError: {e}")
except Exception as e:
    print(f"⚠️  pytorch_wavelets 包导入异常")
    print(f"  Exception: {e}")

print()

# 4. 尝试导入DWT1DForward
print("🌊 尝试导入DWT1DForward:")
try:
    from pytorch_wavelets import DWT1DForward
    print("✅ DWT1DForward 导入成功")
    print(f"  类型: {type(DWT1DForward)}")
    
    # 尝试创建实例
    try:
        dwt = DWT1DForward(J=3, wave='db4', mode='zero')
        print("✅ DWT1DForward 实例创建成功")
        
        # 尝试使用
        import torch
        test_signal = torch.randn(1, 1, 200)
        Yl, Yh = dwt(test_signal)
        print(f"✅ 小波变换测试成功: {test_signal.shape} -> 低频{Yl.shape} + 高频{len(Yh)}层")
        
    except Exception as e:
        print(f"❌ DWT1DForward 使用失败: {e}")
        
except ImportError as e:
    print(f"❌ DWT1DForward 导入失败")
    print(f"  ImportError: {e}")
except Exception as e:
    print(f"⚠️  DWT1DForward 导入异常")
    print(f"  Exception: {e}")

print()

# 5. 检查可能的导入路径问题
print("🔍 检查导入路径:")
try:
    import pytorch_wavelets
    print(f"✅ 包路径: {pytorch_wavelets.__file__}")
    
    # 检查包目录内容
    import os
    pkg_dir = os.path.dirname(pytorch_wavelets.__file__)
    files = os.listdir(pkg_dir)
    py_files = [f for f in files if f.endswith('.py')]
    print(f"  包目录: {pkg_dir}")
    print(f"  Python文件: {py_files[:5]}...")
    
except Exception as e:
    print(f"❌ 无法检查包路径: {e}")

print()

# 6. 模拟wavelet_loss.py的导入逻辑
print("🧪 模拟wavelet_loss.py的导入逻辑:")
try:
    from pytorch_wavelets import DWT1DForward
    WAVELETS_AVAILABLE = True
    print("✅ 导入成功，WAVELETS_AVAILABLE = True")
except ImportError:
    WAVELETS_AVAILABLE = False
    print("❌ 导入失败，WAVELETS_AVAILABLE = False")
    print("   这就是为什么看到FFT替代方案的原因")

print()
print("=" * 50)
if WAVELETS_AVAILABLE:
    print("🎉 pytorch_wavelets 工作正常！")
else:
    print("⚠️  pytorch_wavelets 存在问题，需要进一步调查")
