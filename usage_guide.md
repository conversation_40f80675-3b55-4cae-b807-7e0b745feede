# 三种方法BFS分布对比分析完整使用指南

## 📋 概述

本指南将帮助您完成深度学习预测、MATLAB传统方法和真实BFS分布的三方对比分析，特别针对0.5m频移分布的识别能力评估。

## 🔄 完整使用流程

### 步骤1：生成测试样本
使用`test.m`MATLAB文件生成包含0.5m频移分布的测试样本。

```matlab
% 在MATLAB中运行
test
```

**功能**：
- 生成包含0.5m频移分布的测试样本
- 保存归一化的BFS分布和归一化参数
- 输出文件：
  - `dazuhv/test/test_sample_0001.mat` - 测试样本数据
  - `dazuhv/bfs_stats.mat` - 归一化参数

### 步骤2：运行深度学习测试（可选）
```bash
python botda_test.py
```

**功能**：
- 加载训练好的深度学习模型
- 对测试样本进行BFS预测
- **自动保存反归一化后的预测BFS数据**

**输出文件**：
- `results/bfs_data_file_0.npz` - 包含深度学习预测结果
- 各种可视化图片

### 步骤3：运行MATLAB传统方法
```matlab
% 在MATLAB中运行
erjp_tsyigsui_200m
```

**修改后的功能**：
- **自动检测并读取test.m生成的测试样本**
- **自动反归一化BFS分布**
- **使用test.m测试数据替换原始BFS计算**
- 运行传统BOTDA解调算法
- **自动保存传统方法的BFS测量结果**

**输出文件**：
- `results/matlab_bfs_measurement.mat` - MATLAB格式的测量结果
- `results/matlab_bfs_measurement.csv` - CSV格式的测量结果
- `BFS_distribution.fig` 和 `BFS_distribution.png` - 传统方法的可视化结果

### 步骤4：运行三方法对比分析
```bash
python compare_three_methods.py
```

**功能**：
- 读取test.m的真实BFS分布
- 读取深度学习预测结果（如果存在）
- 读取MATLAB传统方法结果
- 自动检测0.5m频移区域
- 生成主对比图和局部放大图

**输出文件**：
- `results/three_methods_comparison.png` - 主对比图
- `results/strain_region_X_zoom.png` - 每个频移区域的放大图

## 📊 生成的对比图说明

### 主对比图 (`three_methods_comparison.png`)
- **蓝色实线**：真实BFS分布
- **红色虚线**：深度学习预测结果
- **绿色点线**：MATLAB传统方法结果
- **统计信息框**：包含三种方法的均值、标准差和误差对比

### 应变区域放大图 (`strain_region_X_zoom.png`)
- **自动检测**：基于统计阈值识别0.5m频移区域
- **5m范围**：每个放大图显示频移中心±2.5m的范围
- **1m刻度**：x轴每1m显示一个刻度
- **详细对比**：三种方法在局部区域的精确对比
- **局部统计**：每个区域的误差分析

## 🔧 关键修改说明

### MATLAB文件修改 (`erjp_tsyigsui_200m.m`)

1. **添加了test.m数据读取模块**：
   - 自动检测`dazuhv/test/test_sample_0001.mat`和`dazuhv/bfs_stats.mat`
   - 读取归一化的BFS分布和归一化参数
   - 自动反归一化得到真实BFS分布
   - 设置`use_test_data`标志

2. **替换了BFS计算逻辑**：
   ```matlab
   if use_test_data
       % 使用test.m测试数据
       sorted_positions = z_positions_test;
       sorted_BFS = input_bfs_distribution;  % 反归一化后的BFS
   else
       % 使用原始计算结果
       [sorted_positions, sort_idx] = sort(full_fiber_positions);
       sorted_BFS = full_fiber_BFS(sort_idx);
   end
   ```

3. **添加了反归一化功能**：
   ```matlab
   % 反归一化公式: x = x' * (max - min) + min
   bfs_denormalized = bfs_normalized * (bfs_max - bfs_min) + bfs_min;
   ```

4. **添加了结果保存模块**：
   - 保存为`.mat`和`.csv`两种格式
   - 提供详细的数据统计信息
   - 标明数据来源（test.m测试样本）

### Python对比分析文件修改 (`compare_three_methods.py`)

修改了数据读取逻辑：
```python
def load_test_m_and_python_results():
    # 1. 读取test.m生成的真实BFS分布
    test_data = sio.loadmat('dazuhv/test/test_sample_0001.mat')
    bfs_stats = sio.loadmat('dazuhv/bfs_stats.mat')

    # 2. 反归一化得到真实BFS
    target = bfs_normalized * (bfs_max - bfs_min) + bfs_min

    # 3. 读取Python预测结果（如果存在）
    if os.path.exists('results/bfs_data_file_0.npz'):
        prediction = np.load('results/bfs_data_file_0.npz')['prediction']
```

## 🎯 预期结果

### 性能对比指标
- **准确性**：MAE (平均绝对误差) 对比
- **稳定性**：标准差对比
- **局部精度**：0.5m频移区域的识别能力

### 可视化效果
- **全局对比**：200m光纤的完整BFS分布对比
- **局部放大**：每个0.5m频移区域的详细对比
- **统计分析**：定量的误差分析和性能评估

## ⚠️ 注意事项

1. **数据一致性**：确保所有方法使用相同的测试样本
2. **路径设置**：确保`results`目录存在且有写入权限
3. **MATLAB版本**：确保MATLAB支持`writematrix`函数（R2019a+）
4. **Python依赖**：确保安装了`scipy`、`pandas`、`matplotlib`等库

## 🔍 故障排除

### 常见问题
1. **找不到数据文件**：检查是否按顺序执行了所有步骤
2. **MATLAB读取失败**：确保运行了`convert_data_for_matlab.py`
3. **图片生成失败**：检查Python依赖库是否完整安装
4. **数据长度不匹配**：系统会自动进行插值对齐

### 调试信息
每个步骤都会输出详细的状态信息，包括：
- 文件路径和存在性检查
- 数据形状和统计信息
- 处理进度和结果确认

通过这个完整的流程，您将能够全面评估深度学习方法在0.5m频移识别上相对于传统MATLAB方法的性能优势！
