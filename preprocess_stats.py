#!/usr/bin/env python3
"""
预计算训练集的统计量（均值和标准差）
用于标准化预处理，只在训练集上计算，然后应用到所有数据集
"""

import numpy as np
import glob
import scipy.io as sio
import json
from tqdm import tqdm
import os

def compute_dataset_stats():
    """
    计算训练集的Stokes信号和BFS标签的统计量
    """
    DATA_DIR = "./BOTDA_Dataset/train"  # 只用训练集计算统计量
    
    if not os.path.exists(DATA_DIR):
        print(f"错误：训练数据目录不存在: {DATA_DIR}")
        return
    
    file_paths = glob.glob(f"{DATA_DIR}/*.mat")
    
    if len(file_paths) == 0:
        print(f"错误：在 {DATA_DIR} 中没有找到.mat文件")
        return
    
    print(f"找到 {len(file_paths)} 个训练文件")
    
    all_stokes_data = []
    all_bfs_data = []
    
    print("加载所有训练数据以计算统计量...")
    
    for path in tqdm(file_paths, desc="处理文件"):
        try:
            # 加载MATLAB文件
            mat_data = sio.loadmat(path)
            sample_data = mat_data['sample_data']
            
            # 提取Stokes信号
            stokes_signals = sample_data['stokes_signals'][0, 0]
            
            # 处理Stokes信号（假设是(1, num_segments)的cell array）
            for i in range(stokes_signals.shape[1]):
                segment = stokes_signals[0, i]
                if segment.size > 0:
                    # 展平并添加到列表
                    all_stokes_data.append(segment.flatten())
            
            # 提取原始BFS标签
            # 使用原始的frequency_shift_distribution，不是归一化版本
            if 'frequency_shift_distribution' in sample_data.dtype.names:
                original_bfs = sample_data['frequency_shift_distribution'][0, 0]
                if original_bfs.size > 0:
                    all_bfs_data.append(original_bfs.flatten())
            else:
                print(f"警告：文件 {path} 中没有找到 frequency_shift_distribution")
                
        except Exception as e:
            print(f"处理文件 {path} 时出错: {e}")
            continue
    
    if len(all_stokes_data) == 0:
        print("错误：没有成功加载任何Stokes数据")
        return
    
    if len(all_bfs_data) == 0:
        print("错误：没有成功加载任何BFS数据")
        return
    
    # 拼接成大数组
    print("计算Stokes信号统计量...")
    all_stokes_data = np.concatenate(all_stokes_data)
    stokes_mean = np.mean(all_stokes_data)
    stokes_std = np.std(all_stokes_data)
    
    print("计算BFS标签统计量...")
    all_bfs_data = np.concatenate(all_bfs_data)
    bfs_mean = np.mean(all_bfs_data)
    bfs_std = np.std(all_bfs_data)
    
    # 显示结果
    print(f"\n=== 统计量计算完成 ===")
    print(f"Stokes信号:")
    print(f"  样本数: {len(all_stokes_data):,}")
    print(f"  均值 (μ): {stokes_mean:.6f}")
    print(f"  标准差 (σ): {stokes_std:.6f}")
    print(f"  范围: [{np.min(all_stokes_data):.6f}, {np.max(all_stokes_data):.6f}]")
    
    print(f"\nBFS标签:")
    print(f"  样本数: {len(all_bfs_data):,}")
    print(f"  均值 (μ): {bfs_mean:.6f} MHz")
    print(f"  标准差 (σ): {bfs_std:.6f} MHz")
    print(f"  范围: [{np.min(all_bfs_data):.6f}, {np.max(all_bfs_data):.6f}] MHz")
    
    # 保存统计量
    stats = {
        'stokes_mean': float(stokes_mean),
        'stokes_std': float(stokes_std),
        'bfs_mean': float(bfs_mean),
        'bfs_std': float(bfs_std),
        'stokes_samples': int(len(all_stokes_data)),
        'bfs_samples': int(len(all_bfs_data))
    }
    
    stats_path = 'dataset_stats.json'
    with open(stats_path, 'w') as f:
        json.dump(stats, f, indent=2)
    
    print(f"\n✅ 统计量已保存到: {stats_path}")
    print("现在可以在训练代码中使用这些统计量进行标准化预处理")

if __name__ == "__main__":
    compute_dataset_stats()
