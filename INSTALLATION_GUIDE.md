# BOTDA深度学习项目安装和使用指南

## 📦 环境安装

### 1. 安装Python依赖包

在项目根目录下运行以下命令：

```bash
pip install -r requirements.txt
```

如果安装过程中遇到问题，可以逐个安装：

```bash
# 核心深度学习框架
pip install torch>=2.0.0 torchvision>=0.15.0 torchaudio>=2.0.0

# TensorBoard可视化
pip install tensorboard>=2.10.0

# 科学计算
pip install numpy>=1.21.0 scipy>=1.7.0

# 数据可视化
pip install matplotlib>=3.5.0

# 进度条
pip install tqdm>=4.62.0

# 机器学习工具
pip install scikit-learn>=1.0.0
```

### 2. 验证安装

运行以下命令验证安装是否成功：

```bash
python -c "import torch; print(f'PyTorch版本: {torch.__version__}')"
python -c "import tensorboard; print('TensorBoard安装成功')"
python -c "import numpy, scipy, matplotlib; print('科学计算包安装成功')"
```

## 🗂️ 数据准备

确保您的数据目录结构如下：

```
SBS/
├── botda_train.py
├── botda_test.py
├── botda_model.py
├── requirements.txt
└── BOTDA_Dataset/
    ├── bfs_stats.mat
    ├── train/
    │   ├── train_sample_0001.mat
    │   ├── train_sample_0002.mat
    │   └── ...
    ├── val/
    │   ├── val_sample_0008.mat
    │   ├── val_sample_0009.mat
    │   └── ...
    └── test/
        ├── test_sample_0011.mat
        ├── test_sample_0012.mat
        └── ...
```

## 🚀 使用方法

### 1. 训练模型

```bash
python botda_train.py
```

训练过程中会：
- 自动显示第一个训练文件的数据可视化
- 显示训练进度条
- 保存最佳模型到 `./checkpoints/` 目录
- 生成TensorBoard日志

### 2. 使用TensorBoard监控训练

在**新的命令行窗口**中运行：

```bash
tensorboard --logdir=./checkpoints/tensorboard
```

然后在浏览器中打开：http://localhost:6006

### 3. 测试模型

```bash
python botda_test.py
```

测试过程中会：
- 加载训练好的模型
- 在测试集上评估性能
- 让您选择展示单个光纤的完整结果
- 生成可视化图表

## 📊 TensorBoard使用指南（小白版）

### 什么是TensorBoard？
TensorBoard是一个可视化工具，可以实时监控深度学习模型的训练过程。

### 如何启动TensorBoard？

1. **打开新的命令行窗口**（不要关闭训练窗口）
2. **切换到项目目录**：
   ```bash
   cd C:\Users\<USER>\Desktop\SBS
   ```
3. **启动TensorBoard**：
   ```bash
   tensorboard --logdir=./checkpoints/tensorboard
   ```
4. **打开浏览器**，访问：http://localhost:6006

### TensorBoard界面说明

#### 📈 SCALARS标签页（最重要）
- **Train/BatchLoss**: 每个批次的训练损失（实时更新）
- **Train/LearningRate**: 学习率变化
- **Validation/Loss**: 每个epoch的验证损失
- **Epoch/TrainLoss**: 每个epoch的平均训练损失
- **Epoch/ValLoss**: 每个epoch的验证损失

#### 🔍 如何看图表？
- **X轴**: 训练步数或epoch数
- **Y轴**: 损失值或学习率
- **趋势**: 损失应该逐渐下降
- **过拟合**: 训练损失下降但验证损失上升

### 常见问题解决

#### Q1: TensorBoard显示"No dashboards are active"
**解决**: 等待训练开始，TensorBoard需要一些时间生成数据

#### Q2: 浏览器无法打开http://localhost:6006
**解决**: 
1. 检查TensorBoard是否正常启动
2. 尝试http://127.0.0.1:6006
3. 重启TensorBoard

#### Q3: 图表不更新
**解决**: 刷新浏览器页面（F5）

## 📁 输出文件说明

训练完成后，会在 `./checkpoints/` 目录下生成：

```
checkpoints/
├── best_model.pth              # 最佳模型权重
├── final_model.pth             # 最终模型权重
├── training_history.json       # 训练历史记录
├── loss_curve.png             # 损失曲线图
└── tensorboard/               # TensorBoard日志
    ├── events.out.tfevents.*  # 事件文件
    └── ...
```

## ⚠️ 注意事项

1. **GPU使用**: 如果有NVIDIA GPU，确保安装了CUDA
2. **内存不足**: 如果内存不够，可以减小BATCH_SIZE
3. **训练时间**: 完整训练可能需要几小时到几天
4. **数据路径**: 确保BOTDA_Dataset目录在正确位置
5. **TensorBoard端口**: 如果6006端口被占用，TensorBoard会自动选择其他端口

## 🆘 获取帮助

如果遇到问题：
1. 检查错误信息
2. 确认数据目录结构正确
3. 验证所有依赖包已安装
4. 查看TensorBoard日志了解训练状态
