#!/usr/bin/env python3
"""
增强版BOTDA超分辨率网络模型
主要改进：
1. 减少下采样次数，保持高频细节
2. 添加特征融合模块
3. 增强对连续频移的识别能力
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional


class ResDilatedBlock(nn.Module):
    """
    一维残差空洞卷积块
    结合了残差连接和空洞卷积，既能增大感受野又能保持梯度流动
    """
    def __init__(self, channels: int, dilation: int = 1, dropout_rate: float = 0.3):
        super().__init__()
        
        # 第一个卷积：特征提取
        self.conv1 = nn.Conv1d(channels, channels, kernel_size=3, 
                              padding=dilation, dilation=dilation, bias=False)
        self.bn1 = nn.BatchNorm1d(channels)
        
        # 第二个卷积：特征细化
        self.conv2 = nn.Conv1d(channels, channels, kernel_size=3, 
                              padding=dilation, dilation=dilation, bias=False)
        self.bn2 = nn.BatchNorm1d(channels)
        
        self.dropout = nn.Dropout(dropout_rate)
        self.activation = nn.GELU()

    def forward(self, x):
        residual = x
        
        out = self.conv1(x)
        out = self.bn1(out)
        out = self.activation(out)
        out = self.dropout(out)
        
        out = self.conv2(out)
        out = self.bn2(out)
        
        # 残差连接
        out += residual
        out = self.activation(out)
        
        return out


class SelfAttentionBlock(nn.Module):
    """
    自注意力模块
    用于网络瓶颈处，建立全局依赖。
    """
    def __init__(self, channels: int, num_heads: int = 8):
        super().__init__()
        # 多头注意力层
        self.mha = nn.MultiheadAttention(embed_dim=channels, num_heads=num_heads, batch_first=True)
        # 层归一化
        self.norm1 = nn.LayerNorm(channels)
        self.norm2 = nn.LayerNorm(channels)
        # 前馈网络
        self.ffn = nn.Sequential(
            nn.Linear(channels, channels * 4),
            nn.GELU(),
            nn.Linear(channels * 4, channels)
        )

    def forward(self, x):
        # x: (B, C, L)
        B, C, L = x.shape
        
        # 转换为注意力层的输入格式: (B, L, C)
        x_reshaped = x.transpose(1, 2)
        
        # 自注意力
        attn_out, _ = self.mha(x_reshaped, x_reshaped, x_reshaped)
        x_reshaped = self.norm1(x_reshaped + attn_out)
        
        # 前馈网络
        ffn_out = self.ffn(x_reshaped)
        x_reshaped = self.norm2(x_reshaped + ffn_out)
        
        # 转换回原格式: (B, C, L)
        return x_reshaped.transpose(1, 2)


class FeatureFusionBlock(nn.Module):
    """
    特征融合模块
    智能融合上采样特征与跳跃连接特征
    """
    def __init__(self, channels: int):
        super().__init__()
        self.fusion_conv = nn.Sequential(
            nn.Conv1d(channels, channels // 2, kernel_size=1),
            nn.BatchNorm1d(channels // 2),
            nn.GELU()
        )
        
    def forward(self, upsampled, skip):
        # 拼接特征
        fused = torch.cat([upsampled, skip], dim=1)
        # 融合并降维
        return self.fusion_conv(fused)


class HybridUNetTCN(nn.Module):
    """
    增强版混合U-Net-TCN网络
    主要改进：减少下采样，增强高频细节保持
    """
    def __init__(self,
                 input_freq_channels: int = 3,
                 window_segments: int = 5,
                 time_points_per_segment: int = 400,
                 output_segments: int = 4,
                 space_points_per_segment: int = 200,
                 base_channels: int = 64,
                 depth: int = 4,
                 bottleneck_attn_heads: int = 8,
                 dropout_rate: float = 0.3):
        super().__init__()

        # 保存维度信息
        self.input_length = window_segments * time_points_per_segment
        self.output_length = output_segments * space_points_per_segment
        self.window_segments = window_segments
        self.output_segments = output_segments
        self.time_points_per_segment = time_points_per_segment
        self.space_points_per_segment = space_points_per_segment

        # 1. Input projection: 输入投影层
        self.input_proj = nn.Sequential(
            nn.Conv1d(input_freq_channels, base_channels, kernel_size=7, padding=3, bias=False),
            nn.BatchNorm1d(base_channels),
            nn.GELU()
        )

        # 2. Enhanced Encoder: 减少下采样的编码器
        self.encoder_blocks = nn.ModuleList()
        self.skip_channels = []  # 记录每层的通道数
        ch = base_channels
        
        for i in range(depth):
            # 前两层进行下采样，后两层只增加感受野不下采样
            if i < 2:  # 只在前两层进行下采样，保持更多高频细节
                block = nn.Sequential(
                    ResDilatedBlock(ch, dilation=2**i, dropout_rate=dropout_rate),
                    nn.Conv1d(ch, ch * 2, kernel_size=2, stride=2)  # 下采样
                )
                self.skip_channels.append(ch)
                ch *= 2
            else:  # 后两层使用更大的空洞卷积，不下采样
                block = nn.Sequential(
                    ResDilatedBlock(ch, dilation=2**(i+2), dropout_rate=dropout_rate),  # 增大空洞率
                    nn.Conv1d(ch, ch, kernel_size=1)  # 1x1卷积，不改变尺寸
                )
                self.skip_channels.append(ch)
                # 通道数不变，保持高分辨率
            
            self.encoder_blocks.append(block)

        # 3. Bottleneck: 瓶颈层
        self.bottleneck = nn.Sequential(
            ResDilatedBlock(ch, dilation=2**depth, dropout_rate=dropout_rate),
            SelfAttentionBlock(ch, num_heads=bottleneck_attn_heads)
        )

        # 4. Enhanced Decoder: 增强解码器
        self.decoder_blocks = nn.ModuleList()
        self.fusion_blocks = nn.ModuleList()
        
        for i in reversed(range(depth)):
            if i < 2:  # 对应编码器的下采样层
                # 上采样
                decoder_block = nn.Sequential(
                    nn.ConvTranspose1d(ch, ch // 2, kernel_size=2, stride=2),
                    ResDilatedBlock(ch // 2, dilation=2**i, dropout_rate=dropout_rate)
                )
                # 特征融合
                fusion_block = FeatureFusionBlock(ch)
                ch //= 2
            else:  # 对应编码器的非下采样层
                # 不上采样，只处理特征
                decoder_block = nn.Sequential(
                    ResDilatedBlock(ch, dilation=2**(i+2), dropout_rate=dropout_rate),
                    nn.Conv1d(ch, ch, kernel_size=1)
                )
                # 特征融合
                fusion_block = FeatureFusionBlock(ch * 2)  # 拼接后通道数翻倍
            
            self.decoder_blocks.append(decoder_block)
            self.fusion_blocks.append(fusion_block)

        # 5. Head层 - 时空变换 + Sigmoid约束
        r = self.time_points_per_segment // self.space_points_per_segment
        assert r * self.space_points_per_segment == self.time_points_per_segment, \
               f"time_points_per_segment ({self.time_points_per_segment}) 必须能被 space_points_per_segment ({self.space_points_per_segment}) 整除"

        self.head = nn.Sequential(
            nn.Conv1d(base_channels, base_channels // 2, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm1d(base_channels // 2),
            nn.GELU(),
            nn.Conv1d(base_channels // 2, output_segments, kernel_size=1),
            nn.Sigmoid()  # 约束输出到[0,1]区间
        )

        # 通道适配器（用于处理跳跃连接的通道不匹配）
        self.channel_adapters = nn.ModuleDict()

    def forward(self, x):
        # x: (B, 3, 2000)
        B, C, L = x.shape
        
        # 1. 输入投影
        x = self.input_proj(x)  # (B, 64, 2000)
        
        # 2. 编码器路径 + 保存跳跃连接
        skip_connections = []
        for i, block in enumerate(self.encoder_blocks):
            skip_connections.append(x)
            x = block(x)
        
        # 3. 瓶颈层
        x = self.bottleneck(x)
        
        # 4. 解码器路径 + 特征融合
        for i, (decoder_block, fusion_block) in enumerate(zip(self.decoder_blocks, self.fusion_blocks)):
            x = decoder_block(x)
            
            # 获取对应的跳跃连接
            skip_idx = len(skip_connections) - 1 - i
            skip = skip_connections[skip_idx]
            
            # 处理尺寸不匹配
            if x.shape[-1] != skip.shape[-1]:
                x = F.interpolate(x, size=skip.shape[-1], mode='linear', align_corners=False)
            
            # 特征融合
            x = fusion_block(x, skip)
        
        # 5. Head层 - 时空变换
        x = self.head(x)  # (B, 4, 2000)
        
        # 6. 时空变换：从时间域到空间域
        B, C, L = x.shape
        seg_L = self.time_points_per_segment
        assert L == self.window_segments * seg_L, f"Expected length {self.window_segments*seg_L}, got {L}"
        
        # 重塑为段结构
        x = x.view(B, C, self.window_segments, seg_L)  # (B, 4, 5, 400)
        
        # 提取前4段的对角线
        output_segments = []
        for i in range(self.output_segments):
            segment_data = x[:, i, i+1, :]  # (B, 400)
            # 下采样到空间分辨率
            segment_downsampled = F.avg_pool1d(
                segment_data.unsqueeze(1), 
                kernel_size=self.time_points_per_segment // self.space_points_per_segment
            ).squeeze(1)  # (B, 200)
            output_segments.append(segment_downsampled)
        
        # 堆叠输出
        output = torch.stack(output_segments, dim=1)  # (B, 4, 200)
        
        return output


# -------- 测试代码 --------
if __name__ == '__main__':
    # 测试参数
    W = 5
    T = 400  # 200ns / 0.5ns
    P = 200  # 20m / 0.1m

    # 实例化模型
    model = HybridUNetTCN(
        window_segments=W,
        time_points_per_segment=T,
        output_segments=W - 1,
        space_points_per_segment=P,
        base_channels=64,
        depth=4,
        bottleneck_attn_heads=8
    )

    print("模型架构:")
    print(model)

    # 创建测试输入
    batch_size = 16
    freq_channels = 3
    input_length = W * T
    dummy_input = torch.randn(batch_size, freq_channels, input_length)
    # torch.randn: 创建一个由标准正态分布的随机数填充的张量
    # 对于维度测试，不关心张量里的具体数值，只关心它是否具有正确的形状

    print(f"\n输入形状: {dummy_input.shape}")

    # 前向传播测试
    with torch.no_grad():
        output = model(dummy_input)

    print(f"输出形状: {output.shape}")

    # 期望的输出形状 (新格式：batch, output_segments, space_points_per_segment)
    expected_output_segments = W - 1  # 4
    expected_space_points = P  # 200
    print(f"期望输出形状: ({batch_size}, {expected_output_segments}, {expected_space_points})")

    # 检查输出维度是否匹配
    assert output.shape == (batch_size, expected_output_segments, expected_space_points), "输出维度不匹配！"

    print("\n模型测试成功！输入输出维度匹配。")