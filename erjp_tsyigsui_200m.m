%% 分布式布里渊光纤传感仿真（二阶声场）- 分区计算与相位修复版
clc
clear
close all

% 获取当前系统时间
currentTime = datetime('now', 'Format', 'HH:mm:ss');
% 显示当前时间在命令行中
disp(['程序启动时间: ', char(currentTime)]);

%% ========================================================================
%% 🔥 读取test.m生成的测试样本数据
%% ========================================================================
fprintf('\n正在读取test.m生成的测试样本...\n');

% 检查test.m生成的测试样本文件
test_data_path = '0_dot_5/test/test_sample_0001.mat';  % test.m生成的测试样本
bfs_stats_path = '0_dot_5/bfs_stats.mat';              % 归一化参数文件

% 标志变量：是否使用test.m测试数据
use_test_data = false;

if exist(test_data_path, 'file') && exist(bfs_stats_path, 'file')
    % 读取test.m生成的测试样本
    fprintf('📖 读取test.m测试样本: %s\n', test_data_path);
    try
        % 加载测试样本数据
        loaded_sample = load(test_data_path);
        sample_data = loaded_sample.sample_data;

        % 加载归一化参数
        loaded_stats = load(bfs_stats_path);
        bfs_stats = loaded_stats.stats;
        bfs_min = bfs_stats.min;  % -40 MHz
        bfs_max = bfs_stats.max;  % 50 MHz

        fprintf('✅ 成功读取test.m测试数据\n');
        fprintf('   归一化BFS分布尺寸: %s\n', mat2str(size(sample_data.frequency_shift_distribution_normalized)));
        fprintf('   光纤位置尺寸: %s\n', mat2str(size(sample_data.fiber_positions)));
        fprintf('   归一化范围: [%.1f, %.1f] MHz\n', bfs_min, bfs_max);

        % 获取归一化的BFS分布和位置信息
        bfs_normalized = sample_data.frequency_shift_distribution_normalized;
        fiber_positions = sample_data.fiber_positions;

        % 反归一化BFS分布
        % 反归一化公式: x = x' * (max - min) + min
        bfs_shift_denormalized = bfs_normalized * (bfs_max - bfs_min) + bfs_min;

        fprintf('✅ BFS反归一化完成\n');
        fprintf('   反归一化后频移范围: [%.3f, %.3f] MHz\n', min(bfs_shift_denormalized), max(bfs_shift_denormalized));
        fprintf('   test.m数据长度: %d 点\n', length(bfs_shift_denormalized));
        fprintf('   test.m空间范围: %.1f - %.1f m\n', fiber_positions(1), fiber_positions(end));

        % 🔥 将相对频移转换为绝对布里渊频率
        % test.m生成的是相对于基线的频移量，需要加上基础布里渊频移
        Omega_B_base = 2*pi*10.8e9;   % 基础布里渊频移 (rad/s)

        % 将MHz频移转换为rad/s并加上基础频移
        bfs_absolute = Omega_B_base + 2*pi * bfs_shift_denormalized * 1e6;  % rad/s

        fprintf('🔄 频移转换为绝对布里渊频率\n');
        fprintf('   基础布里渊频移: %.3e rad/s (%.1f GHz)\n', Omega_B_base, Omega_B_base/(2*pi*1e9));
        fprintf('   相对频移范围: [%.1f, %.1f] MHz\n', min(bfs_shift_denormalized), max(bfs_shift_denormalized));
        fprintf('   绝对频率范围: [%.3e, %.3e] rad/s\n', min(bfs_absolute), max(bfs_absolute));

        % 🔥 扩展BFS分布到完整的200m光纤长度，因为test.m代码中生成的数据集光纤长度只有180m
        fiber_length = 200;  % 光纤总长度(m)
        test_fiber_length = fiber_positions(end);  % test.m的光纤长度

        if test_fiber_length < fiber_length
            fprintf('🔧 扩展BFS分布从 %.1f m 到 %.1f m\n', test_fiber_length, fiber_length);

            % 🔥 直接扩展，不使用插值（因为网格步长相同）
            % 计算需要扩展的点数
            spatial_resolution = (fiber_positions(end) - fiber_positions(1)) / (length(fiber_positions) - 1);
            additional_points = round((fiber_length - test_fiber_length) / spatial_resolution);

            % 创建扩展的位置轴
            additional_positions = linspace(test_fiber_length + spatial_resolution, fiber_length, additional_points)';
            z_positions_full = [fiber_positions; additional_positions];

            % 创建扩展的布里渊频率分布
            baseline_omega_B = Omega_B_base;  % 基础布里渊频移 (rad/s)
            additional_bfs = ones(additional_points, 1) * baseline_omega_B;
            bfs_full = [bfs_absolute; additional_bfs];

            fprintf('✅ 布里渊频率分布扩展完成\n');
            fprintf('   扩展后总长度: %d 点\n', length(bfs_full));
            fprintf('   0-%.1fm: 包含频移分布 (基础频移 + 相对频移)\n', test_fiber_length);
            fprintf('   %.1f-%.1fm: 基础布里渊频移 (%.3e rad/s)\n', test_fiber_length, fiber_length, baseline_omega_B);

            % 使用扩展后的数据
            input_bfs_distribution = bfs_full;
            z_positions_test = z_positions_full;
        end

        use_test_data = true;

        fprintf('✅ 将使用test.m测试数据作为传统方法的输入\n');
        fprintf('   数据点数: %d\n', length(input_bfs_distribution));
        fprintf('   空间范围: %.1f - %.1f m\n', z_positions_test(1), z_positions_test(end));

    catch ME
        fprintf('❌ 读取test.m数据失败: %s\n', ME.message);
        fprintf('⚠️  将使用原始仿真数据\n');
        use_test_data = false;
    end
else
    fprintf('⚠️  未找到test.m测试数据文件:\n');
    fprintf('   测试样本: %s\n', test_data_path);
    fprintf('   归一化参数: %s\n', bfs_stats_path);
    fprintf('   请先运行test.m生成测试样本\n');
    fprintf('⚠️  将使用原始仿真数据\n');
    use_test_data = false;
end

fprintf('========================================================================\n');

format long eng; %输出格式为长格式（小数点后15位），并以科学计数法显示
rng('shuffle'); %根据当前时间设置随机数种子

% 获取当前并行池
pool = gcp('nocreate');
% 检查并行池状态
if isempty(pool)
    disp('并行池未开启。尝试启动新的并行池...');
    % 如果没有并行池，启动一个新的并行池
    parpool('IdleTimeout', 120); % 设置空闲超时时间为 120 分钟
    disp('新的并行池已启动。');
else
    % 如果已存在并行池，检查其状态
    disp('并行池已开启。');
    disp(['并行池大小: ', num2str(pool.NumWorkers), ' 个工作者']);
    disp(['当前空闲超时时间: ', num2str(pool.IdleTimeout), ' 分钟']);
    
    % 确保其空闲超时时间为 120 分钟
    if pool.IdleTimeout ~= 120
        pool.IdleTimeout = 120;
        disp('空闲超时时间已重新设置为 120 分钟。');
    end
end

tic %计时器，开始计时

%% 控制开关
% 损耗项
Loss_item = 1;

% 动画开关
anim_active = 0;      % 1 = produce animation, 0 = skip animation
title1 = 'SBS_solver_animation_noise.mp4';   % Animation title

%% 参数设置
% 物理常数
c = 3e8;                      % 光速 (m/s)
n = 1.5;                      % 光纤折射率
epsilon0 = 8.85e-12;          % 真空介电常数 (F/m)
kB = 1.38e-23;                % 玻尔兹曼常数
Temperature = 300;            % 波导温度 (K)
va = 5960;                    % 光纤中的声速 (m/s)
vc = c/n;                     % 光纤中的光速
nf = 1.5;                     % 光纤模式的模态指数
eta = c*epsilon0*nf/2;

% 光纤参数
L = 200;                      % 光纤总长度 (m)
Fiber_Aeff = 50e-12;          % 有效模场面积 (m^2)

% 是否有损耗
switch Loss_item
    case 0
        loss = 0;
        alpha = 0;
    case 1 
        loss = 0.2;           % 1550nm波长在光纤中的损耗 (dB/km)
        alpha_power_per_m = loss * log(10) / (10 * 1000); % 功率衰减系数，单位：Np/m
        alpha = alpha_power_per_m / 2; % 幅度衰减系数，单位：Np/m
end

% 仿真参数
SBS_freq_max = 1e9;             % 最大响应频率 [Hz]
t_max = 2.5*n*L/c;              % 最大时间 - 基于全长计算
Delta_freq = 1/t_max;
Delta_t = 1/(2*SBS_freq_max);
f_2 = -SBS_freq_max:Delta_freq:SBS_freq_max;  % 正负频率轴
f_ang_2 = 2*pi*f_2; % 正负角频率轴
Nf = length(f_ang_2);
t = 0:Delta_t:t_max; 
Nt = length(t);

% 检查步长
if (1/Delta_freq)-(1/SBS_freq_max)<(n*L/c)  %传输整根光纤所需的时间
    error('invalid step size combination')
end

dz = c*Delta_t/n;
z_full = 0:dz:L;  % 全光纤空间网格
Nz_full = length(z_full);

% 分区计算参数 - 用于解决内存问题
partition_length = 100;  % 每个计算分区的长度 (m)
num_partitions = ceil(L / partition_length);  % 计算需要的分区数量
points_per_partition = round(partition_length / dz);  % 每个分区的空间点数

disp(['光纤长度: ', num2str(L), ' m']);
disp(['计算分区长度: ', num2str(partition_length), ' m']);
disp(['计算分区数量: ', num2str(num_partitions)]);
disp(['每个分区空间点数: ', num2str(points_per_partition)]);
disp(['总空间点数: ', num2str(Nz_full)]);
disp(['总时间点数: ', num2str(Nt)]);

% 布里渊参数
tao_a = 10e-9;                % 声子寿命 (s)
Gamma = 1/tao_a;              % 再除以pi后，才是布里渊线宽35MHz (Hz)
gamma = Gamma/2;
strain_coef = 0.0483e6;       % 应变系数 (Hz/μϵ)
gB = 5e-11;                   % 布里渊峰值增益系数 (m/W)
g0 = gB/Fiber_Aeff;
Omega_B_base = 2*pi*10.8e9;   % 基础布里渊频移 (rad/s)

% 定义多个应变区域及其对应的频移值
% 每一行格式: [起始位置(m), 结束位置(m), 频移值(MHz)]
strain_regions = [
    30, 35, 10;     % 第一个应变区域
    50, 60, 15;     % 第二个应变区域
    75, 80, 20;     % 第三个应变区域
];

% 定义布里渊频移分布 - 整个光纤
Omega_B_full = ones(1, Nz_full) * Omega_B_base;

% 遍历每个应变区域并应用相应的频移
for i = 1:size(strain_regions, 1)
    region_start = strain_regions(i, 1);
    region_end = strain_regions(i, 2);
    region_shift = strain_regions(i, 3);
    
    % 找到该应变区域对应的索引
    region_indices = find(z_full >= region_start & z_full <= region_end);
    
    % 应用频移
    Omega_B_full(region_indices) = Omega_B_base + 2*pi*region_shift * 1e6;
end

% 脉冲参数 - 泵浦光
Pump_peak_P_0 = 40e-3;           % 泵浦光峰值功率 (W)
Pump_peak_E_0 = sqrt(Pump_peak_P_0); % 泵浦光峰值振幅
Pump_ER = 20;                 % 泵浦光消光比 (dB)
Pump_leak_P_0 = 0;            % 泵浦光泄露功率 (W)
Pump_leak_E_0 = sqrt(Pump_leak_P_0); % 泵浦光泄露振幅
Pump_rise_time = 0.1e-9;      % 泵浦光上升时间 (s)
Pump_a1 = 0.45*Pump_rise_time;
Pump_fall_time = 0.1e-9;      % 泵浦光下降时间 (s)
Pump_a2 = 0.45*Pump_fall_time;
Pump_width = 50e-9;           % 泵浦光脉宽 (s)

% 脉冲参数 - 探测光
Probe_peak_P_L = 1e-3;         % 探测光峰值功率 (W)
Probe_peak_E_L = sqrt(Probe_peak_P_L); % 探测光峰值振幅
Probe_ER = 20;                % 探测光消光比 (dB)
Probe_leak_P_L = 0;           % 探测光泄露功率 (W)
Probe_leak_E_L = sqrt(Probe_leak_P_L); % 探测光泄露振幅
Probe_rise_time = 0.1e-9;     % 探测光上升时间 (s)
Probe_a1 = 0.45*Probe_rise_time;
Probe_fall_time = 0.1e-9;     % 探测光下降时间 (s)
Probe_a2 = 0.45*Probe_fall_time;
Probe_width = 200e-9;          % 探测光脉宽 (s)

% 计算脉冲对应的空间长度
pump_length = vc * Pump_width;    % 泵浦脉冲空间长度 (m)
probe_length = vc * Probe_width;  % 探测脉冲空间长度 (m)
spatial_resolution = pump_length / 2;  % 空间分辨率 (m)

% 扫频参数
Omega_start = 2*pi*10.73e9;        % 起始扫频点 (rad/s)
Omega_end = 2*pi*10.87e9;          % 结束扫频点 (rad/s)
Omega_step = 2*pi*5e6;             % 扫频步长 (rad/s)
Omega_list = Omega_start:Omega_step:Omega_end;  % 扫频列表
n_sweep = length(Omega_list);

%% 生成入射脉冲形状（一次性生成）
t1_p_lin = (t - 0)/Pump_a1;                        % 泵浦脉冲固定 0 ns 入射
t2_p_lin = (t - 0 - Pump_width)/Pump_a2;
Pump_lin = (Pump_peak_E_0-Pump_leak_E_0).*sqrt((tanh(t1_p_lin)-tanh(t2_p_lin))/2)+Pump_leak_E_0;

t1_s_lin = (t - 0)/Probe_a1;                       % 探测脉冲固定 0 ns 入射
t2_s_lin = (t - 0 - Probe_width)/Probe_a2;
Probe_lin = (Probe_peak_E_L-Probe_leak_E_L).*sqrt((tanh(t1_s_lin)-tanh(t2_s_lin))/2)+Probe_leak_E_L;

%% 多段扫描设置
% 定义分段扫描参数
segment_length = probe_length / 2; % 每段长度为探测脉冲长度的一半（约20m）
num_segments = floor(L / segment_length); % 总段数

% 存储不同段的结果
all_probe_map = cell(1, num_segments);
all_stokes_map = cell(1, num_segments);
all_BGS_results = cell(1, num_segments);
all_BPS_results = cell(1, num_segments);
all_BPGR_results = cell(1, num_segments);  % 新增，保存相位增益比
all_window_positions = cell(1, num_segments);
all_BFS_distribution = cell(1, num_segments);
all_A2_complex = cell(1, num_segments);    % 新增，保存复振幅

% 存储整个光纤的BGS/BPS/BFS数据
full_fiber_positions = [];
full_fiber_BFS = [];
all_segment_3D_stokes = cell(1, num_segments);
all_segment_3D_acoustic = cell(1, num_segments);

% 定义段的范围(m)
segment_ranges = zeros(num_segments, 2);
for s = 1:num_segments
    segment_ranges(s, :) = [(s-1)*segment_length, s*segment_length];
end

% 创建主进度条
h_main = waitbar(0, '分段计算准备...', 'Name', '布里渊光纤传感仿真');

% 预计算扫频频率表
EXP_OM_TABLE = exp(1i * (Omega_list.') .* t);   % n_sweep × Nt 的复表

% 创建临时目录用于存储结果
temp_dir = fullfile(pwd, 'temp_results');
if ~exist(temp_dir, 'dir')
    mkdir(temp_dir);
end

%% 按100m分区计算所有段，即20m为一段进行计算
% 外循环遍历计算分区
for partition_idx = 1:num_partitions
    % 计算当前分区的空间范围
    partition_start = (partition_idx - 1) * partition_length;
    partition_end = min(partition_idx * partition_length, L);

    part_left  = partition_idx - 1;        % 测量当前分区时，泵浦脉冲已走过的分区数量
    part_right = num_partitions - partition_idx;  % 测量当前分区时，探测脉冲已走过的分区数量
    
    % 找出位于当前分区内的测量段编号，第一个分区的话，编号是1,2,3,4,5；第二个分区的话，编号是6,7,8,9,10
    segments_in_partition = find(segment_ranges(:,1) >= partition_start & ...
                                segment_ranges(:,2) <= partition_end);
    
    if isempty(segments_in_partition)
        continue; % 如果当前分区内没有测量段起点，跳过
    end
    
    disp('============================================');
    disp(['处理计算分区 ', num2str(partition_idx), '/', num2str(num_partitions), ...
          ': ', num2str(partition_start), 'm - ', num2str(partition_end), 'm']);
    disp(['该分区包含 ', num2str(length(segments_in_partition)), ' 个测量段']);
    
    % 当前分区对应的z索引范围
    z_start_idx = max(1, round(partition_start / dz) + 1);
    z_end_idx = min(Nz_full, round(partition_end / dz) + 1);
    
    % 当前分区的空间网格
    current_z = z_full(z_start_idx:z_end_idx); % 当前段的空间距离，全局，0-100m，100-200m
    current_Nz = length(current_z); % 当前段的空间点数，局部，每个段都是1001
    
    % 🔥 提取当前分区的Omega_B（使用test.m的布里渊频率分布）
    if use_test_data
        current_Omega_B = input_bfs_distribution(z_start_idx:z_end_idx);
    else
        current_Omega_B = Omega_B_full(z_start_idx:z_end_idx);
    end
    
    % 创建分区进度条
    h_segments = waitbar(0, '段计算准备...', 'Name', sprintf('分区 %d 测量段计算', partition_idx));
    
    % 遍历当前分区内的所有测量段
    for seg_idx = 1:length(segments_in_partition) % 循环每一段，即20m为一段
        % 获取当前测量段的全局索引
        segment_idx = segments_in_partition(seg_idx); % 每个区里面有五个段，确定是第几段，这个段数量是全局的，即第一个分区里是1,2,3,4,5；第二个分区是6,7,8,9,10
        
        % 更新段进度条
        waitbar((seg_idx-1)/length(segments_in_partition), h_segments, ...
            sprintf('段 %d/%d (全局 %d/%d)', seg_idx, length(segments_in_partition), ...
                   segment_idx, num_segments));
        
        % 确定当前段的范围
        segment_start = segment_ranges(segment_idx, 1);
        segment_end = segment_ranges(segment_idx, 2);
        
        disp('----------------------------------------');
        disp(['处理测量段 ', num2str(segment_idx), '/', num2str(num_segments), ': ', ...
              num2str(segment_start), 'm - ', num2str(segment_end), 'm']);
        
        % === 这里保留您指定的脉冲入射时间计算逻辑 ===
        % 计算脉冲入射时间
        meeting_position = segment_start + pump_length / 2; % 脉冲前沿相遇位置
        
        % 脉冲后沿分离的位置
        separation_position = meeting_position + (probe_length - pump_length)/2;
        separation_position = min(separation_position, L);   % 仍保留边界保护
        
        % 计算泵浦和探测的入射延迟，全局延迟，并没有说是一个段内，然后检测不同20m段的延迟，所以得到的Pump_t和Probe_t也是全局的
        if meeting_position <= L/2
            % 相遇点在前半段，泵浦延迟入射
            t0_p_global = (L - 2*meeting_position) / vc; % 泵浦延迟时间
            t0_s_global = 0; % 探测立即入射
        else
            % 相遇点在后半段，探测延迟入射
            t0_p_global = 0; % 泵浦立即入射
            t0_s_global = (2*meeting_position - L) / vc; % 探测延迟时间
        end
        
        disp(['泵浦入射延迟: ', num2str(t0_p_global*1e9), ' ns']);
        disp(['探测入射延迟: ', num2str(t0_s_global*1e9), ' ns']);
        disp(['预期相遇位置: ', num2str(meeting_position), ' m']);
        disp(['预期分离位置: ', num2str(separation_position), ' m']);
        
        % 计算脉冲相遇和分离的时间索引，全局时间索引
        t_meet     = (L/vc + t0_p_global + t0_s_global)/2; % 两脉冲前沿相遇时间
        t_separate = t_meet + (Pump_width + Probe_width)/2; % 两脉冲后沿分离时间
        
        [~, idx_meet] = min(abs(t - t_meet));       % 相遇时间索引
        [~, idx_separate] = min(abs(t - t_separate)); % 分离时间索引
        
        disp(['脉冲相遇时间: ', num2str(t_meet*1e9), ' ns (索引: ', num2str(idx_meet), ')']);
        disp(['脉冲分离时间: ', num2str(t_separate*1e9), ' ns (索引: ', num2str(idx_separate), ')']);
        
        % -------- ★ 由于现在是分区进行计算的，所以下面计算的时候，实际上是假设光纤长度只有一个区的光纤长度，即100m，计算的时候不会考虑经过其它区的延迟，所以在这里加上 ★ --------
        extra_t_p = part_left  * partition_length / vc;      % 测量当前区时，泵浦脉冲已走过的区的数量*区的长度/vc，即延迟
        extra_t_s = part_right * partition_length / vc;      % 测量当前区时，探测脉冲已走过的区的数量*区的长度/vc，即延迟
        t0_p_local = t0_p_global + extra_t_p;
        t0_s_local = t0_s_global + extra_t_s;

        loss_p = exp( -alpha * part_left  * partition_length ); % 泵浦脉冲已走过的区所经历的衰减
        loss_s = exp( -alpha * part_right * partition_length ); % 探测脉冲已走过的区所经历的衰减

        % 使用时移获取泵浦和探测脉冲   这是从当前区来看的时候，泵浦和探测时域图，如果是要看实际泵浦和探测时域图、从整根光纤来看的话，那么就不能加上extra_t_p和extra_t_s，就应该用前面算出来的t0_p和t0_s
        % 并且还考虑了从其它区传到当前区的时候所经历的衰减
        Pump_t = circshift(Pump_lin * loss_p, round(t0_p_local/Delta_t));
        Probe_t = circshift(Probe_lin * loss_s, round(t0_s_local/Delta_t));
        
        % 检查当前相遇点是否在当前分区内
        is_meeting_in_partition = (meeting_position >= partition_start && meeting_position < partition_end);
        
        if ~is_meeting_in_partition
            disp(['警告: 相遇点 ', num2str(meeting_position), 'm 不在当前计算分区内']);
            % 这种情况下，需要扩展计算分区或移动到正确的分区
            continue;
        end
        
        % 初始化结果存储
        probe_map_SBS = zeros(Nt, n_sweep);
        stokes_map_SBS = zeros(Nt, n_sweep);
        A2_complex = complex(zeros(Nt, n_sweep));  % 新增：存储复振幅
        q_meeting_point = zeros(Nt, n_sweep);
        
        % 循环计算各频率点（可改用parfor）
        for freq_idx = 1:n_sweep  % 认为光纤长度是分区段的长度，即100m，但时间还是全局的，即入射延迟时间、泵浦探测脉冲也是用的全局时域结果、但只完成100m中的20m长度的扫频，因为下面只在相遇时间段内计算SBS耦合
            Omega = Omega_list(freq_idx);
            fprintf('段 %d/%d, 扫频点 %d/%d: Omega = %.3f GHz\n', ...
                   segment_idx, num_segments, freq_idx, n_sweep, Omega/(2*pi)/1e9);
            
            % === 为当前段的当前频率计算声场响应函数 ===
            H_tab = zeros(current_Nz, Nt);
            for j = 1:current_Nz
                Omega_eff = sqrt(current_Omega_B(j)^2 - gamma^2);
                H_tab(j,:) = sqrt(2*pi)/Omega_eff .* exp(-gamma*t).*sin(Omega_eff*t);
            end
            H_eff = H_tab .* EXP_OM_TABLE(freq_idx,:);
            
            % === 为当前测量段计算传播 ===
            % 初始化场矩阵   局部空间、全局时间
            A1_store = zeros(current_Nz, Nt);
            A2_store = zeros(current_Nz, Nt);
            q_store = zeros(current_Nz, Nt);
            
            % 设置入射脉冲 - 边界条件   
            % 注意这里的Pump_t和Probe_t中的延迟时间由两部分组成，一部分是全局的，一部分是从光纤端点传播到当前分区所经历的延迟，因为下面仅仅考虑的是当前分区，传播到当前分区还有延时
            A1_store(1,:) = Pump_t;
            A2_store(end,:) = Probe_t;
            
            % 进行线性+非线性传播计算
            for i = 1:Nt-1
                % 线性传播
                A1_store(2:end,i+1) = A1_store(1:end-1,i) * exp(-alpha*dz);
                A2_store(1:end-1,i+1) = A2_store(2:end,i) * exp(-alpha*dz);
                
                % 恢复边界条件
                A1_store(1,i+1) = Pump_t(i+1);
                A2_store(end,i+1) = Probe_t(i+1);
                
                % 只在相遇时间段内计算SBS耦合
                if i >= idx_meet && i < idx_separate
                    % 计算声场
                    X = A1_store(:,1:i).*conj(A2_store(:,1:i));
                    rho = Delta_t*sum(H_eff(:,1:i).*fliplr(X),2);
                    q = -1i*g0*gamma*current_Omega_B(:).*rho;
                    
                    % 计算SBS耦合
                    A1_store(:,i+1) = A1_store(:,i+1) - g0/2*vc*A2_store(:,i+1).*q*Delta_t;
                    A2_store(:,i+1) = A2_store(:,i+1) + g0/2*vc*A1_store(:,i+1).*conj(q)*Delta_t;
                    q_store(:,i+1) = q;
                    
                    % 恢复边界条件
                    A1_store(1,i+1) = Pump_t(i+1);
                    A2_store(end,i+1) = Probe_t(i+1);
                end
            end
            
            % 临时结果变量   全局时间
            temp_probe = zeros(1, Nt);
            temp_stokes = zeros(1, Nt);
            temp_A2 = complex(zeros(1, Nt));  % 存储复振幅
            temp_q = zeros(1, Nt);
 
            A2_at_receiver = A2_store(1, :); % 提取的是当前分区左端点探测光随时间的变化
            % 1) 本分区左端到全局 z = 0 的距离
            z_offset = partition_start; % [m]
            % 2) 对应传播时间（采样点）和损耗
            time_delay_pts = round( (z_offset / vc) / Delta_t );   % 需要向左移动回z = 0的时间点数
            loss_extra     = exp( -alpha * z_offset );             % 额外衰减

            A2_at_receiver = circshift( A2_at_receiver * loss_extra ,  time_delay_pts ); % 在z=0处得到探测光的值

            
            % 计算线性传播的探测光场用于比较
            A2_linear = zeros(current_Nz, Nt);
            A2_linear(end,:) = Probe_t;
            
            for i = 1:Nt-1
                A2_linear(1:end-1,i+1) = A2_linear(2:end,i) * exp(-alpha*dz);
                A2_linear(end,i+1) = Probe_t(i+1);
            end
            
            A2_linear_at_receiver = A2_linear(1, :); % 这个得到的并不是在z=0处线性传播的探测光随时间的变化，而是在当前分区的开始光纤点，线性传播的探测光随时间的变化
            % 例如，0-100m分区，就是0m处线性传播的探测光随时间的变化；100-200m分区，就是100m处线性传播的探测光随时间的变化

            A2_linear_at_receiver = circshift( A2_linear_at_receiver * loss_extra ,  time_delay_pts ); 
            % 同样，得到的这个线性传播的探测光也同样仅仅是在当前分区这一段空间长度上、完整时间Nt的线性传播演化
            % 但是由于Probe_t我已经加上了经过右边分区时的延迟和损耗，所以现在只需要把得到的A2_linear_at_receiver这个再线性传播回z=0即可
            
            % 保存复振幅到临时变量
            temp_A2 = A2_at_receiver; % 直接保存复振幅，不取abs
            
            % 计算功率
            power_time = abs(A2_at_receiver).^2;
            power_time_linear = abs(A2_linear_at_receiver).^2;
            
            % 保存到临时变量
            temp_probe = power_time;
            temp_stokes = power_time - power_time_linear;

            
            % 提取相遇点处的声场
            [~, meet_idx_in_partition] = min(abs(current_z - meeting_position));
            temp_q = q_store(meet_idx_in_partition, :);
            
            % 保存临时结果到文件
            save(fullfile(temp_dir, sprintf('temp_results_%d_%d.mat', segment_idx, freq_idx)), ...
                 'temp_A2', 'temp_probe', 'temp_stokes', 'temp_q');  % 添加保存复振幅
        end
        
        % 在循环外收集结果
        for freq_idx = 1:n_sweep
            result_file = fullfile(temp_dir, sprintf('temp_results_%d_%d.mat', segment_idx, freq_idx)); % 读取for freq_idx = 1:n_sweep保存的文件
            
            if exist(result_file, 'file')
                result_data = load(result_file); % 加载临时保存的文件
                probe_map_SBS(:, freq_idx) = result_data.temp_probe; 

                % 把临时保存的变量结果放到全局时间的变量中
                stokes_map_SBS(:, freq_idx) = result_data.temp_stokes;
                A2_complex(:, freq_idx) = result_data.temp_A2;
                q_meeting_point(:, freq_idx) = result_data.temp_q;
                
                % 删除临时文件
                delete(result_file); % 一个个删除不同n_sweep下保存的20m段的结果文件
            end
        end

        % 以上完成当前区下某个20m光纤长度的测量，下面仍然是处理这20m的绘图结果
        
        %% BGS提取与可视化
        % 计算两脉冲前沿相遇的空间位置（验证）
        pump_position = @(t) vc * (t - t0_p_global); % 泵浦脉冲在时间t的位置
        probe_position = @(t) L - vc * (t - t0_s_global); % 探测脉冲在时间t的位置
        
        % 求解相遇时间和位置
        syms t_meet_sym
        meet_eq = pump_position(t_meet_sym) == probe_position(t_meet_sym);
        t_meet_sol = double(solve(meet_eq, t_meet_sym));
        meeting_point_actual = pump_position(t_meet_sol);
        
        % 计算探测光脉冲前沿到达z=0的时间
        t_probe_arrive = t0_s_global + n*L/c;  % 探测光入射时间 + 光纤传播时间
        
        % 计算需要提取的窗口数量
        num_windows = round(probe_length / pump_length);
        
        % 计算时间窗口
        window_time = Pump_width;  % 时间窗口等于泵浦脉冲宽度
        time_window = round(window_time / Delta_t);  % 时间窗口长度(点数)
        
        % 计算探测光脉冲前沿到达z=0的时间索引
        [~, idx_arrive] = min(abs(t - t_probe_arrive));  % 到达时间索引
        
        % 创建存储所有BGS的矩阵
        all_BGS = zeros(num_windows, n_sweep);
        all_BPS = zeros(num_windows, n_sweep);
        all_BPGR = zeros(num_windows, n_sweep);  % 布里渊相位增益比
        
        % 定义窗口对应的光纤位置
        window_positions = zeros(num_windows, 2);  % 存储每个窗口的[起始,结束]位置
        for w = 1:num_windows
            if w == 1
                % 第一个窗口: [相遇位置-空间分辨率, 相遇位置]
                window_positions(w, :) = [meeting_point_actual - spatial_resolution, meeting_point_actual];
            else
                % 其他窗口: [相遇位置+(w-2)*空间分辨率, 相遇位置+(w-1)*空间分辨率]
                window_positions(w, :) = [meeting_point_actual + (w-2)*spatial_resolution, meeting_point_actual + (w-1)*spatial_resolution];
            end
        end
        
        % 提取所有时间窗口的BGS和BPS
        for window = 1:num_windows
            % 计算当前窗口的起始和结束索引
            start_idx = idx_arrive + (window-1) * time_window;
            end_idx = min(start_idx + time_window, Nt);
            
            % 确保索引不越界
            if start_idx >= Nt || end_idx > Nt
                disp(['窗口', num2str(window), '超出数据范围，跳过']);
                continue;
            end
            
            % 提取时间窗口内的数据并计算平均功率(BGS)和平均相位(BPS)
            for freq_idx = 1:n_sweep
                % 提取增益数据 (BGS)
                window_data = stokes_map_SBS(start_idx:end_idx, freq_idx);
                all_BGS(window, freq_idx) = mean(window_data);
                
                % 提取相位数据 (BPS)
                A2_sig = A2_complex(start_idx:end_idx, freq_idx);

                % 计算相位变化 - 使用复振幅计算相位差
                phase_diff = angle(mean(A2_sig) );
                all_BPS(window, freq_idx) = phase_diff;
                
                % 计算布里渊相位增益比(BPGR) - 避免除以0的情况
                gain_val = abs(all_BGS(window, freq_idx));
                if gain_val > 1e-12
                    all_BPGR(window, freq_idx) = phase_diff / gain_val;
                else
                    all_BPGR(window, freq_idx) = 0;
                end
            end
            
            % 找到BGS峰值频率
            [~, max_idx] = max(all_BGS(window, :));
            peak_frequency = (Omega_list(max_idx)/(2*pi)-10.8e9)/1e6;
            
            disp(['窗口', num2str(window), ' - 位置: ', num2str(window_positions(window, 1)), 'm - ', ...
                  num2str(window_positions(window, 2)), 'm, 峰值频移: ', num2str(peak_frequency, '%.2f'), ' MHz']);
        end
        
        % 计算每个窗口的BFS
        BFS_distribution = zeros(num_windows, 1);
        for window = 1:num_windows
            [~, max_idx] = max(all_BGS(window, :));
            BFS_distribution(window) = (Omega_list(max_idx)/(2*pi)-10.8e9)/1e6;
        end
        
        % 存储当前段，即某个分区里面某个20m段的结果
        % segment_idx是全局的，200m光纤，假如分区长度为100m，然后100m里面分段为20m进行测量，那么一共10段，segment_idx就等于1,2,3...10
        all_probe_map{segment_idx} = probe_map_SBS;
        all_stokes_map{segment_idx} = stokes_map_SBS;
        all_BGS_results{segment_idx} = all_BGS;
        all_BPS_results{segment_idx} = all_BPS;
        all_BPGR_results{segment_idx} = all_BPGR;
        all_window_positions{segment_idx} = window_positions;
        all_BFS_distribution{segment_idx} = BFS_distribution;
        all_A2_complex{segment_idx} = A2_complex;  % 存储复振幅
        
        % 收集用于3D图的数据
        all_segment_3D_stokes{segment_idx} = stokes_map_SBS;
        all_segment_3D_acoustic{segment_idx} = q_meeting_point;
        
        % 更新全光纤BFS数据
        full_fiber_positions = [full_fiber_positions; mean(window_positions, 2)];
        full_fiber_BFS = [full_fiber_BFS; BFS_distribution];
        
        % 更新段进度条
        waitbar(seg_idx/length(segments_in_partition), h_segments, ...
            sprintf('段 %d/%d 完成 (全局 %d/%d)', seg_idx, length(segments_in_partition), ...
                   segment_idx, num_segments));
                   
        % 定期保存结果
        if mod(segment_idx, 20) == 0 || segment_idx == num_segments
            save(fullfile(pwd, sprintf('results_checkpoint_seg_%d.mat', segment_idx)), ...
                'all_BGS_results', 'all_BPS_results', 'all_window_positions', ...
                'all_BFS_distribution', 'full_fiber_positions', 'full_fiber_BFS', '-v7.3');
        end
    end  % 循环完所有光纤段20m，即完成整根光纤的扫描
    
    % 关闭段进度条
    close(h_segments);
    
    % 更新主进度条
    waitbar(partition_idx/num_partitions, h_main, ...
        sprintf('完成计算分区 %d/%d (%.1f%%)', partition_idx, num_partitions, ...
               partition_idx/num_partitions*100));
end

% 关闭主进度条
close(h_main);

% 清理临时文件夹
if exist(temp_dir, 'dir')
    rmdir(temp_dir, 's');
end

delete(gcp('nocreate')); % 显式关闭并行池
toc

%% 绘制可视化图形 - 增强版

% 1. 全光纤布里渊频移分布
figure(10);
hold on;

% 对收集的所有段的BFS数据进行排序（原始逻辑）
[sorted_positions, sort_idx] = sort(full_fiber_positions);
sorted_BFS = full_fiber_BFS(sort_idx);
fprintf('⚠️  使用传统方法计算的BFS分布\n');

% 绘制测量结果
plot(sorted_positions, sorted_BFS, 'b-o', 'LineWidth', 2, 'MarkerFaceColor', 'b');

% 🔥 添加理论频移分布（基于test.m的input_bfs_distribution）
if use_test_data
    % 使用test.m的布里渊频率分布计算理论频移
    fiber_pos_theory = z_positions_test;
    % 将绝对布里渊频率转换为相对频移：(Ω - Ω_base) / (2π × 1e6)
    theoretical_shift = (input_bfs_distribution - Omega_B_base) / (2*pi * 1e6);

    % 绘制理论频移分布曲线
    plot(fiber_pos_theory, theoretical_shift, 'r--', 'LineWidth', 2);
else
    % 原始的理论频移分布
    fiber_pos_theory = 0:0.1:L;
    theoretical_shift = zeros(size(fiber_pos_theory));

    % 遍历每个应变区域，设置对应的理论频移值
    for i = 1:size(strain_regions, 1)
        region_start = strain_regions(i, 1);
        region_end = strain_regions(i, 2);
        region_shift = strain_regions(i, 3);

        % 找到当前应变区域对应的位置索引
        region_indices = (fiber_pos_theory >= region_start) & (fiber_pos_theory <= region_end);

        % 设置对应区域的理论频移值
        theoretical_shift(region_indices) = region_shift;
    end

    % 绘制理论频移分布曲线
    plot(fiber_pos_theory, theoretical_shift, 'r--', 'LineWidth', 2);
end

% 添加段分隔线
for s = 1:num_segments-1
    segment_boundary = s * segment_length;
    line([segment_boundary segment_boundary], ylim, 'Color', 'k', 'LineStyle', '--');
end

xlabel('光纤位置 (m)', 'FontSize', 14);
ylabel('布里渊频移 (MHz)', 'FontSize', 14);
title('全光纤布里渊频移分布', 'FontSize', 14);
legend('测量结果', '理论值', 'FontSize', 12);
grid on;
xlim([0 L]);
hold off;

% 保存图形
saveas(gcf, 'BFS_distribution.fig');
saveas(gcf, 'BFS_distribution.png');

%% ========================================================================
%% 🔥 保存MATLAB传统方法的BFS测量结果
%% ========================================================================
fprintf('\n正在保存MATLAB传统方法的BFS测量结果...\n');

% 确保results目录存在
if ~exist('results', 'dir')
    mkdir('results');
    fprintf('📁 创建results目录\n');
end

% 保存测量结果（不包括理论结果）
save_path_mat = 'results/matlab_bfs_measurement.mat';
save(save_path_mat, 'sorted_BFS', 'sorted_positions', '-v7.3');
fprintf('✅ MATLAB BFS测量结果已保存为MAT格式: %s\n', save_path_mat);

% 同时保存为CSV格式，方便Python读取
save_path_csv = 'results/matlab_bfs_measurement.csv';
measurement_data = [sorted_positions(:), sorted_BFS(:)];
writematrix(measurement_data, save_path_csv);
fprintf('✅ MATLAB BFS测量结果已保存为CSV格式: %s\n', save_path_csv);

% 显示保存的数据统计信息
fprintf('\n📊 保存的数据统计:\n');
fprintf('   - 空间点数: %d\n', length(sorted_positions));
fprintf('   - 空间范围: [%.1f, %.1f] m\n', min(sorted_positions), max(sorted_positions));
fprintf('   - BFS范围: [%.3f, %.3f] MHz\n', min(sorted_BFS), max(sorted_BFS));
fprintf('   - 平均BFS: %.3f MHz\n', mean(sorted_BFS));
fprintf('   - BFS标准差: %.3f MHz\n', std(sorted_BFS));

if use_test_data
    fprintf('\n🔍 数据来源: test.m生成的测试样本 (0.5m频移分布)\n');
    fprintf('   这是传统MATLAB方法对test.m测试样本的处理结果\n');
    fprintf('   原始数据: 归一化相对频移 (0-%.1fm)\n', test_fiber_length);
    fprintf('   处理步骤:\n');
    fprintf('     1. 反归一化得到相对频移 (MHz)\n');
    fprintf('     2. 转换为绝对布里渊频率 (Ω_B = Ω_B_base + 2π×频移×1e6)\n');
    fprintf('     3. 扩展到200m (180-200m为基础频移)\n');
    fprintf('   0-%.1fm: 基础频移 + 相对频移\n', test_fiber_length);
    fprintf('   %.1f-200m: 仅基础频移 (2π×10.8GHz)\n', test_fiber_length);
else
    fprintf('\n🔍 数据来源: 原始MATLAB仿真计算\n');
end

fprintf('========================================================================\n');

% 2. 布里渊增益谱的3D曲线图 - 使用归一化增益作为z轴
figure(1);
hold on;

% 获取频率轴
freq_grid = (Omega_list/(2*pi)-10.8e9)/1e6; % MHz

% 创建插值用的更密集频率轴
freq_interp = linspace(min(freq_grid), max(freq_grid), 100);

% 创建坐标轴
ax = gca;
ax.YDir = 'normal';
view(30, 30);

% 绘制BGS三维曲线
for s = 1:num_segments
    % 获取当前段的窗口位置和BGS数据
    if ~isempty(all_window_positions{s}) && ~isempty(all_BGS_results{s})
        segment_positions = mean(all_window_positions{s}, 2);
        segment_bgs = all_BGS_results{s};
        
        % 对于每个窗口
        for w = 1:size(segment_positions, 1)
            % 获取当前位置
            pos = segment_positions(w);
            
            % 获取BGS数据并归一化
            bgs_data = segment_bgs(w, :);
            max_val = max(abs(bgs_data));
            if max_val > 0
                bgs_norm = bgs_data / max_val;
            else
                bgs_norm = bgs_data;
            end
            
            % 使用样条插值平滑曲线
            bgs_interp = spline(freq_grid, bgs_norm, freq_interp);
            
            % 绘制3D平滑曲线 - 直接使用增益作为Z轴
            plot3(freq_interp, ones(size(freq_interp))*pos, bgs_interp, 'LineWidth', 1.6, 'Color', indexToColor(w));
        end
    end
end

% 设置图表属性
xlabel('频率偏移 (MHz)', 'FontSize', 14);
ylabel('光纤位置 (m)', 'FontSize', 14);
zlabel('归一化增益', 'FontSize', 14);
title('全光纤布里渊增益谱（BGS）曲线', 'FontSize', 14);
grid on;
xlim([min(freq_grid), max(freq_grid)]);
ylim([0, L]);

% 添加段分隔线
for s = 1:num_segments-1
    segment_boundary = s * segment_length;
    line([min(freq_grid), max(freq_grid)], [segment_boundary, segment_boundary], [0, 0], 'Color', 'k', 'LineStyle', '--');
end

hold off;

% 保存图形
saveas(gcf, 'BGS_3D_curves.fig');
saveas(gcf, 'BGS_3D_curves.png');

% 3. 声场分布3D图 (仅显示开始、中间和结束频率点)
figure(2);
hold on;

% 选择要展示的三个代表性频率点 (开始、中间、结束)
freq_indices = [1, round(n_sweep/2), n_sweep];

% 存储图例句柄
h_legend_acoustic = zeros(1, length(freq_indices));

% 创建频率标签
freq_labels = cell(1, length(freq_indices));
for idx = 1:length(freq_indices)
    freq_val = Omega_list(freq_indices(idx))/(2*pi*1e9);
    freq_labels{idx} = sprintf('%.2f GHz', freq_val);
end

% 创建段标签
segment_labels = cell(1, num_segments);
for s = 1:num_segments
    segment_labels{s} = sprintf('%d-%dm', segment_ranges(s, 1), segment_ranges(s, 2));
end

% 绘制声场曲线
for freq_idx_idx = 1:length(freq_indices)
    freq_idx = freq_indices(freq_idx_idx);
    % 使用indexToColor为每个频率生成颜色
    color = hsv2rgb([freq_idx_idx/length(freq_indices), 0.8, 0.9]);
    
    % 对每个段绘制一条线
    for s = 1:num_segments
        % 提取此段此频率的声场幅度
        if ~isempty(all_segment_3D_acoustic{s})
            acoustic_data = abs(all_segment_3D_acoustic{s}(:, freq_idx));
            
            % 绘制线段
            h = plot3(t*1e9, s*ones(size(t)), acoustic_data, 'Color', color, 'LineWidth', 2);
            
            % 保存第一个段的句柄用于图例
            if s == 1
                h_legend_acoustic(freq_idx_idx) = h;
            end
        end
    end
end

% 设置图表属性
view(30, 40);
xlabel('时间 (ns)', 'FontSize', 14);
set(gca, 'YTick', 1:num_segments, 'YTickLabel', segment_labels);
ylabel('光纤段', 'FontSize', 14);
zlabel('声场幅度', 'FontSize', 14);
title('各段相遇点的声场幅度分布', 'FontSize', 14);
grid on;

% 添加正确的图例
legend(h_legend_acoustic, freq_labels, 'Location', 'best', 'FontSize', 12);
hold off;

% 保存图形
saveas(gcf, 'acoustic_field_3D.fig');
saveas(gcf, 'acoustic_field_3D.png');

% 4. 斯托克斯信号分布3D图 (仅显示开始、中间和结束频率点)
figure(3);
hold on;

% 存储图例句柄
h_legend_stokes = zeros(1, length(freq_indices));

% 绘制斯托克斯功率曲线
for freq_idx_idx = 1:length(freq_indices)
    freq_idx = freq_indices(freq_idx_idx);
    % 使用indexToColor为每个频率生成颜色
    color = hsv2rgb([freq_idx_idx/length(freq_indices), 0.8, 0.9]);
    
    % 对每个段绘制一条线
    for s = 1:num_segments
        % 提取此段此频率的斯托克斯功率
        if ~isempty(all_segment_3D_stokes{s})
            stokes_data = all_segment_3D_stokes{s}(:, freq_idx);
            
            % 绘制线段
            h = plot3(t*1e9, s*ones(size(t)), stokes_data, 'Color', color, 'LineWidth', 2);
            
            % 保存第一个段的句柄用于图例
            if s == 1
                h_legend_stokes(freq_idx_idx) = h;
            end
        end
    end
end

% 设置图表属性
view(30, 40);
xlabel('时间 (ns)', 'FontSize', 14);
set(gca, 'YTick', 1:num_segments, 'YTickLabel', segment_labels);
ylabel('光纤段', 'FontSize', 14);
zlabel('斯托克斯功率 (W)', 'FontSize', 14);
title('z=0处斯托克斯功率分布', 'FontSize', 14);
grid on;

% 添加正确的图例
legend(h_legend_stokes, freq_labels, 'Location', 'best', 'FontSize', 12);
hold off;

% 保存图形
saveas(gcf, 'stokes_power_3D.fig');
saveas(gcf, 'stokes_power_3D.png');

% 5. BGS热图 - 2D表示方式
figure(4);

% 准备网格数据
freq_grid = (Omega_list/(2*pi)-10.8e9)/1e6; % 频率轴(MHz)
pos_grid = sorted_positions; % 位置轴(m)

% 创建表面数据
[X, Y] = meshgrid(freq_grid, pos_grid);
Z = zeros(length(pos_grid), length(freq_grid));

% 收集所有段的BGS数据
% 重新获取排序索引（因为作用域问题）
[~, sort_idx_bgs] = sort(full_fiber_positions);
for i = 1:length(sorted_positions)
    original_idx = sort_idx_bgs(i);
    % 找到原始数据所在的段和窗口
    for s = 1:num_segments
        if ~isempty(all_window_positions{s}) && ~isempty(all_BGS_results{s}) && ...
           any(ismember(mean(all_window_positions{s},2), full_fiber_positions(original_idx)))
            % 找到对应的窗口索引
            [~, win_idx] = min(abs(mean(all_window_positions{s},2) - full_fiber_positions(original_idx)));
            % 提取BGS数据并归一化
            bgs_data = all_BGS_results{s}(win_idx,:);
            max_val = max(abs(bgs_data) + eps);
            if max_val > 0
                Z(i,:) = bgs_data / max_val;
            end
            break;
        end
    end
end

% 绘制热图
imagesc(freq_grid, pos_grid, Z);
set(gca, 'YDir', 'normal'); % 确保Y轴方向正确
colormap('jet');
colorbar;
xlabel('频率偏移 (MHz)', 'FontSize', 14);
ylabel('光纤位置 (m)', 'FontSize', 14);
title('全光纤布里渊增益谱（BGS）分布', 'FontSize', 14);

% 添加段分隔线
hold on;
for s = 1:num_segments-1
    segment_boundary = s * segment_length;
    line([freq_grid(1) freq_grid(end)], [segment_boundary segment_boundary], 'Color', 'w', 'LineStyle', '--', 'LineWidth', 2);
end

% 添加应变区域标记
for i = 1:size(strain_regions, 1)
    region_start = strain_regions(i, 1);
    region_end = strain_regions(i, 2);
    region_shift = strain_regions(i, 3);
    
    % 标记应变区域
    rectangle('Position', [freq_grid(1), region_start, freq_grid(end)-freq_grid(1), region_end-region_start], ...
              'EdgeColor', 'w', 'LineStyle', ':', 'LineWidth', 1.5);
    
    % 添加应变标签
    text(freq_grid(1) + 0.1*(freq_grid(end)-freq_grid(1)), (region_start+region_end)/2, ...
         [num2str(region_shift), ' MHz'], 'Color', 'w', 'FontWeight', 'bold');
end
hold off;

% 保存图形
saveas(gcf, 'BGS_heatmap.fig');
saveas(gcf, 'BGS_heatmap.png');

% 6. 布里渊相位谱的3D曲线图
figure(5);
hold on;

% 创建坐标轴
ax = gca;
ax.YDir = 'normal';
view(30, 30);

% 绘制BPS三维曲线
for s = 1:num_segments
    % 获取当前段的窗口位置和BPS数据
    if ~isempty(all_window_positions{s}) && ~isempty(all_BPS_results{s})
        segment_positions = mean(all_window_positions{s}, 2);
        segment_bps = all_BPS_results{s};
        
        % 对于每个窗口
        for w = 1:size(segment_positions, 1)
            % 获取当前位置
            pos = segment_positions(w);
            
            % 获取BPS数据，使用unwrap保证相位连续
            bps_data = unwrap(segment_bps(w, :));
            
            % 使用样条插值平滑曲线
            bps_interp = spline(freq_grid, bps_data, freq_interp);
            
            % 绘制3D平滑曲线
            plot3(freq_interp, ones(size(freq_interp))*pos, bps_interp, 'LineWidth', 1.6, 'Color', indexToColor(w));
        end
    end
end

% 设置图表属性
xlabel('频率偏移 (MHz)', 'FontSize', 14);
ylabel('光纤位置 (m)', 'FontSize', 14);
zlabel('相位 (rad)', 'FontSize', 14);
title('全光纤布里渊相位谱（BPS）曲线', 'FontSize', 14);
grid on;
xlim([min(freq_grid), max(freq_grid)]);
ylim([0, L]);

% 添加段分隔线
for s = 1:num_segments-1
    segment_boundary = s * segment_length;
    line([min(freq_grid), max(freq_grid)], [segment_boundary, segment_boundary], [0, 0], 'Color', 'k', 'LineStyle', '--');
end

hold off;

% 保存图形
saveas(gcf, 'BPS_3D_curves.fig');
saveas(gcf, 'BPS_3D_curves.png');

% 7. 布里渊相位增益比的3D曲线图
figure(6);
hold on;

% 创建坐标轴
ax = gca;
ax.YDir = 'normal';
view(30, 30);

% 绘制BPGR三维曲线
for s = 1:num_segments
    % 获取当前段的窗口位置和BPGR数据
    if ~isempty(all_window_positions{s}) && ~isempty(all_BPGR_results{s})
        segment_positions = mean(all_window_positions{s}, 2);
        segment_bpgr = all_BPGR_results{s};
        
        % 对于每个窗口
        for w = 1:size(segment_positions, 1)
            % 获取当前位置
            pos = segment_positions(w);
            
            % 获取BPGR数据
            bpgr_data = segment_bpgr(w, :);
            
            % 使用样条插值平滑曲线
            bpgr_interp = spline(freq_grid, bpgr_data, freq_interp);
            
            % 绘制3D平滑曲线
            plot3(freq_interp, ones(size(freq_interp))*pos, bpgr_interp, 'LineWidth', 1.6, 'Color', indexToColor(w));
        end
    end
end

% 设置图表属性
xlabel('频率偏移 (MHz)', 'FontSize', 14);
ylabel('光纤位置 (m)', 'FontSize', 14);
zlabel('相位增益比 (rad/W)', 'FontSize', 14);
title('全光纤布里渊相位增益比（BPGR）曲线', 'FontSize', 14);
grid on;
xlim([min(freq_grid), max(freq_grid)]);
ylim([0, L]);

% 添加段分隔线
for s = 1:num_segments-1
    segment_boundary = s * segment_length;
    line([min(freq_grid), max(freq_grid)], [segment_boundary, segment_boundary], [0, 0], 'Color', 'k', 'LineStyle', '--');
end

hold off;

% 保存图形
saveas(gcf, 'BPGR_3D_curves.fig');
saveas(gcf, 'BPGR_3D_curves.png');

% 8. 保存最终结果
save('final_results.mat', 'all_BGS_results', 'all_BPS_results', 'all_window_positions', ...
     'all_BFS_distribution', 'full_fiber_positions', 'full_fiber_BFS', ...
     'strain_regions', 'L', 'segment_ranges', '-v7.3');

% 显示完成信息
disp(['所有计算和可视化完成。总运行时间: ', num2str(toc/60), ' 分钟']);
disp('结果图已保存至当前目录。');
disp('完整结果数据已保存至 final_results.mat');

% 定义基于索引的颜色生成函数
function color = indexToColor(idx)
    % 使用黄金比例确保色相分布均匀
    golden_ratio_conjugate = 0.618033988749895;
    h = mod(idx * golden_ratio_conjugate, 1); % 色相 (0-1)
    s = 0.85;  % 饱和度
    v = 0.9;   % 亮度
    color = hsv2rgb([h s v]); % 转换为RGB
end