"""
全面测试TensorBoard安装和使用
"""

import os
import sys

def check_python_env():
    """检查Python环境"""
    print("\n=== Python环境信息 ===")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    
    # 检查PYTHONPATH
    print(f"Python路径列表: {sys.path}")

def check_tensorboard_install():
    """检查TensorBoard安装"""
    print("\n=== TensorBoard安装检查 ===")
    
    # 1. 直接导入tensorboard
    try:
        import tensorboard
        print(f"✅ tensorboard 导入成功，版本: {tensorboard.__version__}")
    except ImportError as e:
        print(f"❌ tensorboard 导入失败: {e}")
        print("尝试安装: pip install tensorboard")
        return False
    
    # 2. 检查PyTorch的tensorboard集成
    try:
        import torch
        print(f"✅ PyTorch 导入成功，版本: {torch.__version__}")
        
        try:
            from torch.utils.tensorboard import SummaryWriter
            print("✅ torch.utils.tensorboard.SummaryWriter 导入成功")
        except ImportError as e:
            print(f"❌ torch.utils.tensorboard 导入失败: {e}")
            print("可能需要更新PyTorch: pip install --upgrade torch")
            return False
    except ImportError as e:
        print(f"❌ PyTorch 导入失败: {e}")
        return False
    
    return True

def test_tensorboard_write():
    """测试TensorBoard写入功能"""
    print("\n=== TensorBoard写入测试 ===")
    
    try:
        from torch.utils.tensorboard import SummaryWriter
        
        # 创建日志目录
        log_dir = "./test_tensorboard_logs"
        os.makedirs(log_dir, exist_ok=True)
        print(f"创建日志目录: {log_dir}")
        
        # 创建SummaryWriter
        writer = SummaryWriter(log_dir=log_dir)
        print("✅ 成功创建SummaryWriter")
        
        # 写入一些测试数据
        for i in range(10):
            writer.add_scalar("test/value", i * 0.1, i)
        print("✅ 成功写入测试数据")
        
        # 关闭writer
        writer.close()
        print("✅ 成功关闭SummaryWriter")
        
        print(f"\n要查看TensorBoard，请运行: tensorboard --logdir={log_dir}")
        return True
    except Exception as e:
        print(f"❌ TensorBoard写入测试失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    print("\n=== 依赖包检查 ===")
    
    dependencies = [
        "numpy", "torch", "tensorboard", "protobuf", 
        "grpcio", "werkzeug", "absl-py", "markdown"
    ]
    
    for dep in dependencies:
        try:
            module = __import__(dep)
            if hasattr(module, "__version__"):
                print(f"✅ {dep}: {module.__version__}")
            else:
                print(f"✅ {dep}: 已安装 (无版本信息)")
        except ImportError:
            print(f"❌ {dep}: 未安装")

def main():
    """主函数"""
    print("===== TensorBoard 全面诊断 =====")
    
    # 检查Python环境
    check_python_env()
    
    # 检查依赖包
    check_dependencies()
    
    # 检查TensorBoard安装
    if not check_tensorboard_install():
        print("\n❌ TensorBoard安装检查失败，请先解决上述问题")
        return
    
    # 测试TensorBoard写入
    if not test_tensorboard_write():
        print("\n❌ TensorBoard写入测试失败，请先解决上述问题")
        return
    
    print("\n✅ 所有测试通过！TensorBoard应该可以正常工作")
    print("如果botda_train.py仍然报错，可能是其他代码问题")

if __name__ == "__main__":
    main()
