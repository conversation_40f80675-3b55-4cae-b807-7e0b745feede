#!/usr/bin/env python3
"""
简单的路径验证测试（不依赖外部包）
"""

import os
import glob

def main():
    print("=== 简单路径验证 ===\n")
    
    # 检查数据目录
    data_dir = "./BOTDA_Dataset"
    print(f"数据目录: {data_dir}")
    print(f"存在: {os.path.exists(data_dir)}")
    
    if os.path.exists(data_dir):
        print(f"目录内容: {os.listdir(data_dir)}")
        
        # 检查各子目录
        for subdir in ['train', 'val', 'test']:
            subdir_path = os.path.join(data_dir, subdir)
            if os.path.exists(subdir_path):
                files = glob.glob(os.path.join(subdir_path, "*.mat"))
                print(f"{subdir}: {len(files)} 个文件")
            else:
                print(f"{subdir}: 目录不存在")
    
    # 检查代码文件中的路径设置
    print(f"\n=== 检查代码文件 ===")
    
    for filename in ['botda_train.py', 'botda_test.py']:
        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'DATA_DIR = "./BOTDA_Dataset"' in content:
                print(f"✅ {filename}: 路径设置正确")
            elif 'DATA_DIR = "./DL_DATA"' in content:
                print(f"❌ {filename}: 路径设置错误")
            else:
                print(f"⚠️  {filename}: 未找到DATA_DIR设置")
        else:
            print(f"❌ {filename}: 文件不存在")

if __name__ == "__main__":
    main()
