=== BOTDA深度学习训练数据集摘要（快速求和方法）===
生成时间: 29-Jul-2025 13:18:23
样本总数: 1
数据集划分:
  - 训练集: 0 样本 (70%)
  - 验证集: 0 样本 (30%)
  - 测试集: 1 样本

--- 段配置信息 ---
段长度: 20m
光纤总段数: 10段 (1-10段，第10段被排除)
有效段数: 9段 (第1-9段)
保存段数: 9段 (第1-9段)

--- 快速求和方法优势 ---
使用真实响应求和求解方法替代SBS三波耦合方程求解方法
计算速度提升显著，适用于短脉冲情况
近似条件：幅度增益和损失较小，相互作用导致的幅度变化可忽略

--- 光纤配置 ---
光纤总长度: 200m
有效光纤长度: 180.0m (用于应变区域生成)
分区长度: 100m
分区数量: 2个

--- 应变区域配置 ---
应变区域长度选项: 0.5m, 1m, 1.5m, 2m, 3m, 4m, 5m, 6m, 8m, 10m
应变区域数量: 2-6个（随机样本），多样化配置（测试样本）
频移范围: -40.0MHz 到 +50.0MHz
应变区域限制: 只在有效光纤长度内 (0-180.0m)
测试样本: 15种多样化固定配置，适配有效光纤长度

--- 保存的频率点信息 ---
保存3个频率点的斯托克斯信号:
- 频率点1: 10.73 GHz
- 频率点2: 10.80 GHz
- 频率点3: 10.87 GHz
扫频步长: 70MHz

--- 边界伪影消除 ---
使用重叠扩展计算域方法消除分区边界伪影
重叠区长度: 1.5倍泵浦脉冲长度 (15m)
第一分区扩展: 0-100m -> 0-115m
第二分区扩展: 100m-200m -> 85m-200m
最后一段处理: 完全排除，避免不可消除的光纤末端伪影

--- 数据归一化说明 ---
每个频率点的斯托克斯信号单独归一化到[0,1]范围
