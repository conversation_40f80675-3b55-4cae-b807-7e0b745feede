#!/usr/bin/env python3
# ---------------------------------------------------------------
#  数据格式转换脚本
#  
#  功能：
#  1. 将Python测试结果转换为MATLAB可读格式
#  2. 为MATLAB传统方法提供测试数据
# ---------------------------------------------------------------

import numpy as np
import scipy.io as sio
import os

def convert_python_data_to_matlab():
    """将Python测试结果转换为MATLAB格式"""
    
    # 检查Python测试结果文件
    python_data_path = 'results/bfs_data_file_0.npz'
    
    if not os.path.exists(python_data_path):
        print(f"❌ 未找到Python测试结果文件: {python_data_path}")
        print("请先运行botda_test.py生成测试结果")
        return False
    
    try:
        # 读取Python测试结果
        print(f"📖 正在读取Python测试结果: {python_data_path}")
        data = np.load(python_data_path)
        
        print("包含的数据:")
        for key in data.keys():
            print(f"  - {key}: {data[key].shape if hasattr(data[key], 'shape') else type(data[key])}")
        
        # 转换为MATLAB格式
        matlab_data_path = 'results/bfs_data_file_0.mat'
        print(f"💾 正在转换为MATLAB格式: {matlab_data_path}")
        
        sio.savemat(matlab_data_path, {
            'target': data['target'],           # 真实BFS分布
            'prediction': data['prediction'],   # 预测BFS分布
            'bfs_min': float(data['bfs_min']),  # BFS最小值
            'bfs_max': float(data['bfs_max'])   # BFS最大值
        })
        
        print("✅ 数据转换完成")
        print(f"MATLAB可以使用以下代码读取数据:")
        print(f"  loaded_data = load('{matlab_data_path}');")
        print(f"  true_bfs = loaded_data.target;")
        print(f"  pred_bfs = loaded_data.prediction;")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据转换失败: {e}")
        return False

if __name__ == '__main__':
    print("🔄 开始数据格式转换...")
    
    # 确保results目录存在
    if not os.path.exists('results'):
        os.makedirs('results')
        print("📁 创建results目录")
    
    # 执行转换
    success = convert_python_data_to_matlab()
    
    if success:
        print("\n🎉 转换完成！现在可以:")
        print("1. 运行修改后的MATLAB代码 erjp_tsyigsui_200m.m")
        print("2. 然后运行Python对比分析代码")
    else:
        print("\n❌ 转换失败，请检查错误信息")
