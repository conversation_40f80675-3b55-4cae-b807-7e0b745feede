# ---------------------------------------------------------------
#  BOTDA 超分辨率网络训练脚本（回归问题：预测连续的数值，使用MSE、MAE、RMSE、R²等指标，不需要准确率、精确率、召回率等指标）
#  
#  功能：
#  1. 加载MATLAB生成的训练和验证数据
#  2. 训练BOTDA超分辨率网络
#  3. 验证模型性能
#  4. 保存最佳模型
#  
#  数据格式：
#  - 输入：归一化后的斯托克斯信号 [0,1]
#  - 标签：高斯平滑后归一化的BFS分布 [0,1]
# ---------------------------------------------------------------

import os
import glob
import numpy as np

# 禁用TensorFlow的自动导入，避免与PyTorch TensorBoard冲突
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'
import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
from scipy.ndimage import gaussian_filter1d
import random
from torch.utils.data import Dataset, DataLoader
from torch.utils.tensorboard import SummaryWriter
import scipy.io as sio
from tqdm import tqdm
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import json
from datetime import datetime
import time  # 计时器模块
from collections import deque
import math

from botda_model import HybridUNetTCN
from wavelet_loss import MultiScaleWaveletLoss, FrequencyDomainLoss


class GradMonitor:
    """
    维护一个滑动窗口 + EMA 的梯度范数统计，判断是否异常。
    """
    def __init__(self, win_size=100, ema_beta=0.98, std_factor=4.0, abs_clip=None):
        self.win      = deque(maxlen=win_size)
        self.ema_beta = ema_beta
        self.std_factor = std_factor
        self.abs_clip = abs_clip   # 若想用绝对阈值，给个 float
        self.ema      = 0.0
        self.var      = 0.0  # Welford 方差
        self.count    = 0

    def update(self, value: float):
        # 更新滑动窗口
        self.win.append(value)
        # 更新 EMA 和方差估计
        self.count += 1
        delta      = value - self.ema
        self.ema  += delta * (1 - self.ema_beta)
        self.var  += delta * (value - self.ema)   # Welford
        return self

    def is_anomaly(self, value: float) -> bool:
        if self.abs_clip is not None and value > self.abs_clip:
            return True
        if self.count < 20:        # 还没稳定，先不过滤
            return False
        mean = self.ema
        std  = math.sqrt(self.var / max(self.count-1, 1))
        return value > mean + self.std_factor * std


def save_training_plots(train_losses, val_losses, learning_rates, current_epoch, save_dir):
    """
    保存训练损失和学习率图片

    参数：
    - train_losses: 训练损失列表
    - val_losses: 验证损失列表
    - learning_rates: 学习率列表
    - current_epoch: 当前epoch数
    - save_dir: 保存目录
    """
    import matplotlib.pyplot as plt
    plt.ioff()  # 关闭交互模式

    # 创建图形
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

    # 绘制损失曲线
    epochs = range(1, len(train_losses) + 1)
    ax1.plot(epochs, train_losses, 'b-', label='Training Loss', linewidth=2)
    ax1.plot(epochs, val_losses, 'r-', label='Validation Loss', linewidth=2)
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.set_title(f'Training and Validation Loss (Epoch {current_epoch})')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_yscale('log')  # 使用对数刻度

    # 绘制学习率曲线
    ax2.plot(epochs, learning_rates, 'g-', label='Learning Rate', linewidth=2)
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('Learning Rate')
    ax2.set_title(f'Learning Rate Schedule (Epoch {current_epoch})')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_yscale('log')  # 使用对数刻度

    # 保存图片
    plt.tight_layout()
    loss_plot_path = os.path.join(save_dir, f'training_progress_epoch_{current_epoch}.png')
    plt.savefig(loss_plot_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"Training plots saved: {loss_plot_path}")


def visualize_first_training_sample(train_dataset):
    """
    可视化第一个训练文件的完整数据（9段斯托克斯信号和BFS标签），目的是查看数据是否读取正确

    参数：
    - train_dataset: 训练数据集对象
    """
    try:
        import matplotlib.pyplot as plt
        from mpl_toolkits.mplot3d import Axes3D
        import os

        print("正在加载第一个训练文件进行可视化验证...")

        # 获取第一个文件路径
        first_file_path = train_dataset.file_paths[0]
        print(f"文件路径: {first_file_path}")

        # 检查文件是否存在
        if not os.path.exists(first_file_path):
            print(f"错误：文件不存在 - {first_file_path}")
            return

        # 直接加载完整的第一个文件数据
        import scipy.io as sio
        print("正在加载MATLAB文件...")
        sample_data = sio.loadmat(first_file_path)
        print("MATLAB文件加载成功")

        # 打印文件中的所有键
        print(f"文件中的键: {list(sample_data.keys())}")

        # 检查实际的数据结构
        if 'sample_data' in sample_data:
            actual_data = sample_data['sample_data']
            print(f"sample_data中的键: {list(actual_data.dtype.names) if hasattr(actual_data, 'dtype') else 'N/A'}")

            # 提取数据 - 使用正确的结构
            stokes_signals = actual_data['stokes_signals'][0, 0]  # 从结构体中提取
            bfs_distribution = actual_data['frequency_shift_distribution_normalized'][0, 0].flatten()
            fiber_positions = actual_data['fiber_positions'][0, 0].flatten()
        else:
            # 如果没有sample_data结构，直接提取
            stokes_signals = sample_data['stokes_signals']  # (9, 1) cell array
            bfs_distribution = sample_data['frequency_shift_distribution_normalized'].flatten()  # 归一化BFS标签
            fiber_positions = sample_data['fiber_positions'].flatten()  # 光纤位置

        print(f"斯托克斯信号数据结构: {stokes_signals.shape}")
        print(f"BFS分布长度: {len(bfs_distribution)}")
        print(f"光纤位置长度: {len(fiber_positions)}")

        # 创建图形
        fig = plt.figure(figsize=(16, 12))

        # ==================== 子图1: 斯托克斯信号3D展示 ====================
        ax1 = fig.add_subplot(2, 1, 1, projection='3d')

        # 颜色和标签
        colors = ['r', 'g', 'b']  # 红、绿、蓝分别对应3个频率点
        freq_labels = ['10.73 GHz', '10.80 GHz', '10.87 GHz']

        legend_handles = []

        # 为每个段绘制三线图
        print(f"斯托克斯信号数组形状: {stokes_signals.shape}")

        # 根据实际数据结构访问数据
        if stokes_signals.shape == (1, 9):  # (1, num_segments) 结构
            for seg_idx in range(9):  # 9段
                try:
                    # 获取第seg_idx段的信号矩阵
                    signal_matrix = stokes_signals[0, seg_idx]  # 正确的访问方式
                    print(f"段 {seg_idx+1} 信号形状: {signal_matrix.shape if hasattr(signal_matrix, 'shape') else 'N/A'}")

                    if signal_matrix is not None and hasattr(signal_matrix, 'shape') and signal_matrix.size > 0:
                        time_points = signal_matrix.shape[0]

                        # 计算时间轴 (仿真中Delta_t = 0.5ns)
                        Delta_t = 0.5e-9  # 0.5ns
                        time_axis = np.arange(time_points) * Delta_t * 1e9  # 转换为ns

                        # 绘制该段的3个频率点信号
                        for freq_idx in range(min(3, signal_matrix.shape[1])):
                            freq_signal = signal_matrix[:, freq_idx]

                            # 段编号作为y轴
                            y_val = np.full_like(time_axis, seg_idx + 1)  # 段编号从1开始

                            # 绘制3D线图
                            line = ax1.plot(time_axis, y_val, freq_signal,
                                           color=colors[freq_idx], linewidth=1.5,
                                           label=freq_labels[freq_idx] if seg_idx == 0 else "")

                            # 只为第一段添加图例句柄
                            if seg_idx == 0:
                                legend_handles.append(line[0])
                    else:
                        print(f"段 {seg_idx+1} 数据无效或为空")

                except Exception as e:
                    print(f"处理段 {seg_idx+1} 时出错: {e}")
        else:
            print(f"未知的斯托克斯信号数据结构: {stokes_signals.shape}")

        ax1.set_xlabel('Time (ns)', fontsize=12)
        ax1.set_ylabel('Segment Number', fontsize=12)
        ax1.set_zlabel('Normalized Stokes Power', fontsize=12)
        ax1.set_title('First Training File - All Segments Stokes Signal', fontsize=14)
        ax1.legend(legend_handles, freq_labels, loc='best')
        ax1.grid(True)

        # 设置y轴刻度
        ax1.set_yticks(range(1, 10))
        ax1.set_ylim(0.5, 9.5)

        # ==================== 子图2: 归一化BFS标签 ====================
        ax2 = fig.add_subplot(2, 1, 2)

        # 绘制归一化BFS分布
        ax2.plot(fiber_positions, bfs_distribution, 'b-', linewidth=3, label='Normalized BFS Label')

        ax2.set_xlabel('Fiber Position (m)', fontsize=12)
        ax2.set_ylabel('Normalized BFS Value [0,1]', fontsize=12)
        ax2.set_title('First Training File - Normalized BFS Label (For Training)', fontsize=14)
        ax2.grid(True)
        ax2.legend()

        # 设置x轴范围
        ax2.set_xlim(0, fiber_positions[-1])

        # 添加统计信息
        bfs_min = np.min(bfs_distribution)
        bfs_max = np.max(bfs_distribution)
        bfs_mean = np.mean(bfs_distribution)

        info_text = f'BFS Statistics:\nMin: {bfs_min:.3f}\nMax: {bfs_max:.3f}\nMean: {bfs_mean:.3f}'
        ax2.text(0.02, 0.98, info_text, transform=ax2.transAxes,
                verticalalignment='top', fontsize=10,
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()

        # 保存图片到checkpoints目录
        save_dir = './checkpoints'
        os.makedirs(save_dir, exist_ok=True)
        save_path = os.path.join(save_dir, 'first_training_sample_visualization.png')

        print(f"准备保存图片到: {save_path}")
        print(f"保存目录是否存在: {os.path.exists(save_dir)}")

        try:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"✅ 可视化图片已保存: {save_path}")
            print(f"文件大小: {os.path.getsize(save_path)} bytes")
        except Exception as e:
            print(f"❌ 保存图片时出错: {e}")
            import traceback
            traceback.print_exc()
            # 尝试保存到当前目录
            fallback_path = './first_training_sample_visualization.png'
            try:
                plt.savefig(fallback_path, dpi=300, bbox_inches='tight')
                print(f"✅ 已保存到备用路径: {fallback_path}")
                print(f"文件大小: {os.path.getsize(fallback_path)} bytes")
            except Exception as e2:
                print(f"❌ 备用路径也保存失败: {e2}")

        # 不显示图形，只保存
        # plt.show()  # 注释掉显示，避免在服务器环境中出错
        plt.close()
        print("图形已关闭")

        # 打印数据统计信息
        print(f"\n数据统计信息:")
        print(f"  文件名: {os.path.basename(first_file_path)}")
        print(f"  段数: 9")
        print(f"  每段时间点数: {signal_matrix.shape[0] if 'signal_matrix' in locals() else 'N/A'}")
        print(f"  频率点数: {signal_matrix.shape[1] if 'signal_matrix' in locals() else 'N/A'}")
        print(f"  BFS分布点数: {len(bfs_distribution)}")
        print(f"  光纤总长度: {fiber_positions[-1]:.1f}m")
        print(f"  BFS值范围: [{bfs_min:.3f}, {bfs_max:.3f}]")

    except Exception as e:
        print(f"可视化过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        print("跳过可视化，继续训练...")


class BOTDADataset(Dataset):
    """
    BOTDA数据集类
    
    功能：
    1. 加载MATLAB生成的.mat文件
    2. 提取斯托克斯信号和BFS标签
    3. 按窗口大小组织数据
    """
    
    def __init__(self,
                 data_dir: str,
                 dataset_type: str = 'train',
                 window_size: int = 5,
                 stride: Optional[int] = None,
                 max_samples: Optional[int] = None):
        """
        初始化数据集

        参数：
        - data_dir: 数据目录路径
        - dataset_type: 数据集类型 ('train', 'val', 'test')
        - window_size: 窗口大小（段数）
        - stride: 滑窗步长，None表示自动选择
                 1: 重叠滑窗（每次移动1段）
                 window_size-1: 非重叠滑窗（每次移动window_size-1段）
        - max_samples: 最大样本数（用于调试）
        """
        self.data_dir = data_dir
        self.dataset_type = dataset_type
        self.window_size = window_size

        # 设置滑窗的步长
        if stride is None:
            # 默认使用重叠滑窗（步长=1）
            self.stride = 1
        else:
            self.stride = stride

        # 验证步长的有效性
        if self.stride <= 0:
            raise ValueError("步长必须大于0")
        if self.stride > self.window_size:
            raise ValueError(f"步长({self.stride})不能大于窗口大小({self.window_size})")
        
        # 查找数据文件
        pattern = os.path.join(data_dir, dataset_type, f"{dataset_type}_sample_*.mat")
        # 除了传入了顶层目录 data_dir (也就是 ./BOTDA_Dataset)，还额外传入了一个第二个参数（数据集所在文件夹的名字：train/val/test）
        # os.path.join(...)：作用是智能地将多个字符串拼接成一个操作系统兼容的路径。
        # 当后面create_data_loaders函数创建训练数据集train_dataset时，如何生成pattern：
        # data_dir是. / BOTDA_Dataset，
        # dataset_type是train，
        # f"{dataset_type}_sample_*.mat"是train_sample_ *.mat，
        # os.path.join会把这三部分拼接起来，得到：pattern = "./BOTDA_Dataset/train/train_sample_*.mat"

        self.file_paths = sorted(glob.glob(pattern))
        # glob.glob(pattern): 查找所有匹配模式的文件
        # sorted(): 按文件名排序，确保数据加载顺序一致
        # 例如找到：["train_sample_0001.mat", "train_sample_0002.mat", ...]
        
        if max_samples is not None:
            self.file_paths = self.file_paths[:max_samples]
            # 作用：限制样本数量（调试用）
            # 如果设置了最大样本数，只取前max_samples个文件
            # 用于快速测试，避免加载全部数据
        
        print(f"找到 {len(self.file_paths)} 个 {dataset_type} 样本")
        
        if len(self.file_paths) == 0:
            raise ValueError(f"在 {pattern} 未找到数据文件")

        # 当调用BOTDADataset("./BOTDA_Dataset", "train")时：
        # pattern = "./BOTDA_Dataset/train/train_sample_*.mat"
        # glob.glob()找到：["train_sample_0001.mat", "train_sample_0002.mat", "train_sample_0003.mat"]
        # 输出："找到 3 个 train 样本"
        
        # 加载第一个文件获取数据维度信息
        self._get_data_info()
    
    def _get_data_info(self):
        """获取数据维度信息"""
        sample_data = sio.loadmat(self.file_paths[0])['sample_data']
        # 加载第一个找到的.mat文件，提取其中的sample_data结构体，对应MATLAB代码中保存的：save(filepath, 'sample_data')
        
        # 提取斯托克斯信号（归一化后的）
        stokes_signals = sample_data['stokes_signals'][0, 0]
        # 对应MATLAB代码中的sample_data.stokes_signals = stokes_signals_normalized;  % 归一化的信号（用于训练，噪声已在power_total中添加）
        
        # 获取段数
        self.valid_segments = []

        # 检查stokes_signals的实际结构
        print(f"调试信息 - stokes_signals形状: {stokes_signals.shape}")
        print(f"调试信息 - stokes_signals类型: {type(stokes_signals)}")
        print(f"调试信息 - stokes_signals数据类型: {stokes_signals.dtype}")

        # 根据实际结构确定遍历方式
        if stokes_signals.shape[0] > stokes_signals.shape[1]:
            # (num_segments, 1) 结构
            print(f"调试信息 - 检测到 (num_segments, 1) 结构")
            self.is_row_major = True  # 段在行方向
            num_segments = stokes_signals.shape[0]
            for i in range(num_segments):
                if stokes_signals[i, 0].size > 0:
                    self.valid_segments.append(i)
            # 从样本的第一个有效段获取数据维度
            first_segment = stokes_signals[self.valid_segments[0], 0]
        else:
            # (1, num_segments) 结构
            print(f"调试信息 - 检测到 (1, num_segments) 结构")
            self.is_row_major = False  # 段在列方向
            num_segments = stokes_signals.shape[1]
            for i in range(num_segments):
                if stokes_signals[0, i].size > 0:
                    self.valid_segments.append(i)
            # 从样本的第一个有效段获取数据维度
            first_segment = stokes_signals[0, self.valid_segments[0]]

        print(f"调试信息 - 有效段数: {len(self.valid_segments)}")
        print(f"调试信息 - 第一个有效段形状: {first_segment.shape}")
        self.time_points = first_segment.shape[0]
        self.freq_channels = first_segment.shape[1]
        
        # 获取BFS分布长度
        # 对应MATLAB代码中的sample_data.frequency_shift_distribution_normalized = frequency_shift_distribution_normalized; % 平滑后归一化BFS分布（用于训练）
        bfs_label = sample_data['frequency_shift_distribution_normalized'][0, 0]
        self.bfs_length = len(bfs_label)
        
        print(f"数据信息:")
        print(f"  有效段数: {len(self.valid_segments)}")
        print(f"  时间点数: {self.time_points}")
        print(f"  频率通道数: {self.freq_channels}")
        print(f"  BFS分布长度: {self.bfs_length}")
        print(f"  滑窗步长: {self.stride}")

        # 计算每个样本的窗口数量
        self.windows_per_sample = self._calculate_windows_per_sample()

    def _calculate_windows_per_sample(self) -> int:
        """
        计算每个样本可以生成的窗口数量，包含向前扩展的窗口

        新策略：
        1. 按步长=窗长-1生成标准窗口
        2. 如果最后剩余段数不足5段但>=1段，则向前扩展生成额外窗口

        例如：9段数据 (索引0-8)
        - 标准窗口：[0,1,2,3,4], [4,5,6,7,8] (2个窗口)
        - 最后一段(索引8)已被包含，无需额外窗口

        例如：10段数据 (索引0-9)
        - 标准窗口：[0,1,2,3,4], [4,5,6,7,8] (2个窗口)
        - 最后一段(索引9)未被包含，需要向前扩展
        - 向前扩展：[5,6,7,8,9] (额外1个窗口)
        - 重叠段[5,6,7,8]会被预测两次，需要平均

        返回：
        - 窗口数量
        """
        num_segments = len(self.valid_segments)

        if num_segments < self.window_size:
            return 0

        # 1. 计算标准非重叠窗口数量
        stride = self.window_size - 1  # 步长=4
        standard_windows = 0

        # 从位置0开始，每次步进4，直到无法放下完整的5段窗口
        pos = 0
        while pos + self.window_size <= num_segments:
            standard_windows += 1
            pos += stride

        # 2. 检查最后一段是否被包含
        # 最后一段的索引是 num_segments - 1
        last_segment_idx = num_segments - 1

        # 检查最后一个标准窗口是否包含最后一段，若包含，则不需要向前扩展，生成的全是标准窗口
        if standard_windows > 0:
            last_window_start = (standard_windows - 1) * stride  # 最后一个标准窗口的起始位置，从0开始算起
            last_window_end = last_window_start + self.window_size - 1  # 最后一个标准窗口的结束位置，也是从0开始算起

            if last_segment_idx <= last_window_end:
                # 最后一段已被包含，无需额外窗口
                return standard_windows
            else:
                # 最后一段未被包含，需要向前扩展
                return standard_windows + 1
        else:
            # 没有标准窗口但有足够段数，应该不会发生
            return 1 if num_segments >= self.window_size else 0

    def __len__(self):
        # 每个样本可以生成多个滑动窗口，数量取决于滑窗步长
        # 总样本数 = 训练集中文件数量（即完整测量了光纤多少次） × 每个文件中对应的滑窗数量
        return len(self.file_paths) * self.windows_per_sample
    
    def __getitem__(self, idx):
        # 根据收到的索引，创建一个个对应的样本，通过DataLoader，下发一个个索引，它会从 idx=0 开始，然后是 idx=1, idx=2，一直到所有可能的样本都产生完。
        # 由于有很多个.mat文件，每个.mat文件都代表一次完整的实验数据，通过sample_idx和window_idx知道对应的是哪个文件里面哪个窗，然后生成一个个滑窗样本，返回给DataLoader
        """
        获取一个训练样本

        新的滑窗策略：
        - 窗口大小：5段
        - 输入：第1,2,3,4,5段的斯托克斯信号拼接
        - 输出：只预测前4段的BFS分布（第1,2,3,4段）
        - 步长：window_size-1=4（非重叠滑窗）

        返回：
        - stokes_concatenated: (3, total_time_points) 拼接后的斯托克斯信号
        - bfs_target: (output_space_points,) 前4段的BFS标签拼接
        """
        # 计算样本索引和窗口索引
        # idx是滑窗生成的样本，sample_idx是完整文件的样本
        # 计算在第几个文件，从0开始计数
        sample_idx = idx // self.windows_per_sample
        # 计算在该文件下第几个窗口，也是从0开始计数
        window_idx = idx % self.windows_per_sample

        # 计算当前窗口在整个光纤所有段的索引
        # 需要与_calculate_windows_per_sample中的逻辑保持一致
        num_segments = len(self.valid_segments)
        stride = self.window_size - 1  # 步长=4

        # 计算标准窗口数量和位置
        standard_windows = 0
        pos = 0
        while pos + self.window_size <= num_segments:
            if standard_windows == window_idx:  # 说明当前idx处于一次.mat完整测量文件中的标准窗口
                # 找到对应的标准窗口
                actual_window_start = pos
                break
            standard_windows += 1
            pos += stride
        else:
            # 如果window_idx超出标准窗口范围，说明这是向前扩展的额外窗口
            # 检查是否真的需要额外窗口
            last_segment_idx = num_segments - 1  # 光纤最后一段索引
            if standard_windows > 0:
                last_window_start = (standard_windows - 1) * stride  # 最后一个标准窗口的起始位置，从0开始算起
                last_window_end = last_window_start + self.window_size - 1  # 最后一个标准窗口的结束位置，也是从0开始算起

                if last_segment_idx > last_window_end:  # 确实需要额外窗口，向前扩展
                    actual_window_start = num_segments - self.window_size
                    actual_window_start = max(0, actual_window_start)
                else:
                    # 不应该到这里，说明窗口索引有问题
                    raise ValueError(f"窗口索引{window_idx}超出范围，标准窗口数{standard_windows}")
            else:
                # 没有标准窗口，直接从0开始
                actual_window_start = 0
        
        # 加载样本数据
        sample_data = sio.loadmat(self.file_paths[sample_idx])['sample_data']  # 读取当前idx所在的.mat完整测量文件，即sample_idx
        
        # 提取当前完整测量文件中的斯托克斯信号（归一化后的）
        stokes_signals = sample_data['stokes_signals'][0, 0]
        
        # 提取当前完整测量文件中的BFS标签（高斯平滑后归一化的）
        bfs_label = sample_data['frequency_shift_distribution_normalized'][0, 0].flatten()
        
        # 新的数据构建方式：拼接5段斯托克斯信号
        stokes_segments = []

        # 根据所在窗的开始索引，找到里面的5段斯托克斯信号，然后拼接
        for i in range(self.window_size):
            seg_idx = self.valid_segments[actual_window_start + i]  # 在某个完整样本中的段索引

            # 根据数据结构选择正确的访问方式
            if self.is_row_major:
                # (num_segments, 1) 结构
                segment_data = stokes_signals[seg_idx, 0]
            else:
                # (1, num_segments) 结构
                segment_data = stokes_signals[0, seg_idx]

            if segment_data.size > 0:
                # 转置：(time, freq) -> (freq, time)
                segment_transposed = segment_data.T  # (freq_channels, time_points)
                stokes_segments.append(segment_transposed)

        # 拼接5段信号：(freq_channels, window_size * time_points)
        stokes_concatenated = np.concatenate(stokes_segments, axis=1)  # 拼接这五段信号得到stokes_concatenated

        # 打印维度信息（仅在第一次调用时）
        if not hasattr(self, '_printed_dims'):
            print(f"\n=== 数据维度信息 ===")
            print(f"窗口大小: {self.window_size}段")
            print(f"每段时间点数: {self.time_points}")
            print(f"频率通道数: {self.freq_channels}")
            print(f"单段斯托克斯信号维度: ({self.freq_channels}, {self.time_points})")
            print(f"拼接后斯托克斯信号维度: {stokes_concatenated.shape}")
            self._printed_dims = True
        
        # 新的BFS标签提取策略：只预测前4段
        # 根据段的空间位置提取对应的BFS片段
        segment_ranges = sample_data['segment_ranges'][0, 0]  # segment_ranges定义了每个段的物理起始和结束位置（单位：米）
        output_segments = self.window_size - 1  # 只预测前4段（5-1=4）

        # 提取前4段对应的BFS标签 - 修复：使用固定步长切片，避免浮点累计误差
        bfs_target = []
        space_points_per_segment = len(bfs_label) // len(self.valid_segments)  # 应该是200

        for i in range(output_segments):  # 前4段索引：0,1,2,3
            seg_idx = self.valid_segments[actual_window_start + i]

            # 使用固定步长切片，不依赖浮点累计误差
            start_idx = seg_idx * space_points_per_segment
            end_idx = start_idx + space_points_per_segment

            # 确保索引在有效范围内
            start_idx = max(0, min(start_idx, len(bfs_label)))
            end_idx = max(start_idx, min(end_idx, len(bfs_label)))

            # 提取该段的BFS标签
            segment_bfs = bfs_label[start_idx:end_idx]

            # 如果长度不足200，用边界值填充
            if len(segment_bfs) < space_points_per_segment:
                padding_needed = space_points_per_segment - len(segment_bfs)
                if len(segment_bfs) > 0:
                    segment_bfs = np.pad(segment_bfs, (0, padding_needed), 'edge')
                else:
                    segment_bfs = np.zeros(space_points_per_segment)

            bfs_target.append(segment_bfs)

        # # 如果各段长度不同，填充到相同长度
        # if len(bfs_target) > 0:
        #     max_len = max(len(seg) for seg in bfs_target)  # max_len得到bfs_target中存放的中间段的最长的一个段，而我现在段长都是固定的20m，所以理论上这里的max_len应该永远是20/0.1=200
        #     bfs_target_padded = []
        #     for seg_bfs in bfs_target:
        #         if len(seg_bfs) < max_len:
        #             # 用最后一个值填充
        #             padded = np.pad(seg_bfs, (0, max_len - len(seg_bfs)), 'edge')
        #         else:
        #             padded = seg_bfs[:max_len]
        #         bfs_target_padded.append(padded)
        #     bfs_target = np.array(bfs_target_padded)
        # else:
        #     # 备用方案：使用整个BFS分布
        #     bfs_target = np.tile(bfs_label, (output_segments, 1))

        # 关键修改：保持BFS标签的(4,200)形状，不再flatten
        bfs_array = np.array(bfs_target)  # (4, 200) 保持段间结构

        # 检查BFS标签的数值范围（仅在第一次调用时）
        if not hasattr(self, '_printed_bfs_stats'):
            print(f"DEBUG: BFS target range: [{np.min(bfs_array):.4f}, {np.max(bfs_array):.4f}]")
            self._printed_bfs_stats = True

        # 打印BFS维度信息（仅在第一次调用时）
        if not hasattr(self, '_printed_bfs_dims'):
            print(f"输出段数: {output_segments}")
            print(f"每段空间点数: {len(bfs_target[0]) if bfs_target else 0}")
            print(f"BFS标签维度: {bfs_array.shape} (保持段间结构)")
            print("===================\n")
            self._printed_bfs_dims = True

        # 把数组转化为32位浮点数的张量，保持(4,200)形状
        return (torch.FloatTensor(stokes_concatenated),  # 拼接后的5段斯托克斯信号
                torch.FloatTensor(bfs_array))            # (4,200)形状的BFS标签


def create_data_loaders(data_dir: str,
                       window_size: int = 5,
                       stride: Optional[int] = None,
                       batch_size: int = 16,
                       num_workers: int = 4,
                       max_samples: Optional[int] = None) -> Tuple[DataLoader, DataLoader]:
    # -> Tuple[DataLoader, DataLoader]
    # 类型提示（TypeHinting）语法。它本身不执行任何操作，也不影响代码的运行。Tuple: Tuple 来自Python的 typing 模块，它表示一个元组（tuple）。
    # [DataLoader, DataLoader]: 方括号里的内容描述了元组中每个元素的类型。这里表示这个元组包含两个元素，并且这两个元素都是 DataLoader 类的实例。
    # -> Tuple[DataLoader, DataLoader]这段代码在向阅读代码的人声明：“create_data_loaders这个函数将会返回一个元组，这个元组里有两个东西，第一个是DataLoader，第二个也是DataLoader。”
    # 功能：这是一个类型提示，用于声明函数的返回值类型。
    # 目的：提升代码的可读性、可维护性，并为开发工具提供更智能的支持。
    # 对运行的影响：没有影响。Python解释器在运行时会忽略这些提示。就像是代码的“注释”和“说明书”，但比普通注释更规范、更强大。
    """
    创建训练和验证数据加载器

    参数：
    - data_dir: 数据目录
    - window_size: 窗口大小
    - stride: 滑窗步长
    - batch_size: 批大小
    - num_workers: 数据加载进程数
    - max_samples: 最大样本数（调试用）

    返回：
    - train_loader: 训练数据加载器
    - val_loader: 验证数据加载器
    """
    # 创建数据集
    train_dataset = BOTDADataset(data_dir, 'train', window_size, stride, max_samples)  # 这里data_dir="./BOTDA_Dataset"，只提供了三个数据集的顶层目录，因此创建训练数据集的时候还需要输入存放训练数据集的文件名：train
    # train_dataset和val_dataset是你定义的BOTDADataset类的两个实例（Instance）。
    # train_dataset:
    # 这是一个BOTDADataset类的实例。在它被创建时（__init__被调用），它传入的参数是dataset_type = 'train'。因此，它内部的self.file_paths列表只包含了. / DL_DATA / train / 目录下的所有.mat文件。
    # 它知道训练集一共有多少个滑窗样本（通过__len__方法）。当你向它请求第i个样本时（通过__getitem__(i)方法），它知道如何去正确的.mat文件中加载数据，切片，然后返回一个(stokes_window, bfs_target)的数据对。
    val_dataset = BOTDADataset(data_dir, 'val', window_size, stride, max_samples)
    # 验证集类似

    # 当执行上面这两行代码时，程序并没有把所有训练和验证数据一次性全部加载到内存里。只做了一些初始化的工作：找到了所有相关文件的路径；加载了第一个文件来获取维度信息（_get_data_info）；计算出了总共有多少个样本（__len__）。
    # 真正的数据加载发生在 DataLoader 开始工作之后。由 DataLoader 来调用这个对象里面的函数或者方法。

    # ==================== 数据可视化验证 ====================
    print("\n" + "="*50)
    print("数据可视化验证")
    print("="*50)
    visualize_first_training_sample(train_dataset)
    print("="*50)
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True  # 功能：将 DataLoader 返回的张量放在锁页内存中；目的：加速数据从CPU到GPU的传输，提升训练效率。
    )
    # DataLoader 会根据batch_size（例如16）和shuffle = True，从train_dataset中随机挑选16个索引（比如[105, 23, 8, ...]）。然后，它会（可能使用多个后台进程num_workers）并行地调用train_dataset的__getitem__方法16次，分别传入这16个索引。
    # train_dataset根据这16个索引，分别从硬盘加载对应的.mat文件，处理数据，返回16个(stokes_window, bfs_target)数据对。
    # DataLoader将这16个数据对打包（collate）成一个批次（batch）。所有stokes_window会被堆叠成一个大的张量，形状为(16, 3, 5, 2000)（由于现在窗口大小是5，一段斯托克斯信号长度为200ns/0.5ns=400,0.5ns是仿真用的时间网格，所以维度是5*400=2000）。
    # 所有bfs_target会被堆叠成另一个大的张量，形状为(16, 3, 200)（这个200也是一段bfs的维度，是通过20m/0.1m=200得到的，0.1m是仿真用的空间网格）。
    # 最后，在训练循环中，for stokes, bfs_target in train_loader: 这一行代码就会产出这个打包好的批次。
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )
    
    return train_loader, val_loader


def train_epoch(model: nn.Module,  # 接收一个PyTorch模型
                train_loader: DataLoader,   # 接收一个数据加载器，提供训练数据
                criterion: nn.Module,   # 损失函数（MSE）
                optimizer: optim.Optimizer,   # 接收一个优化器（例如 optim.Adam），它知道如何根据损失来更新模型的权重
                device: torch.device,    # 接收一个设备对象（'cuda' 或 'cpu'），告诉函数在哪里进行计算
                epoch: int,  # 当前训练轮数
                writer: SummaryWriter = None,  # TensorBoard写入器
                smooth_lambda: float = 2e-4,  # 🔥 动态平滑性权重
                grad_alpha: float = 1.0,  # 🔥 动态梯度损失权重
                scheduler = None,  # 学习率调度器
                wavelet_criterion = None) -> float:  # 🔥 小波感知损失函数
    # -> float 声明这个函数最终会返回一个浮点数，即平均损失。
    # 注意这些参数都是从函数外部传入的，通常发生在 main() 函数中，train_epoch 函数本身被设计成一个高度模块化、可复用的组件。它只负责“执行一轮训练”这个核心任务，而不关心具体的模型、数据、损失函数或优化器（学习率在优化器中给出）是什么。

    """
    接收模型和训练数据，对整个训练数据集进行一次完整的遍历，更新模型的权重，并返回本次遍历的平均损失值。一个epoch代表完整遍历了所有样本。

    训练一个epoch

    返回：
    - 平均训练损失
    """
    model.train()  # 设置为训练模式，这会激活一些只在训练时使用的层，比如 Dropout 和 BatchNorm
    total_loss = 0.0  # 用于累加每个批次的损失，以便最后计算平均损失
    num_batches = 0  # 用于记录总共处理了多少个批次
    
    # 初始化梯度监测器
    grad_monitor = GradMonitor(win_size=100, std_factor=4.0, abs_clip=None)
    skipped_batch = 0

    # 使用tqdm显示进度条
    pbar = tqdm(train_loader, desc=f"训练中 Epoch {epoch+1}")  # tqdm 是一个进度条库。它会包装 train_loader，当遍历它时，会自动显示一个动态的进度条，看到训练进度
    
    for batch_idx, (stokes, bfs_target) in enumerate(pbar):  # 不断地从 train_loader 中获取一个批次（batch）的数据，直到整个数据集都被遍历完。
    # for 循环中变量的顺序和 __getitem__ 的返回顺序是严格对应的。由于在 __getitem__ 中返回的是 (输入, 标签)，所以在 for 循环中接收时，也必须写成 (输入变量, 标签变量) 的形式，例如 (stokes, bfs_target)。
    # 在BOTDADataset类中，定义了__getitem__方法的返回格式：  return (stokes_window, bfs_target)。明确规定了：返回一个元组（tuple），元组的第一个元素是输入信号 stokes_window，第二个元素是标签 bfs_target。
    # 这个顺序是自己定义的，它就是数据的“出生证明”。
    # 打包成批次(DataLoader)：
    # DataLoader在工作时，会多次调用__getitem__来获取一个批次的数据（比如16个）。它会得到16个像(stokes_1, bfs_1), (stokes_2, bfs_2), ..., (stokes_16, bfs_16)这样的数据对。然后，DataLoader它会把所有数据对的第一个元素（所有的stokes_window）收集起来，
    # 用torch.stack把它们堆叠成一个大的张量。它会把所有数据对的第二个元素（所有的bfs_target）收集起来，也堆叠成另一个大的张量。最后，DataLoader会产出（yield）一个包含这两个堆叠后的大张量的元组。
    # 解包赋值(for 循环)
    # 在训练循环中：for batch_idx, (stokes, bfs_target) in enumerate(pbar):这一行代码使用了Python的元组解包（Tuple Unpacking）语法，pbar（也就是 train_loader）产出了一个元组，这个元组里有两个大张量，
    # Python看到 (stokes, bfs_target) 这样的结构，就会自动地把元组的第一个元素赋值给变量 stokes，把元组的第二个元素赋值给变量 bfs_target。

        # 数据移到GPU
        stokes = stokes.to(device)  # (B, 3, W, L)
        bfs_target = bfs_target.to(device)  # (B, W-1, bfs_length)

        # 去掉训练时对标签的增强
        # # 数据增强：随机应用低通滤波
        # if random.random() < 0.5:
        #     # 将tensor转为numpy进行滤波，然后转回tensor
        #     bfs_target_np = bfs_target.cpu().numpy()
        #     for i in range(bfs_target_np.shape[0]):  # 对每个batch
        #         for j in range(bfs_target_np.shape[1]):  # 对每个段
        #             bfs_target_np[i, j] = gaussian_filter1d(bfs_target_np[i, j], sigma=1)
        #     bfs_target = torch.from_numpy(bfs_target_np).to(device)
        
        # 前向传播
        optimizer.zero_grad()  # 优化器梯度清零，否则梯度累加
        bfs_pred = model(stokes)  # (B, output_segments, space_points_per_segment)

        # 计算主损失
        main_loss = criterion(bfs_pred, bfs_target)  # (B,4,200) vs (B,4,200)

        # 添加平滑性损失 - 一阶导正则化
        def smoothness_loss(y, lam=2e-4):
            # y : (B,4,200)
            diff = y[..., 1:] - y[..., :-1]  # 计算相邻点的差值
            return lam * (diff**2).mean()

        # 🔥 动态梯度损失 - 支持分阶段权重调整
        def gradient_loss(y_pred, y_true, alpha=1.0):
            """
            梯度损失：确保预测的变化趋势与真实标签一致
            使用动态alpha值，支持分阶段训练策略
            """
            pred_grad = y_pred[..., 1:] - y_pred[..., :-1]  # 预测的梯度
            true_grad = y_true[..., 1:] - y_true[..., :-1]  # 真实的梯度
            return alpha * F.mse_loss(pred_grad, true_grad)

        # 🔥 使用动态权重计算各项损失
        smooth_loss = smoothness_loss(bfs_pred, smooth_lambda)
        grad_loss = gradient_loss(bfs_pred, bfs_target, alpha=grad_alpha)

        # 🔥 小波感知损失 - 解决MSE"和稀泥"问题
        wave_loss = wavelet_criterion(bfs_pred, bfs_target)

        # 总损失 = 主损失 + 动态权重的各项辅助损失
        loss = main_loss + smooth_loss + grad_loss + wave_loss
        
        # 反向传播计算梯度
        loss.backward()

        # 计算总梯度范数并进行异常检测
        grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), 0.05, error_if_nonfinite=False)
        grad_monitor.update(float(grad_norm))

        # 判断异常：检查loss是否有限且梯度是否异常
        anomaly = (not torch.isfinite(loss)) or grad_monitor.is_anomaly(float(grad_norm))
        if anomaly:
            skipped_batch += 1
            optimizer.zero_grad(set_to_none=True)  # 清掉异常梯度
            pbar.set_postfix({'跳过': skipped_batch, '梯度范数': f'{grad_norm:.2e}'})
            continue  # 不做 optimizer.step()，直接下一个 batch
        else:
            # 正常情况下更新权重参数
            optimizer.step()

        # CosineAnnealing不需要在每个batch后调用step()
        # 只有OneCycleLR需要在每个batch后调用，现在我们使用epoch级别的调度
        
        # 统计
        total_loss += loss.item()  # 将当前批次的损失累加到总损失中，最后得到所有样本的总的损失（即一个epoch的损失）   item()方法是将张量转化为数字
        num_batches += 1  # 批次计数次+1

        # TensorBoard日志记录
        if writer is not None:
            # global_step 是一个全局的时间戳，代表这是整个训练过程中的第几步
            global_step = epoch * len(train_loader) + batch_idx
            writer.add_scalar('Train/BatchLoss', loss.item(), global_step)
            # 在TensorBoard里创建一个名叫 Train 的折叠菜单，里面有一张图表叫 BatchLoss
            # loss.item(): “要记录的具体数值就是当前这个批次的损失值。”
            # global_step: “把这个数值记录在时间轴的 global_step 这个点上。”（图表的X轴）

            # 每100个batch记录一次学习率
            if batch_idx % 100 == 0:
                current_lr = optimizer.param_groups[0]['lr']
                writer.add_scalar('Train/LearningRate', current_lr, global_step)

        # 更新进度条
        pbar.set_postfix({'损失': f'{loss.item():.6f}'})
    
    return total_loss / num_batches  # 在for循环结束后，意味着整个所有训练样本都被完整遍历了一遍。用总损失除以总批次数，代表这个epoch的平均训练损失，并将其作为函数的返回值。


def validate_epoch(model: nn.Module,
                  val_loader: DataLoader,
                  criterion: nn.Module,
                  device: torch.device,
                  epoch: int,
                  writer: SummaryWriter = None,
                  smooth_lambda: float = 2e-4,  # 🔥 动态平滑性权重
                  grad_alpha: float = 1.0,  # 🔥 动态梯度损失权重
                  wavelet_criterion = None) -> float:  # 🔥 小波感知损失函数
    """
    判断模型是否过拟合，并决定是否要保存当前模型为“最佳模型”

    验证一个epoch
    
    返回：
    - 平均验证损失
    """
    # 设置模型为评估模式，会关闭一些只在训练时使用的层，比如：Dropout层，BN层: 在评估时，BN层会使用在整个训练过程中学习到的固定的均值和方差来进行归一化，而不是使用当前批次的均值和方差。
    model.eval()
    total_loss = 0.0
    num_batches = 0
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc="验证中")
        
        for stokes, bfs_target in pbar:
            # 数据移到GPU
            stokes = stokes.to(device)
            bfs_target = bfs_target.to(device)
            
            # 前向传播
            bfs_pred = model(stokes)  # (B, output_segments, space_points_per_segment)

            # 计算主损失
            main_loss = criterion(bfs_pred, bfs_target)  # (B,4,200) vs (B,4,200)

            # 添加平滑性损失（与训练保持一致）
            def smoothness_loss(y_pred, lam):
                diff = y_pred[..., 1:] - y_pred[..., :-1]
                return lam * (diff**2).mean()

            # 🔥 动态梯度损失（与训练保持一致）
            def gradient_loss(y_pred, y_true, alpha=1.0):
                pred_grad = y_pred[..., 1:] - y_pred[..., :-1]
                true_grad = y_true[..., 1:] - y_true[..., :-1]
                return alpha * F.mse_loss(pred_grad, true_grad)

            # 🔥 使用动态权重计算各项损失（与训练保持一致）
            smooth_loss = smoothness_loss(bfs_pred, smooth_lambda)
            grad_loss = gradient_loss(bfs_pred, bfs_target, alpha=grad_alpha)

            # 🔥 新增：小波感知损失（与训练保持一致）
            wave_loss = wavelet_criterion(bfs_pred, bfs_target)

            # 总损失（与训练保持一致）
            loss = main_loss + smooth_loss + grad_loss + wave_loss
            
            total_loss += loss.item()
            num_batches += 1
            
            pbar.set_postfix({'损失': f'{loss.item():.6f}'})

    avg_val_loss = total_loss / num_batches

    # TensorBoard日志记录
    if writer is not None:
        writer.add_scalar('Validation/Loss', avg_val_loss, epoch)

    return avg_val_loss


def save_checkpoint(model: nn.Module,   # 将训练过程中的关键信息打包并保存到一个文件中，以便将来可以从这个点恢复训练，或者加载训练好的模型进行推理。
                   optimizer: optim.Optimizer, 
                   epoch: int,   # 当前是第几轮训练
                   train_loss: float,   # 当前轮的训练损失
                   val_loss: float,   # 当前轮的验证损失
                   save_path: str):
    """
    保存模型检查点
    作用：
    1.中断与恢复
    2.保存最佳模型
    """
    # 创建一个字典
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),  # 保存了模型所有的可学习参数（即权重 weights 和偏置 biases）
        'optimizer_state_dict': optimizer.state_dict(),  # 保存了优化器的内部状态。对于像 Adam 这样的自适应优化器，它会保存每个参数的动量和二阶矩估计。保存这些状态可以让你在恢复训练时，优化器也能恢复到中断前的状态，使得训练过程更加平滑，而不是从头开始。
        # 保存当前轮的损失值
        'train_loss': train_loss,
        'val_loss': val_loss,
        'timestamp': datetime.now().isoformat()  # 记录保存检查点时的确切时间
    }
    torch.save(checkpoint, save_path)
    print(f"模型已保存到: {save_path}")  # 生成的文件通常以 .pt 或 .pth 作为扩展名


def main():
    """主训练函数"""
    # ==================== 配置参数 ====================
    # 数据配置
    DATA_DIR = "./SNR25_35_Dataset"  # 数据集所在目录
    WINDOW_SIZE = 5         # 窗口大小（段数）
    BATCH_SIZE = 32         # 批大小
    NUM_WORKERS = 8         # 数据加载进程数
    MAX_SAMPLES = None      # 最大样本数（None表示使用全部）
    
    # 模型配置 - 回到合理配置，专注解决几何对齐问题
    BASE_CHANNELS = 32      # 基础通道数

    # 滑窗配置
    STRIDE = WINDOW_SIZE - 1         # 滑窗步长（None=默认重叠滑窗，1=重叠，window_size-1=非重叠）
    
    # 训练配置
    MAX_EPOCHS = 400     # 最大训练轮数，增加训练轮数让模型充分收敛
    EARLY_STOP_PATIENCE = 25  # 早停耐心值，从开始训练就计数
    # 学习率策略调整 - 使用OneCycleLR
    MAX_LEARNING_RATE = 1e-3  # 最大学习率，比之前更大胆
    WEIGHT_DECAY = 5e-3       # 权重衰减，增强正则化效果
    
    # 保存配置
    SAVE_DIR = "./checkpoints"  # 保存相关结果，包括最佳模型
    os.makedirs(SAVE_DIR, exist_ok=True)  # 创建一个用于保存模型检查点的文件夹。exist_ok=True 表示如果文件夹已经存在，不要报错，直接跳过

    # TensorBoard配置 - 使用临时目录避免中文路径问题
    import tempfile
    TEMP_TB_DIR = os.path.join(tempfile.gettempdir(), "botda_tensorboard")
    TENSORBOARD_DIR = os.path.join(SAVE_DIR, "tensorboard")  # 备用路径
    TENSORBOARD_REL_PATH = "./checkpoints/tensorboard"  # 相对路径用于命令行

    # 尝试使用临时目录，如果失败则使用原路径
    try:
        os.makedirs(TEMP_TB_DIR, exist_ok=True)
        # 测试路径是否包含中文
        test_path = os.path.abspath(TEMP_TB_DIR)
        test_path.encode('ascii')  # 如果包含非ASCII字符会抛出异常
        TENSORBOARD_DIR = TEMP_TB_DIR
        TENSORBOARD_REL_PATH = TEMP_TB_DIR
        print(f"使用临时目录避免中文路径问题: {TENSORBOARD_DIR}")
    except (UnicodeEncodeError, UnicodeDecodeError):
        # 如果临时目录也有问题，使用原路径
        os.makedirs(TENSORBOARD_DIR, exist_ok=True)
        print(f"使用原路径: {TENSORBOARD_DIR}")
    except Exception:
        # 其他异常，使用原路径
        os.makedirs(TENSORBOARD_DIR, exist_ok=True)

    # 临时跳过TensorBoard初始化，避免TensorFlow冲突
    try:
        writer = SummaryWriter(log_dir=TENSORBOARD_DIR)
        print(f"TensorBoard日志保存到: {TENSORBOARD_DIR}")
        print(f"启动TensorBoard命令: tensorboard --logdir={TENSORBOARD_REL_PATH}")
    except Exception as e:
        print(f"TensorBoard初始化失败: {e}")
        print("跳过TensorBoard日志记录，继续训练...")
        writer = None
    
    # ==================== 设备配置 ====================
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # ==================== 数据加载 ====================
    print("加载数据...")
    train_loader, val_loader = create_data_loaders(
        DATA_DIR, WINDOW_SIZE, STRIDE, BATCH_SIZE, NUM_WORKERS, MAX_SAMPLES
    )
    
    print(f"训练样本数: {len(train_loader.dataset)}")
    print(f"验证样本数: {len(val_loader.dataset)}")
    
    # ==================== 模型创建 ====================
    print("创建模型...")
    # 实例化新的HybridUNetTCN模型
    # 从实际数据中获取维度参数
    print("从训练数据中获取实际维度参数...")

    try:  # 如果失败，则跳转到 except 部分
        sample_stokes, sample_bfs = train_loader.dataset[0]  # train_loader.dataset: 从 DataLoader 对象中“取回”最开始创建的那个 Dataset 对象（比如 BOTDADataset）；[0]: 直接调用 Dataset 对象的 __getitem__ 方法，并请求第一个样本
        # sample_stokes, sample_bfs = ...: __getitem__(0)会返回一个元组(斯托克斯信号, BFS标签)
        time_points_per_segment = sample_stokes.shape[1] // WINDOW_SIZE  # 五段斯托克斯信号一共的时间点数除以窗长，对应的就是每一段斯托克斯的时间点数
        # 修复：BFS标签现在是(4,200)形状，每段空间点数是第二个维度
        space_points_per_segment = sample_bfs.shape[1]  # 每段空间点数直接从第二个维度获取

        print(f"从数据中获取的维度参数:")
        print(f"  每段时间点数: {time_points_per_segment}")
        print(f"  每段空间点数: {space_points_per_segment}")
        print(f"  窗口大小: {WINDOW_SIZE}段")
        print(f"  输出段数: {WINDOW_SIZE - 1}段")
        print(f"  输入总长度: {WINDOW_SIZE * time_points_per_segment}")
        print(f"  输出总长度: {(WINDOW_SIZE - 1) * space_points_per_segment}")

    except Exception as e:
        print(f"无法从数据中获取维度: {e}")
        print("使用默认维度参数")
        time_points_per_segment = 400  # 默认值
        space_points_per_segment = 200  # 默认值

    model = HybridUNetTCN(
        input_freq_channels=3,
        window_segments=WINDOW_SIZE,
        time_points_per_segment=time_points_per_segment,
        output_segments=WINDOW_SIZE - 1,
        space_points_per_segment=space_points_per_segment,
        base_channels=BASE_CHANNELS,
        depth=4,  # 回到合理深度
        bottleneck_attn_heads=8  # 注意力头数要整除通道数
    ).to(device)

    # 计算模型参数数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数总数: {total_params:,}")
    print(f"可训练参数: {trainable_params:,}")

    # ==================== 添加网络架构图到TensorBoard ====================
    print("添加网络架构图到TensorBoard...")
    try:
        # 确保模型完全在正确的设备上
        model = model.to(device)

        # 创建一个示例输入
        dummy_input = torch.randn(1, 3, WINDOW_SIZE * time_points_per_segment).to(device)
        print(f"示例输入形状: {dummy_input.shape}")

        # 测试模型前向传播（在添加图之前）
        model.eval()
        with torch.no_grad():
            dummy_output = model(dummy_input)
            print(f"示例输出形状: {dummy_output.shape}")
        model.train()

        # 使用CPU进行图追踪（避免CUDA兼容性问题）
        model_cpu = model.cpu()
        dummy_input_cpu = dummy_input.cpu()

        # 添加详细的网络架构图
        if writer is not None:
            try:
                # 方法1：使用JIT追踪保存完整图结构
                traced_model = torch.jit.trace(model_cpu, dummy_input_cpu)
                writer.add_graph(traced_model, dummy_input_cpu)
                print("✅ 使用JIT追踪保存完整网络架构图")
            except Exception as e:
                print(f"JIT追踪失败，使用标准方法: {e}")
                # 方法2：标准方法，但设置详细模式
                writer.add_graph(model_cpu, dummy_input_cpu, verbose=True)
                print("✅ 使用标准方法保存网络架构图")

            # 添加模型结构的文本描述
            model_str = str(model_cpu)
            writer.add_text("Model Architecture", model_str.replace('\n', '<br>'), 0)

        # 添加模型参数统计
        total_params = sum(p.numel() for p in model_cpu.parameters())
        trainable_params = sum(p.numel() for p in model_cpu.parameters() if p.requires_grad)
        param_info = f"""
        <h3>模型参数统计</h3>
        <ul>
        <li>总参数数量: {total_params:,}</li>
        <li>可训练参数: {trainable_params:,}</li>
        <li>模型大小: {total_params * 4 / 1024 / 1024:.2f} MB (float32)</li>
        </ul>
        """
        if writer is not None:
            writer.add_text("Model Statistics", param_info, 0)

            # 添加每层的详细信息
            layer_info = "<h3>网络层详细信息</h3><table border='1'><tr><th>层名称</th><th>类型</th><th>参数数量</th><th>输出形状</th></tr>"

            # 使用hook来获取每层的输出形状
            layer_outputs = {}
            hooks = []

            def get_activation(name):
                def hook(*args):
                    output = args[2]  # 第三个参数是output
                    if isinstance(output, torch.Tensor):
                        layer_outputs[name] = str(tuple(output.shape))
                    elif isinstance(output, (list, tuple)):
                        shapes = []
                        for o in output:
                            if isinstance(o, torch.Tensor):
                                shapes.append(str(tuple(o.shape)))
                            else:
                                shapes.append(str(type(o).__name__))
                        layer_outputs[name] = f"[{', '.join(shapes)}]"
                    else:
                        layer_outputs[name] = str(type(output).__name__)
                return hook

            # 注册hooks到所有叶子节点
            for name, module in model_cpu.named_modules():
                if len(list(module.children())) == 0:  # 只对叶子节点注册hook
                    hooks.append(module.register_forward_hook(get_activation(name)))

            # 前向传播获取形状信息
            model_cpu.eval()
            with torch.no_grad():
                _ = model_cpu(dummy_input_cpu)

            # 移除hooks
            for hook in hooks:
                hook.remove()

            # 生成层信息表格
            for name, module in model_cpu.named_modules():
                if len(list(module.children())) == 0:  # 只显示叶子节点
                    param_count = sum(p.numel() for p in module.parameters())
                    module_type = type(module).__name__

                    # 尝试获取模块的基本信息
                    module_info = ""
                    if hasattr(module, 'in_features') and hasattr(module, 'out_features'):
                        module_info = f"({module.in_features} -> {module.out_features})"
                    elif hasattr(module, 'in_channels') and hasattr(module, 'out_channels'):
                        module_info = f"({module.in_channels} -> {module.out_channels})"
                    elif hasattr(module, 'num_features'):
                        module_info = f"({module.num_features})"

                    # 获取输出形状
                    output_shape = layer_outputs.get(name, "Unknown")

                    layer_info += f"<tr><td>{name}</td><td>{module_type} {module_info}</td><td>{param_count:,}</td><td>{output_shape}</td></tr>"

            layer_info += "</table>"
            writer.add_text("Layer Details", layer_info, 0)

            writer.flush()  # 强制刷新到磁盘

        # 将模型移回GPU
        model = model.to(device)

        # 再次刷新确保数据写入
        if writer is not None:
            writer.flush()

    except Exception as e:
        print(f"❌ 添加网络架构图失败: {e}")
        print("继续训练，跳过网络架构图...")
        # 确保模型在正确的设备上
        model = model.to(device)

    # ==================== 损失函数和优化器 ====================
    # 回归简单经典：使用MSE损失，集中火力提升模型拟合能力
    criterion = nn.MSELoss()

    # 🔥 新增：小波感知损失 - 解决MSE"和稀泥"问题，强化高频细节
    try:
        wavelet_criterion = MultiScaleWaveletLoss(
            wave='db4',           # Daubechies 4小波，适合信号处理
            level=3,              # 3层分解，适合200点长度
            loss_type='l1',       # L1损失，对异常值更鲁棒
            base_weight=0.05,     # 控制小波损失占总损失的5%
            decay=0.6,            # 权重衰减，让细节层权重更大
            include_lowpass=False # 只关注高频，不包含低频趋势
        ).to(device)
        print("✅ 小波感知损失初始化成功")
    except Exception as e:
        print(f"⚠️  小波损失初始化失败，使用频域损失替代: {e}")
        wavelet_criterion = FrequencyDomainLoss(
            weight=0.05,          # 与小波损失相同的权重
            high_freq_boost=3.0,  # 高频增强3倍
            loss_type='l1'
        ).to(device)

    # 🔥 注意：平滑性和梯度权重现在通过分阶段策略动态调整
    
    # 使用AdamW优化器，更好地处理权重衰减
    optimizer = optim.AdamW(
        model.parameters(),   # model.parameters() 返回模型中所有需要学习的参数，以便优化器进行参数更新
        lr=MAX_LEARNING_RATE,
        weight_decay=WEIGHT_DECAY,
        eps=1e-6              # 提升数值稳定性，避免半精度下溢
    )
    
    # 两段式学习率策略：前40%用CosineAnnealing，后60%用ReduceLROnPlateau
    cosine_epochs = int(MAX_EPOCHS * 0.4)  # 前40%的epoch

    # 第一阶段：CosineAnnealing (1e-3 → 1e-4)
    scheduler_cosine = optim.lr_scheduler.CosineAnnealingLR(
        optimizer,
        T_max=cosine_epochs,
        eta_min=1e-4  # 最小学习率
    )

    # 第二阶段：ReduceLROnPlateau（监控val_loss）
    scheduler_plateau = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer,
        mode='min',
        factor=0.5,
        patience=5,
        min_lr=1e-8
    )
    # optim.lr_scheduler是PyTorch中专门负责学习率策略的模块；ReduceLROnPlateau 的作用是“当某个指标不再改善时，降低学习率”。
    # mode = 'min'：让监控的指标越小越好。mode 可以是 'min' 或 'max'，但由于后面会用验证损失（val_loss）来评估模型，而损失是越小越好，所以这里设置为 'min'。
    # factor = 0.5：表示每次触发时，学习率都会乘以0.5。
    # patience = 10: 如果连续10个epoch，val_loss（通过后面scheduler.step(val_loss)这行代码知道传入的是val_loss）一直没有降到比之前的最低点更低，就触发学习率降低。
    # verbose=True：verbose（详细信息）设置为True后，每当学习率被降低时，它都会在控制台打印一条消息，方便监控训练过程。

    # ==================== 训练循环 ====================
    print("开始训练...")

    # 记录训练开始时间
    training_start_time = time.time()

    best_val_loss = float('inf')  # 首先设置最佳的验证损失为无穷大，方便后续更新
    train_losses = []
    val_losses = []
    learning_rates = []  # 记录学习率变化
    early_stop_counter = 0  # 早停计数器
    best_epoch = 0  # 记录最佳模型的epoch

    for epoch in range(MAX_EPOCHS):  # 取值为0~MAX_EPOCHS-1
        print(f"\n========== Epoch {epoch+1}/{MAX_EPOCHS} ==========")

        # 🔥 线性渐变权重策略：避免第50个epoch的损失激增
        RAMP_START = 40          # 提前10个epoch开始渐变
        RAMP_LEN = 20            # 40~59 epoch线性过渡，避免突变

        def ramp(old_val, new_val, current_epoch):
            """线性插值函数：平滑过渡权重"""
            if current_epoch < RAMP_START:
                return old_val
            if current_epoch >= RAMP_START + RAMP_LEN:
                return new_val
            # 线性插值
            t = (current_epoch - RAMP_START) / RAMP_LEN  # 0~1
            return old_val + t * (new_val - old_val)

        # 计算当前epoch的平滑权重
        grad_alpha = ramp(0.5, 1.5, epoch)           # 梯度权重平滑过渡
        smooth_lambda = ramp(2e-4, 1e-4, epoch)      # 平滑权重平滑过渡
        wave_base_weight = ramp(0.01, 0.15, epoch)   # 小波权重平滑过渡

        # 确定当前阶段名称
        if epoch < RAMP_START:
            stage_name = "宏观结构学习"
        elif epoch < RAMP_START + RAMP_LEN:
            stage_name = f"权重渐变过渡 ({epoch-RAMP_START+1}/{RAMP_LEN})"
        else:
            stage_name = "细节精雕"

        # 更新小波损失权重
        if hasattr(wavelet_criterion, 'update_weight'):
            wavelet_criterion.update_weight(wave_base_weight)

        print(f"🎯 训练阶段: {stage_name}")
        print(f"   梯度权重: {grad_alpha:.3f}, 平滑权重: {smooth_lambda:.1e}, 小波权重: {wave_base_weight:.4f}")

        # 显示渐变进度
        if RAMP_START <= epoch < RAMP_START + RAMP_LEN:
            progress = (epoch - RAMP_START) / RAMP_LEN * 100
            print(f"   🔄 渐变进度: {progress:.1f}% (避免权重突变)")

        # 记录权重变化到TensorBoard
        if writer is not None:
            writer.add_scalar('Weights/Gradient_Alpha', grad_alpha, epoch)
            writer.add_scalar('Weights/Smooth_Lambda', smooth_lambda, epoch)
            writer.add_scalar('Weights/Wavelet_Base', wave_base_weight, epoch)
            writer.add_text('Training/Stage', stage_name, epoch)

            # 记录渐变过程
            if RAMP_START <= epoch < RAMP_START + RAMP_LEN:
                progress = (epoch - RAMP_START) / RAMP_LEN
                writer.add_scalar('Weights/Transition_Progress', progress, epoch)
                writer.add_scalar('Training/In_Transition', 1.0, epoch)
            else:
                writer.add_scalar('Training/In_Transition', 0.0, epoch)

        # 确定当前使用的调度器
        current_scheduler = scheduler_cosine if epoch < cosine_epochs else None

        # 训练
        train_loss = train_epoch(model, train_loader, criterion, optimizer, device, epoch, writer, smooth_lambda, grad_alpha, current_scheduler, wavelet_criterion)  # 返回一个epoch的平均训练损失

        # 验证
        val_loss = validate_epoch(model, val_loader, criterion, device, epoch, writer, smooth_lambda, grad_alpha, wavelet_criterion)  # 返回一个epoch的平均验证损失

        # 两段式学习率调度
        if epoch < cosine_epochs:
            # 第一阶段：CosineAnnealing
            scheduler_cosine.step()
        else:
            # 第二阶段：ReduceLROnPlateau
            scheduler_plateau.step(val_loss)
        # 把刚刚计算出来的当前轮的验证损失val_loss作为参数传递给了scheduler.step()
        # scheduler接收到这个val_loss值后，就会在内部进行判断：
        # 接收到了一个新的指标值val_loss。目标是让这个值越小越好(mode='min')。
        # 这个新的val_loss是否比记录的历史最低值还要低？
        # 如果是：则更新历史最低记录，并重置“耐心计数器”。
        # 如果不是：耐心计数器+1。
        # 耐心计数器是否达到10(patience=10)了吗
        # 如果达到了：说明已经连续10轮没有进步了，进入了平台期，那么应该降低学习率了，会让optimizer的学习率乘以0.5(factor=0.5)，并重置“耐心计数器”。
        # 如果没达到：则再等等，先不改变学习率。
        
        # 记录损失和学习率
        train_losses.append(train_loss)
        val_losses.append(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        learning_rates.append(current_lr)

        # TensorBoard记录epoch级别的指标
        if writer is not None:
            writer.add_scalar('Epoch/TrainLoss', train_loss, epoch)
            writer.add_scalar('Epoch/ValLoss', val_loss, epoch)
            writer.add_scalar('Epoch/LearningRate', optimizer.param_groups[0]['lr'], epoch)

        print(f"训练损失: {train_loss:.6f}")
        print(f"验证损失: {val_loss:.6f}")
        print(f"当前学习率: {optimizer.param_groups[0]['lr']:.2e}")
        
        # 🔥 智能早停：权重过渡期间暂停早停判断
        in_transition = RAMP_START <= epoch < RAMP_START + RAMP_LEN

        if in_transition:
            # 过渡期内：直接更新best_val_loss，避开权重突变的影响
            best_val_loss = min(best_val_loss, val_loss)
            early_stop_counter = 0  # 重置计数器，避免误触发
            print(f"🔄 权重过渡期，暂停早停判断 (当前损失: {val_loss:.6f})")

            # 如果是过渡期的最佳模型，仍然保存
            if val_loss <= best_val_loss:
                best_epoch = epoch
                best_model_path = os.path.join(SAVE_DIR, "best_model.pth")
                save_checkpoint(model, optimizer, epoch, train_loss, val_loss, best_model_path)
                print(f"过渡期最佳模型！验证损失: {val_loss:.6f}")
        else:
            # 非过渡期：正常的早停逻辑
            if val_loss < best_val_loss:
                best_val_loss = val_loss
                best_epoch = epoch
                early_stop_counter = 0  # 重置早停计数器
                best_model_path = os.path.join(SAVE_DIR, "best_model.pth")
                save_checkpoint(model, optimizer, epoch, train_loss, val_loss, best_model_path)
                print(f"新的最佳模型！验证损失: {val_loss:.6f}")
            else:
                early_stop_counter += 1  # 验证损失没有改善，计数器+1
                print(f"验证损失未改善，早停计数: {early_stop_counter}/{EARLY_STOP_PATIENCE}")

            # 早停检查（仅在非过渡期）
            if early_stop_counter >= EARLY_STOP_PATIENCE:
                print(f"\n早停触发！连续{EARLY_STOP_PATIENCE}轮验证损失未改善")
                print(f"最佳验证损失: {best_val_loss:.6f} (第{best_epoch+1}轮)")
                break

        # 定期保存检查点和图片
        # 每20轮保存一下训练进度，若程序中断，则可以从最近的训练进度恢复
        if (epoch + 1) % 20 == 0:
            checkpoint_path = os.path.join(SAVE_DIR, f"checkpoint_epoch_{epoch+1}.pth")
            save_checkpoint(model, optimizer, epoch, train_loss, val_loss, checkpoint_path)

        # 每10个epoch保存loss和学习率图片
        if (epoch + 1) % 10 == 0:
            save_training_plots(train_losses, val_losses, learning_rates, epoch + 1, SAVE_DIR)
    
    # 计算总训练时间
    training_end_time = time.time()
    total_training_time = training_end_time - training_start_time
    hours = int(total_training_time // 3600)
    minutes = int((total_training_time % 3600) // 60)
    seconds = int(total_training_time % 60)

    # ==================== 保存训练历史 ====================
    # 所有epoch循环完之后，保存下每次epoch训练得到的训练损失和验证损失，保存为json格式
    history = {
        'train_losses': train_losses,
        'val_losses': val_losses,
        'learning_rates': learning_rates,
        'best_val_loss': best_val_loss,
        'best_epoch': best_epoch,
        'total_epochs': len(train_losses),
        'total_training_time_seconds': total_training_time
    }

    history_path = os.path.join(SAVE_DIR, "training_history.json")
    with open(history_path, 'w') as f:
        json.dump(history, f, indent=2)  # indent=2：表示在生成JSON文本时，使用2个空格来进行缩进，让数据的层级结构一目了然

    # ==================== 绘制最终训练曲线 ====================
    print("绘制最终训练损失和学习率曲线...")

    # 保存最终的训练图片
    final_epoch = len(train_losses)
    save_training_plots(train_losses, val_losses, learning_rates, final_epoch, SAVE_DIR)

    # 保持原有的详细绘图代码
    plt.figure(figsize=(12, 8))

    epochs_range = range(1, len(train_losses) + 1)

    # 绘制损失曲线
    plt.plot(epochs_range, train_losses, 'b-', label='Training Loss', linewidth=2)
    plt.plot(epochs_range, val_losses, 'r-', label='Validation Loss', linewidth=2)

    # 标记最佳模型点
    plt.plot(best_epoch + 1, best_val_loss, 'go', markersize=10, label=f'Best Model (Epoch {best_epoch+1})')

    plt.xlabel('Epoch', fontsize=12)
    plt.ylabel('Loss', fontsize=12)
    plt.title('BOTDA Super-Resolution Network Training Loss Curve', fontsize=14, fontweight='bold')
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)

    # 添加统计信息
    plt.text(0.02, 0.98, f'Total Training Time: {hours:02d}:{minutes:02d}:{seconds:02d}\n'
                         f'Total Epochs: {len(train_losses)}\n'
                         f'Best Validation Loss: {best_val_loss:.6f}\n'
                         f'Best Model Epoch: {best_epoch+1}',
             transform=plt.gca().transAxes, fontsize=10,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    # 保存损失曲线图
    loss_curve_path = os.path.join(SAVE_DIR, "training_loss_curve.png")
    plt.savefig(loss_curve_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"\n训练完成！")
    print(f"总训练时间: {hours:02d}小时{minutes:02d}分钟{seconds:02d}秒")
    print(f"总训练轮数: {len(train_losses)}")
    print(f"最佳验证损失: {best_val_loss:.6f} (第{best_epoch+1}轮)")
    print(f"模型和历史已保存到: {SAVE_DIR}")
    print(f"损失曲线已保存到: {loss_curve_path}")

    # 关闭TensorBoard writer
    if writer is not None:
        writer.close()
        print(f"TensorBoard日志已保存，可使用以下命令查看:")
        print(f"tensorboard --logdir={TENSORBOARD_REL_PATH}")
    else:
        print("TensorBoard未启用，跳过日志保存")


if __name__ == "__main__":
    main()
