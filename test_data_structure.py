# ---------------------------------------------------------------
#  测试MATLAB数据结构
#  
#  验证stokes_signals的实际结构是(num_segments, 1)还是(1, num_segments)
# ---------------------------------------------------------------

import os
import glob
import scipy.io as sio

def test_matlab_data_structure(data_dir="./DL_DATA"):
    """测试MATLAB数据的实际结构"""
    
    print("=" * 60)
    print("MATLAB数据结构测试")
    print("=" * 60)
    
    # 查找训练数据文件
    pattern = os.path.join(data_dir, "train", "train_sample_*.mat")
    file_paths = sorted(glob.glob(pattern))
    
    if len(file_paths) == 0:
        print(f"❌ 在 {pattern} 未找到数据文件")
        print("请确保数据目录路径正确")
        return
    
    print(f"找到 {len(file_paths)} 个训练样本")
    print(f"测试文件: {os.path.basename(file_paths[0])}")
    
    try:
        # 加载第一个文件
        sample_data = sio.loadmat(file_paths[0])['sample_data']
        print(f"✅ 成功加载数据文件")
        
        # 提取斯托克斯信号
        stokes_signals = sample_data['stokes_signals'][0, 0]
        
        print(f"\n📊 数据结构分析:")
        print(f"stokes_signals类型: {type(stokes_signals)}")
        print(f"stokes_signals形状: {stokes_signals.shape}")
        print(f"stokes_signals数据类型: {stokes_signals.dtype}")
        
        # 分析结构
        rows, cols = stokes_signals.shape
        print(f"\n🔍 结构分析:")
        print(f"行数: {rows}")
        print(f"列数: {cols}")
        
        if rows > cols:
            print(f"✅ 判断: (num_segments, 1) 结构 - 段在行方向")
            structure_type = "row_major"
            num_segments = rows
        else:
            print(f"✅ 判断: (1, num_segments) 结构 - 段在列方向")
            structure_type = "col_major"
            num_segments = cols
        
        print(f"段数量: {num_segments}")
        
        # 测试数据访问
        print(f"\n🧪 数据访问测试:")
        
        valid_segments = []
        for i in range(num_segments):
            try:
                if structure_type == "row_major":
                    segment_data = stokes_signals[i, 0]
                else:
                    segment_data = stokes_signals[0, i]
                
                if hasattr(segment_data, 'size') and segment_data.size > 0:
                    valid_segments.append(i)
                    if len(valid_segments) <= 3:  # 只显示前3个
                        print(f"  段{i}: 形状{segment_data.shape}, 大小{segment_data.size}")
                        
            except Exception as e:
                print(f"  段{i}: 访问失败 - {e}")
        
        print(f"\n📈 统计信息:")
        print(f"有效段数: {len(valid_segments)}")
        print(f"有效段索引: {valid_segments}")
        
        # 检查第一个有效段的详细信息
        if valid_segments:
            first_valid_idx = valid_segments[0]
            if structure_type == "row_major":
                first_segment = stokes_signals[first_valid_idx, 0]
            else:
                first_segment = stokes_signals[0, first_valid_idx]
            
            print(f"\n🔬 第一个有效段详细信息:")
            print(f"段索引: {first_valid_idx}")
            print(f"数据形状: {first_segment.shape}")
            print(f"数据类型: {first_segment.dtype}")
            
            if len(first_segment.shape) == 2:
                time_points, freq_channels = first_segment.shape
                print(f"时间点数: {time_points}")
                print(f"频率通道数: {freq_channels}")
                
                # 显示一些样本数据
                print(f"数据范围: [{first_segment.min():.6f}, {first_segment.max():.6f}]")
                print(f"数据均值: {first_segment.mean():.6f}")
        
        # 测试BFS标签
        print(f"\n🎯 BFS标签测试:")
        bfs_label = sample_data['frequency_shift_distribution_normalized'][0, 0]
        print(f"BFS标签形状: {bfs_label.shape}")
        print(f"BFS标签长度: {len(bfs_label.flatten())}")
        print(f"BFS标签范围: [{bfs_label.min():.6f}, {bfs_label.max():.6f}]")
        
        # 返回结构信息
        return {
            'structure_type': structure_type,
            'shape': stokes_signals.shape,
            'num_segments': num_segments,
            'valid_segments': valid_segments,
            'time_points': first_segment.shape[0] if valid_segments else 0,
            'freq_channels': first_segment.shape[1] if valid_segments else 0,
            'bfs_length': len(bfs_label.flatten())
        }
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return None

def test_multiple_files(data_dir="./DL_DATA", max_files=3):
    """测试多个文件的数据结构一致性"""
    
    print(f"\n{'='*60}")
    print("多文件一致性测试")
    print(f"{'='*60}")
    
    pattern = os.path.join(data_dir, "train", "train_sample_*.mat")
    file_paths = sorted(glob.glob(pattern))[:max_files]
    
    if len(file_paths) == 0:
        print("❌ 未找到测试文件")
        return
    
    structures = []
    
    for i, file_path in enumerate(file_paths):
        print(f"\n测试文件 {i+1}: {os.path.basename(file_path)}")
        
        try:
            sample_data = sio.loadmat(file_path)['sample_data']
            stokes_signals = sample_data['stokes_signals'][0, 0]
            
            shape = stokes_signals.shape
            structure_type = "row_major" if shape[0] > shape[1] else "col_major"
            
            structures.append({
                'file': os.path.basename(file_path),
                'shape': shape,
                'type': structure_type
            })
            
            print(f"  形状: {shape}, 类型: {structure_type}")
            
        except Exception as e:
            print(f"  ❌ 加载失败: {e}")
    
    # 检查一致性
    if structures:
        first_type = structures[0]['type']
        first_shape = structures[0]['shape']
        
        all_consistent = all(s['type'] == first_type and s['shape'] == first_shape 
                           for s in structures)
        
        if all_consistent:
            print(f"\n✅ 所有文件结构一致: {first_type}, 形状: {first_shape}")
        else:
            print(f"\n❌ 文件结构不一致!")
            for s in structures:
                print(f"  {s['file']}: {s['type']}, {s['shape']}")

if __name__ == "__main__":
    # 测试数据结构
    result = test_matlab_data_structure()
    
    if result:
        print(f"\n{'='*60}")
        print("测试结果总结")
        print(f"{'='*60}")
        print(f"数据结构类型: {result['structure_type']}")
        print(f"stokes_signals形状: {result['shape']}")
        print(f"段数量: {result['num_segments']}")
        print(f"有效段数: {len(result['valid_segments'])}")
        print(f"时间点数: {result['time_points']}")
        print(f"频率通道数: {result['freq_channels']}")
        print(f"BFS标签长度: {result['bfs_length']}")
        
        # 给出代码修改建议
        print(f"\n💡 代码修改建议:")
        if result['structure_type'] == 'row_major':
            print("✅ 当前修改正确 - 使用 stokes_signals[seg_idx, 0] 访问")
        else:
            print("✅ 原始代码正确 - 使用 stokes_signals[0, seg_idx] 访问")
    
    # 测试多文件一致性
    test_multiple_files()
    
    print(f"\n{'='*60}")
    print("测试完成!")
    print(f"{'='*60}")
