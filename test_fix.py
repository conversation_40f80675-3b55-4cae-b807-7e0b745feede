"""
测试修复后的模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

# 简化版的模型，只保留关键结构
class SimpleUNet(nn.Module):
    def __init__(self, input_channels=3, base_channels=64, depth=4):
        super().__init__()
        
        # 初始层
        self.stem = nn.Conv1d(input_channels, base_channels, kernel_size=3, padding=1)
        
        # 编码器
        self.encoder_blocks = nn.ModuleList()
        ch = base_channels
        for i in range(depth):
            block = nn.Sequential(
                nn.Conv1d(ch, ch*2, kernel_size=3, padding=1),
                nn.ReLU(),
                nn.MaxPool1d(kernel_size=2, stride=2)
            )
            self.encoder_blocks.append(block)
            ch *= 2
        
        # 瓶颈层
        self.bottleneck = nn.Conv1d(ch, ch, kernel_size=3, padding=1)
        
        # 解码器
        self.decoder_blocks = nn.ModuleList()
        for i in reversed(range(depth)):
            block = nn.Sequential(
                nn.ConvTranspose1d(ch, ch // 2, kernel_size=2, stride=2),
                nn.Conv1d(ch // 2, ch // 2, kernel_size=3, padding=1),
                nn.ReLU()
            )
            self.decoder_blocks.append(block)
            ch //= 2
        
        # 输出层
        self.head = nn.Conv1d(base_channels, 1, kernel_size=1)
    
    def forward(self, x):
        # 初始层
        x = self.stem(x)
        
        # 编码器
        skips = []
        for block in self.encoder_blocks:
            x = block(x)
            skips.append(x)
        
        # 瓶颈层
        x = self.bottleneck(x)
        
        # 解码器 - 原始版本（有问题）
        for block, skip in zip(self.decoder_blocks, reversed(skips)):
            x = block(x)
            # 这里会出现通道数不匹配的问题
            x = x + skip  # 错误：通道数不匹配
        
        # 输出层
        x = self.head(x)
        return x

# 修复版本
class FixedUNet(nn.Module):
    def __init__(self, input_channels=3, base_channels=64, depth=4):
        super().__init__()
        
        # 初始层
        self.stem = nn.Conv1d(input_channels, base_channels, kernel_size=3, padding=1)
        
        # 编码器
        self.encoder_blocks = nn.ModuleList()
        ch = base_channels
        for i in range(depth):
            block = nn.Sequential(
                nn.Conv1d(ch, ch*2, kernel_size=3, padding=1),
                nn.ReLU(),
                nn.MaxPool1d(kernel_size=2, stride=2)
            )
            self.encoder_blocks.append(block)
            ch *= 2
        
        # 瓶颈层
        self.bottleneck = nn.Conv1d(ch, ch, kernel_size=3, padding=1)
        
        # 解码器
        self.decoder_blocks = nn.ModuleList()
        for i in reversed(range(depth)):
            block = nn.Sequential(
                nn.ConvTranspose1d(ch, ch // 2, kernel_size=2, stride=2),
                nn.Conv1d(ch // 2, ch // 2, kernel_size=3, padding=1),
                nn.ReLU()
            )
            self.decoder_blocks.append(block)
            ch //= 2
        
        # 输出层
        self.head = nn.Conv1d(base_channels, 1, kernel_size=1)
    
    def forward(self, x):
        # 初始层
        x = self.stem(x)
        
        # 编码器
        skips = []
        for block in self.encoder_blocks:
            x = block(x)
            skips.append(x)
        
        # 瓶颈层
        x = self.bottleneck(x)
        
        # 解码器 - 修复版本
        for block, skip in zip(self.decoder_blocks, reversed(skips)):
            x = block(x)
            
            # 处理通道数不匹配问题
            if skip.size(1) != x.size(1):
                # 使用1x1卷积调整通道数
                skip = nn.Conv1d(skip.size(1), x.size(1), kernel_size=1).to(x.device)(skip)
            
            x = x + skip  # 现在通道数匹配了
        
        # 输出层
        x = self.head(x)
        return x

# 测试函数
def test_models():
    # 创建输入数据
    batch_size = 2
    input_channels = 3
    seq_length = 1024
    x = torch.randn(batch_size, input_channels, seq_length)
    
    print("测试原始模型（预期会失败）...")
    try:
        model = SimpleUNet(input_channels=input_channels)
        output = model(x)
        print("原始模型成功运行，输出形状:", output.shape)
    except RuntimeError as e:
        print("原始模型失败，错误:", e)
    
    print("\n测试修复后的模型...")
    try:
        model = FixedUNet(input_channels=input_channels)
        output = model(x)
        print("修复后的模型成功运行，输出形状:", output.shape)
    except RuntimeError as e:
        print("修复后的模型失败，错误:", e)

if __name__ == "__main__":
    test_models()
