%% ========================================================================
%% MATLAB代码修改指导 - 在erjp_tsyigsui_200m.m中添加以下代码块
%% ========================================================================

%% 🔥 在erjp_tsyigsui_200m.m文件开头添加以下代码块
%% 用于读取Python测试代码生成的测试样本并进行反归一化

% -------------------------------------------------------------------------
% 读取Python生成的测试样本数据
% -------------------------------------------------------------------------
fprintf('正在读取Python生成的测试样本...\n');

% 1. 读取测试样本的真实BFS分布（已反归一化）
python_data_path = 'results/bfs_data_file_0.npz';  % 根据实际路径调整
if exist(python_data_path, 'file')
    % 使用Python读取.npz文件并保存为.mat格式
    % 需要先在Python中运行以下代码转换格式：
    % import numpy as np
    % import scipy.io as sio
    % data = np.load('results/bfs_data_file_0.npz')
    % sio.savemat('results/bfs_data_file_0.mat', {
    %     'target': data['target'],
    %     'prediction': data['prediction'],
    %     'bfs_min': data['bfs_min'],
    %     'bfs_max': data['bfs_max']
    % })
    
    % 读取转换后的.mat文件
    mat_data_path = 'results/bfs_data_file_0.mat';
    if exist(mat_data_path, 'file')
        loaded_data = load(mat_data_path);
        true_bfs_distribution = loaded_data.target;  % 真实BFS分布
        fprintf('✅ 成功读取真实BFS分布，尺寸: %s\n', mat2str(size(true_bfs_distribution)));
        
        % 展平为一维数组
        if ndims(true_bfs_distribution) > 1
            true_bfs_flat = reshape(true_bfs_distribution, [], 1);
        else
            true_bfs_flat = true_bfs_distribution(:);
        end
        
        % 使用读取的真实BFS分布替换原有的BFS计算
        % 注意：需要确保长度匹配
        fiber_length = 200;  % 光纤总长度(m)
        num_points = length(true_bfs_flat);
        z_positions = linspace(0, fiber_length, num_points);  % 空间位置
        
        % 将真实BFS分布赋值给MATLAB变量
        bfs_distribution = true_bfs_flat;  % 这将替换原有的BFS计算结果
        
        fprintf('✅ BFS分布数据准备完成，长度: %d 点\n', length(bfs_distribution));
    else
        error('❌ 未找到转换后的.mat文件: %s', mat_data_path);
    end
else
    error('❌ 未找到Python生成的数据文件: %s', python_data_path);
end

% -------------------------------------------------------------------------
% 替换原有的BFS计算部分
% -------------------------------------------------------------------------
% 注释掉或删除原有的BFS计算代码，使用上面读取的数据
% 例如，如果原代码中有类似以下的计算：
% bfs_distribution = some_calculation_function(data);
% 
% 请将其替换为：
% bfs_distribution = true_bfs_flat;  % 使用从Python读取的真实BFS分布

%% ========================================================================
%% 🔥 在原有的绘图部分（figure(10)）后添加数据保存代码
%% ========================================================================

% 在 figure(10) 绘制完成后，添加以下保存代码：

% -------------------------------------------------------------------------
% 保存MATLAB传统方法的BFS测量结果
% -------------------------------------------------------------------------
fprintf('正在保存MATLAB传统方法的BFS测量结果...\n');

% 确保results目录存在
if ~exist('results', 'dir')
    mkdir('results');
end

% 保存测量结果（不包括理论结果）
save_path = 'results/matlab_bfs_measurement.mat';
save(save_path, 'bfs_distribution', 'z_positions', '-v7.3');
fprintf('✅ MATLAB BFS测量结果已保存: %s\n', save_path);

% 同时保存为CSV格式，方便Python读取
csv_path = 'results/matlab_bfs_measurement.csv';
measurement_data = [z_positions(:), bfs_distribution(:)];
writematrix(measurement_data, csv_path);
fprintf('✅ MATLAB BFS测量结果已保存为CSV: %s\n', csv_path);

% 显示保存的数据统计信息
fprintf('保存的数据统计:\n');
fprintf('  - 空间点数: %d\n', length(z_positions));
fprintf('  - BFS范围: [%.3f, %.3f] MHz\n', min(bfs_distribution), max(bfs_distribution));
fprintf('  - 平均BFS: %.3f MHz\n', mean(bfs_distribution));

%% ========================================================================
%% 使用说明
%% ========================================================================
% 
% 1. 将上述代码块添加到 erjp_tsyigsui_200m.m 文件的适当位置
% 2. 在Python中先运行以下转换代码：
%    
%    import numpy as np
%    import scipy.io as sio
%    
%    # 读取Python测试结果
%    data = np.load('results/bfs_data_file_0.npz')
%    
%    # 转换为MATLAB格式
%    sio.savemat('results/bfs_data_file_0.mat', {
%        'target': data['target'],
%        'prediction': data['prediction'], 
%        'bfs_min': data['bfs_min'],
%        'bfs_max': data['bfs_max']
%    })
%    
% 3. 然后运行修改后的MATLAB代码
% 4. 最后运行Python对比分析代码
%
