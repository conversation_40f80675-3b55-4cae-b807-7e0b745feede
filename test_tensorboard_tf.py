#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TensorBoard和TensorFlow集成
"""

import torch
import numpy as np
from torch.utils.tensorboard import SummaryWriter
import os

def test_tensorboard_with_tensorflow():
    """测试TensorBoard是否能正确识别TensorFlow"""
    
    print("测试TensorBoard和TensorFlow集成...")
    
    # 创建测试目录
    test_dir = "./test_tensorboard_tf"
    os.makedirs(test_dir, exist_ok=True)
    
    # 创建SummaryWriter
    writer = SummaryWriter(log_dir=test_dir)
    
    # 写入一些测试数据
    for i in range(10):
        # 模拟损失值
        train_loss = 1.0 / (i + 1) + np.random.normal(0, 0.1)
        val_loss = 1.2 / (i + 1) + np.random.normal(0, 0.05)
        
        writer.add_scalar('Loss/Train', train_loss, i)
        writer.add_scalar('Loss/Validation', val_loss, i)
        
        # 添加学习率
        lr = 0.001 * (0.9 ** (i // 3))
        writer.add_scalar('Learning_Rate', lr, i)
    
    # 添加一个简单的图像
    dummy_img = torch.randn(3, 64, 64)
    writer.add_image('Test_Image', dummy_img, 0)
    
    writer.close()
    
    print(f"TensorBoard日志已保存到: {test_dir}")
    print(f"启动TensorBoard命令: tensorboard --logdir={test_dir}")
    print("如果没有看到'TensorFlow installation not found'警告，说明集成成功！")

if __name__ == "__main__":
    test_tensorboard_with_tensorflow()
