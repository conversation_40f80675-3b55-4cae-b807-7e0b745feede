%% 分布式布里渊光纤传感仿真（二阶声场）- 深度学习数据生成完整版（消除边界伪影）
% 段长20m（由探测脉冲200ns决定）；窗长5m（由泵浦脉冲50ns决定）；分区长度100m；光纤长度200m（快速验证）

% 由于采用分区计算，因此在计算每个区的最后一段时，仿真中以为这里就是光纤的末端，然后导致此分区最后一段计算出来的斯托克斯信号与当前分区其它段计算出来的不一样（假设布里渊频移均匀的情况下），
% 10.8GHz的斯托克斯信号在最后50ns有一个下降，而10.73和10.87GHz的信号在最后50ns有一个轻微的振荡，其余段的信号在最后50ns都是平稳的。
% 所以在这里把分区进行扩展，即左边端点-1.5*泵浦长度，右边端点+1.5*泵浦长度，这样就避免了上面这个问题。

% 扩展分区计算时，对于0-100m会扩展为0-115m，当100-115m存在频移时，对于第5段80-100m测量出来的斯托克斯信号也会有影响，最后会有一点振荡，因为实际上泵浦和探测脉冲在100m后面其实还是有点重合的，
% 并不是在100m外面两者就分开了；这并不是扩展分区这个方法造成的，因为假如当靠近100m外没有频移的时候，第5段80-100m测量出来的斯托克斯信号最后是平稳的（没有使用扩展分区的时候，不管100m外有没有频移，这里都会有振荡）
% 对于100-200m，会扩展为85-200m，当85-100m这一部分有频移时，对于第6段100-120m测量出来的斯托克斯信号是没有影响的，因为在100m这里两个脉冲确实就分开了，
% 所以85-100m这部分的频移影响不到第6段，不像上面，两个脉冲在测量第5段80-100m的时候，在100m其实还是没有分开的

% 由于最后一段就是光纤的末端，这部分没办法用扩展分区的方法消除伪影，所以保存数据和展示的时候直接忽略了最后一段

% 即便是频移范围区域相同、频移大小也一样，但如果处于段的不同位置，那么这个段的斯托克斯信号都会不一样，
% 例如100-120m段，有一个101-103m、大小为10MHz的频移，160-180段，有一个164-166m、大小同样为10MHz的频移，这两个段出来的斯托克斯曲线是不一样的
% 原因：应变区偏离泵浦/探测"最佳重叠"位置（增益的 PSF 近似三角形，离中心 1–2 m 就会掉好几 dB）。计算相遇位置：
% meeting_position = segment_start + pump_length/2;
% 第 6 段应变 101-103 m 离中心 105 m 有 ~2 m；
% 第 9 段 164–166 m 几乎正中 165 m。

% all_effective_stokes,all_probe_map,all_stokes_map中仅有all_effective_stokes 被用作深度学习的训练样本，其他两个变量仅用于内部计算和临时存储

% 仿真中的网格是0.1m，说明BFS分布是以0.1m一个网格的，然后训练需要BFS标签，但是现在想要达到的空间分辨率是0.5m，说明系统只能分辨出0.5m的变化，
% 如果直接用现在的BFS的话，由于网格是0.1m的，所以BFS上升/下降沿是0.1m，等同让网络还原 0.1 m 分辨率，远高于硬件条件（50 ns 脉冲→5 m）可恢复的带宽，训练几乎必然失败。
% 在 5 m 的硬件模糊核下，想分辨 0.1 m 等效要反卷积 50 倍带宽 的高频信号——信息论上已非常接近不可逆，噪声一放大就全崩。0.5 m 相当于只提升 10 倍带宽；
% 在 CNN 那篇论文里实验的 SNR（≈ 26 dB）恰好还能撑得住，于是选择 0.5 m 作为"既有意义又可实现"的目标。
% 因此需要把上升/下降沿平滑为0.5m，这样就能分辨了，只不过会把现在0.5m频移长度拉长一点。
% 硬件（50 ns 脉冲→5 m 分辨率）本身无法分辨 0.1 m 的跳变，所以如果直接把这些“刀片式”上升/下降沿丢给网络训练，网络会被迫学习根本测不到的高频信息，训练极易发散。

% 在总的探测光信号中（线性+斯托克斯）中加入了高斯白噪声，使得SNR为：23-33dB，然后把这个带噪总功率-不带噪的线性功率，得到带噪斯托克斯功率，然后再进行了归一化

% 这里面有两个归一化：
% 1.斯托克斯信号的归一化，作为模型的输入，这个归一化的公式是：x' = (x - min) / (max - min)  ∈ [0,1]，归一化后属于0-1之间，并且是斯托克斯信号加了噪声后再进行的归一化，而且是每一个频率点的斯托克斯曲线分别单独进行归一化；
% 2.BFS标签的归一化，由于网络模型的输出一般是限制在0-1之间的（或者是-1~1之间），因此要使得模型输出能匹配上BFS标签，因此也需要把BFS标签归一化到0-1，而且只用归一化训练样本的（因为验证和测试都是用来预测的，不用处理它们的标签），
% 同样也是用公式：x' = (x - min) / (max - min)，
% 注意此时这个就不是单独归一化了，而是找到所有训练集样本中的一个最大BFS和最小BFS，这样用这两个全局极值点，对每一个训练样本的BFS进行统一归一化。为什么这里需要统一归一化呢：因为是为了方便还原真实BFS：x  = x' * (max - min) + min，
% 如果不统一进行归一化的，那么每一个样本都会有自己的BFS极值点，到时候就不好还原了。
% 由于随机生成的训练样本应变极值为-40MHz和50MHz，因此我直接用的这两个极值来全局归一化BFS（高斯平滑后的BFS）

% *** 最后还是去掉了基线段 ***
% 因为这只能用在模型训练中，在实际进行实验的时候，根本就没办法生成正确的基线段，仿真中倒是可以生成，因为下一段开头的频移会影响上一段的尾部信号，因此可以在仿真中基于第一段开头有无频移生成正确的第0段基线信号，
% 但是在实际中根本生成不了，因为我根本就不知道第一段开头有没有频移，而且还需要知道这个频移的具体大小和范围，才能生成第0段基线信号，所以感觉这种方法不太可行。

clc
clear
close all

% 获取当前系统时间
currentTime = datetime('now', 'Format', 'HH:mm:ss');
disp(['程序启动时间: ', char(currentTime)]);

format long eng;
rng('shuffle');

%% *** 深度学习数据生成控制参数 ***
DL_DATA_GENERATION = true;    % 是否启用深度学习数据生成模式
SAVE_DL_DATA = true;          % 是否保存深度学习数据
DL_DATA_PATH = '0_dot_5m_Noise_Dataset/';  % 深度学习数据保存路径

% *** 全局频移范围参数（确保所有地方使用一致的值）***
MIN_FREQ_SHIFT = -40.0;       % 最小频移 (MHz)
MAX_FREQ_SHIFT = 50.0;        % 最大频移 (MHz)

% *** 数据集划分策略 ***
TOTAL_RANDOM_SAMPLES = 5000;    % 随机样本总数 train + val    mat文件的数量，不是滑窗样本的数量
NUM_TEST_SAMPLES = 1000;         % 测试样本数量 test      mat文件的数量，不是滑窗样本的数量
TRAIN_RATIO = 0.7;            % 训练集比例 (70%) train
VAL_RATIO = 0.3;              % 验证集比例 (30%) val

% 计算各数据集样本数量
NUM_TRAIN_SAMPLES = TRAIN_RATIO * TOTAL_RANDOM_SAMPLES; % train
NUM_VAL_SAMPLES = VAL_RATIO * TOTAL_RANDOM_SAMPLES; % val
TOTAL_SAMPLES = NUM_TRAIN_SAMPLES + NUM_VAL_SAMPLES + NUM_TEST_SAMPLES;

% 获取当前并行池
pool = gcp('nocreate');
if isempty(pool)
    disp('并行池未开启。尝试启动新的并行池...');
    parpool('IdleTimeout', 120);
    disp('新的并行池已启动。');
else
    disp('并行池已开启。');
    disp(['并行池大小: ', num2str(pool.NumWorkers), ' 个工作者']);
    if pool.IdleTimeout ~= 120
        pool.IdleTimeout = 120;
        disp('空闲超时时间已重新设置为 120 分钟。');
    end
end

tic

%% 控制开关
Loss_item = 1;
anim_active = 0;
title1 = 'SBS_solver_animation_noise.mp4';

% *** 噪声控制参数 ***
enable_detector_noise = true;  % 是否启用探测器噪声
detector_snr_range = [25, 35]; % 探测器SNR范围 (dB)

%% 参数设置
% 物理常数
c = 3e8;                      % 光速 (m/s)
n = 1.5;                      % 光纤折射率
epsilon0 = 8.85e-12;          % 真空介电常数 (F/m)
kB = 1.38e-23;                % 玻尔兹曼常数
Temperature = 300;            % 波导温度 (K)
va = 5960;                    % 光纤中的声速 (m/s)
vc = c/n;                     % 光纤中的光速
nf = 1.5;                     % 光纤模式的模态指数
eta = c*epsilon0*nf/2;

% 光纤参数 - 修改为200m用于快速验证
L = 200;                     % 光纤总长度 (m) - 从1000m改为200m
Fiber_Aeff = 50e-12;          % 有效模场面积 (m^2)

% 损耗参数
switch Loss_item
    case 0
        loss = 0;
        alpha = 0;
    case 1 
        loss = 0.2;
        alpha_power_per_m = loss * log(10) / (10 * 1000);
        alpha = alpha_power_per_m / 2;
end

% 仿真参数
SBS_freq_max = 1e9;
Delta_t = 1/(2*SBS_freq_max);

% 计算全局时间其他相关参数
t_max = 2.5*n*L/c;
Delta_freq = 1/t_max;
f_2 = -SBS_freq_max:Delta_freq:SBS_freq_max;
f_ang_2 = 2*pi*f_2;
Nf = length(f_ang_2);

% 创建全局时间轴
t_global = single(0:Delta_t:t_max);
Nt = length(t_global);

% 检查步长
if (1/Delta_freq)-(1/SBS_freq_max)<(n*L/c)
    error('invalid step size combination')
end

dz = c*Delta_t/n;
z_full = 0:dz:L;
Nz_full = length(z_full);

% 分区计算参数
partition_length = 100;
num_partitions = ceil(L / partition_length);
points_per_partition = round(partition_length / dz);

% 探测脉冲的脉宽
Probe_width_global = 200e-9;

%% *** 修改：多段扫描设置（不包含基线段）***
segment_length = vc * Probe_width_global / 2;  % 每段20m（探测脉冲200ns的一半）
num_segments_total = floor(L / segment_length);
num_segments_valid = num_segments_total - 1;  % 排除最后一段
valid_fiber_length = num_segments_valid * segment_length;  % 有效光纤长度

fprintf('*** 段配置信息（不包含基线段）***\n');
fprintf('光纤总长度: %.1f m\n', L);
fprintf('段长度: %.1f m\n', segment_length);
fprintf('光纤总段数: %d\n', num_segments_total);
fprintf('有效段数: %d (段1-9)\n', num_segments_valid);
fprintf('保存段数: %d (段1-9)\n', num_segments_valid);
fprintf('有效光纤长度: %.1f m (用于应变区域)\n', valid_fiber_length);

disp(['计算分区长度: ', num2str(partition_length), ' m']);
disp(['计算分区数量: ', num2str(num_partitions)]);
disp(['每个分区空间点数: ', num2str(points_per_partition)]);
disp(['总空间点数: ', num2str(Nz_full)]);
disp(['总时间点数: ', num2str(Nt)]);

% 布里渊参数
tao_a = 10e-9;
Gamma = 1/tao_a;
gamma = Gamma/2;
strain_coef = 0.0483e6;
gB = 5e-11;
g0 = gB/Fiber_Aeff;
Omega_B_base = 2*pi*10.8e9;

%% *** 深度学习数据生成主循环 ***
if DL_DATA_GENERATION
    % 创建深度学习数据保存目录
    if SAVE_DL_DATA && ~exist(DL_DATA_PATH, 'dir')
        mkdir(DL_DATA_PATH);
        disp(['创建深度学习数据目录: ', DL_DATA_PATH]);
    end

    % 显示数据集配置
    fprintf('\n*** 数据集配置（不包含基线段）***\n');
    fprintf('  - 随机样本总数: %d\n', TOTAL_RANDOM_SAMPLES);
    fprintf('  - 训练集: %d 样本 (%.0f%%)\n', NUM_TRAIN_SAMPLES, TRAIN_RATIO*100);
    fprintf('  - 验证集: %d 样本 (%.0f%%)\n', NUM_VAL_SAMPLES, VAL_RATIO*100);
    fprintf('  - 测试集: %d 样本\n', NUM_TEST_SAMPLES);
    fprintf('  - 总样本数: %d\n', TOTAL_SAMPLES);
    fprintf('  - 每个样本段数: %d (段1-9)\n', num_segments_valid);

    % 深度学习数据生成循环
    for sample_idx = 1:TOTAL_SAMPLES
        % 确定当前样本的数据集类型
        if sample_idx <= NUM_TRAIN_SAMPLES
            dataset_type = 'train';
            type_idx = sample_idx;
        elseif sample_idx <= NUM_TRAIN_SAMPLES + NUM_VAL_SAMPLES
            dataset_type = 'val';
            type_idx = sample_idx - NUM_TRAIN_SAMPLES;
        else
            dataset_type = 'test';
            type_idx = sample_idx - NUM_TRAIN_SAMPLES - NUM_VAL_SAMPLES;
        end

        fprintf('\n=== 生成%s样本 %d/%d (总进度: %d/%d) ===\n', ...
               dataset_type, type_idx, eval(['NUM_' upper(dataset_type) '_SAMPLES']), sample_idx, TOTAL_SAMPLES);

        % *** 修改：根据数据集类型生成应变区域（限制在有效光纤长度内）***
        % 所有数据集（train、val、test）都使用统一的随机生成函数
        strain_regions = generate_random_strain_regions_exclude_last(valid_fiber_length, dataset_type, MIN_FREQ_SHIFT, MAX_FREQ_SHIFT);
        fprintf('生成随机%s样本 %d: %d 个应变区域，使用全局频移范围: [%.2f, %.2f] MHz\n', ...
                dataset_type, type_idx, size(strain_regions, 1), MIN_FREQ_SHIFT, MAX_FREQ_SHIFT);

        % 显示应变区域信息
        for i = 1:size(strain_regions, 1)
            fprintf('  区域%d: %.1f-%.1fm, %+.2fMHz\n', i, strain_regions(i,1), strain_regions(i,2), strain_regions(i,3));
        end

        % 定义布里渊频移分布
        Omega_B_full = ones(1, Nz_full) * Omega_B_base;

        % 遍历每个应变区域并应用相应的频移
        for i = 1:size(strain_regions, 1)
            region_start = strain_regions(i, 1);
            region_end = strain_regions(i, 2);
            region_shift = strain_regions(i, 3);

            region_indices = find(z_full >= region_start & z_full <= region_end);
            Omega_B_full(region_indices) = Omega_B_base + 2*pi*region_shift * 1e6;
        end

        % 脉冲参数 - 泵浦光
        Pump_peak_P_0 = 40e-3;
        Pump_peak_E_0 = sqrt(Pump_peak_P_0);
        Pump_ER = 20;
        Pump_leak_P_0 = 0;
        Pump_leak_E_0 = sqrt(Pump_leak_P_0);
        Pump_rise_time = 0.1e-9;
        Pump_a1 = 0.45*Pump_rise_time;
        Pump_fall_time = 0.1e-9;
        Pump_a2 = 0.45*Pump_fall_time;
        Pump_width = 50e-9;

        % 脉冲参数 - 探测光
        Probe_peak_P_L = 1e-3;
        Probe_peak_E_L = sqrt(Probe_peak_P_L);
        Probe_ER = 20;
        Probe_leak_P_L = 0;
        Probe_leak_E_L = sqrt(Probe_leak_P_L);
        Probe_rise_time = 0.1e-9;
        Probe_a1 = 0.45*Probe_rise_time;
        Probe_fall_time = 0.1e-9;
        Probe_a2 = 0.45*Probe_fall_time;
        Probe_width = Probe_width_global;

        % 计算脉冲对应的空间长度
        pump_length = vc * Pump_width;
        probe_length = vc * Probe_width;
        spatial_resolution = pump_length / 2;

        % *** 修改：扫频参数，使用70MHz步长只保存3个频率点 ***
        Omega_start = 2*pi*10.73e9;
        Omega_end = 2*pi*10.87e9;
        Omega_step = 2*pi*70e6;         % 修改为70MHz步长
        Omega_list = Omega_start:Omega_step:Omega_end;
        n_sweep = length(Omega_list);   % 现在应该是3个频率点

        % 选择频点进行可视化 - 现在就是所有3个频率点
        freq_pick = 1:n_sweep;  % 所有频率点都保存
        n_pick = numel(freq_pick);
        
        % 显示保存的频率点信息
        fprintf('保存的频率点信息 (共%d个):\n', n_sweep);
        for i = 1:n_sweep
            freq_val = Omega_list(i)/(2*pi*1e9);
            fprintf('  频率点%d: %.3f GHz\n', i, freq_val);
        end

        % *** 重叠扩展计算域参数 ***
        overlap_length = pump_length * 1.5;  % 重叠区长度，1.5倍泵浦脉冲长度，即15m
        fprintf('重叠区长度: %.1fm\n', overlap_length);

        % *** 修改：存储不同段的结果（不包含基线段）***
        all_probe_map = cell(1, num_segments_valid);
        all_stokes_map = cell(1, num_segments_valid);
        all_BGS_results = cell(1, num_segments_valid);
        all_BPS_results = cell(1, num_segments_valid);
        all_BPGR_results = cell(1, num_segments_valid);
        all_window_positions = cell(1, num_segments_valid);
        all_BFS_distribution = cell(1, num_segments_valid);
        all_A2_complex = cell(1, num_segments_valid);
        all_A2_linear_complex = cell(1, num_segments_valid);

        % 存储整个光纤的BGS/BPS/BFS数据
        full_fiber_positions = [];
        full_fiber_BFS = [];
        all_segment_3D_stokes = cell(1, num_segments_valid);
        all_segment_3D_acoustic = cell(1, num_segments_valid);

        % 存储有效的斯托克斯信号，即探测脉宽长度的信号
        all_effective_stokes = cell(1, num_segments_valid);

        % *** 修改：定义段的范围（不包含基线段）***
        segment_ranges = zeros(num_segments_valid, 2);
        % 第1-9段：有效段
        for s = 1:num_segments_valid
            segment_ranges(s, :) = [(s-1)*segment_length, s*segment_length];
        end

        % 创建主进度条
        h_main = waitbar(0, '分段计算准备...', 'Name', 'Brillouin Fiber Sensing Simulation (Without Baseline Segments)');

        %% *** 修改：按重叠扩展分区计算所有段 ***
        for partition_idx = 1:num_partitions
            % *** 1. 计算扩展后的计算分区范围 ***
            % 未扩展前的此分区左端和右端全局空间位置
            partition_start_physical = (partition_idx - 1) * partition_length;
            partition_end_physical = min(partition_idx * partition_length, L);
            
            % 向左扩展，全局位置
            partition_start_calc = max(0, partition_start_physical - overlap_length);
            % 实际向左扩展的长度
            left_extend_length = partition_start_physical - partition_start_calc;
            % 向右扩展，全局位置
            partition_end_calc = min(L, partition_end_physical + overlap_length);
            % 实际向右扩展的长度
            right_extend_length = partition_end_calc - partition_end_physical;
            
            fprintf('分区%d: 物理范围[%.1f-%.1fm], 计算范围[%.1f-%.1fm]\n', ...
                   partition_idx, partition_start_physical, partition_end_physical, ...
                   partition_start_calc, partition_end_calc);
            
            % 当前扩展分区的实际长度和z轴
            L_part_calc = partition_end_calc - partition_start_calc;
            z_calc_indices = find(z_full >= partition_start_calc & z_full <= partition_end_calc);
            current_z_calc = z_full(z_calc_indices);
            current_Nz_calc = length(current_z_calc);
            current_Omega_B_calc = Omega_B_full(z_calc_indices);

            % *** 2. 找出位于"物理"分区内的有效测量段 ***
            segments_in_partition = [];
            for seg_idx = 1:num_segments_valid  % 段1-9对应索引1-9
                seg_start = segment_ranges(seg_idx, 1);
                seg_end = segment_ranges(seg_idx, 2);
                if seg_start >= partition_start_physical && seg_end <= partition_end_physical
                    segments_in_partition = [segments_in_partition, seg_idx];
                end
            end
            
            if isempty(segments_in_partition)
                continue;
            end
            
            fprintf('  包含%d个有效测量段: %s\n', length(segments_in_partition), ...
                   mat2str(segments_in_partition));  % 显示为段1-9
            
            % 计算跨分区传播延迟与损耗（基于物理分区，即未扩展前的分区）
            part_left  = partition_idx - 1;
            part_right = num_partitions - partition_idx;
            
            extra_t_p = part_left  * partition_length / vc;
            extra_t_s = part_right * partition_length / vc;
            % 基于扩展分区方法下，实际的损耗因子
            loss_p    = exp(-alpha * (part_left  * partition_length - left_extend_length));
            loss_s    = exp(-alpha * (part_right * partition_length - right_extend_length));
            
            % *** 3. 遍历当前物理分区内的所有有效测量段 ***
            for seg_idx = 1:length(segments_in_partition)
                segment_idx = segments_in_partition(seg_idx);
                
                % 确定当前段的范围
                segment_start = segment_ranges(segment_idx, 1);
                segment_end = segment_ranges(segment_idx, 2);
                
                fprintf('    处理有效段%d: %.1f-%.1fm\n', segment_idx, segment_start, segment_end);
                
                % 计算脉冲入射时间
                meeting_position = segment_start + pump_length / 2;
                separation_position = meeting_position + (probe_length - pump_length)/2;
                separation_position = min(separation_position, L);
                
                % 计算泵浦和探测的入射延迟（基于全局光纤）
                if meeting_position <= L/2
                    t0_p_global = (L - 2*meeting_position) / vc;
                    t0_s_global = 0;
                else
                    t0_p_global = 0;
                    t0_s_global = (2*meeting_position - L) / vc;
                end
                
                % 计算脉冲相遇和分离的时间
                t_meet     = (L/vc + t0_p_global + t0_s_global)/2;
                t_separate = t_meet + (Pump_width + Probe_width)/2;
                
                %% *** 4. 每分区自建基于扩展域的时间轴 ***
                t_max_part_calc = 2.5*n*L_part_calc/c;
                Nt_local = floor(t_max_part_calc/Delta_t) + 1;
                t_local = single(0 : Delta_t : (Nt_local-1)*Delta_t);
                
                % 脉冲在扩展计算分区内的相遇位置（相对于扩展域的起点）
                meeting_pos_rel = meeting_position - partition_start_calc;
                
                % 延迟时间，局部（基于扩展域的长度），相当于现在从扩展分区的两端入射了
                if meeting_pos_rel <= L_part_calc/2            
                    t0_p_local = (L_part_calc - 2*meeting_pos_rel)/vc;
                    t0_s_local = 0;
                else
                    t0_p_local = 0;
                    t0_s_local = (2*meeting_pos_rel - L_part_calc)/vc;
                end
                
                % 把前面分区的衰减叠加到幅度上（已经减去了扩展长度）
                Pump_loss  = loss_p;
                Probe_loss = loss_s;
                
                % 生成局部泵浦/探测波形
                t_rel_p = t_local - t0_p_local;
                t_rel_s = t_local - t0_s_local;
                
                Pump_t = (Pump_peak_E_0-Pump_leak_E_0) ...
                       .* sqrt((tanh(t_rel_p/Pump_a1)-tanh((t_rel_p-Pump_width)/Pump_a2))/2) ...
                       +  Pump_leak_E_0;
                Pump_t = Pump_t * Pump_loss;
                
                Probe_t = (Probe_peak_E_L-Probe_leak_E_L) ...
                        .* sqrt((tanh(t_rel_s/Probe_a1)-tanh((t_rel_s-Probe_width)/Probe_a2))/2) ...
                        +  Probe_leak_E_L;
                Probe_t = Probe_t * Probe_loss;
                
                % 相遇/分离/到达接收端的时间（都是在扩展分区中，局部）
                t_meet_local      = (L_part_calc/vc + t0_p_local + t0_s_local)/2;
                t_separate_local  = t_meet_local + (Pump_width + Probe_width)/2;
                t_probe_arr_local = t0_s_local  + L_part_calc/vc;
                
                % 对应的扩展分区下局部时间索引，都是局部
                idx_meet     = round(t_meet_local     / Delta_t) + 1;
                idx_separate = round(t_separate_local / Delta_t) + 1;
                idx_arrive   = round(t_probe_arr_local/ Delta_t) + 1;
                
                % 检查当前相遇点是否在当前扩展计算分区内
                is_meeting_in_partition = (meeting_position >= partition_start_calc && meeting_position <= partition_end_calc);
                
                if ~is_meeting_in_partition
                    fprintf('    警告: 相遇点不在扩展计算分区内，跳过\n');
                    continue;
                end
                
                % 相遇点在当前扩展分区的索引，当前分区下局部空间索引
                [~, idx_meet_z] = min(abs(current_z_calc - meeting_position));
                
                % *** 5. 初始化结果数组 - 现在是3个频率点 ***
                A2_complex_matrix = complex(zeros(Nt_local, n_sweep, 'single'));
                A2_linear_complex_matrix = complex(zeros(Nt_local, n_sweep, 'single'));
                power_total_matrix = zeros(Nt_local, n_sweep, 'single');
                power_linear_matrix = zeros(Nt_local, n_sweep, 'single');
                stokes_power = zeros(Nt_local, n_sweep, 'single');
                q_meeting_point = zeros(Nt_local, n_sweep, 'single');
                
                % *** 预计算线性传播的泵浦和探测光场，用于真实响应方法中计算驱动项和产生速率 ***
                % 扩展分区下，对应的局部空间点数和时间点数
                A1_linear = zeros(current_Nz_calc, Nt_local, 'single');
                A2_linear = zeros(current_Nz_calc, Nt_local, 'single');
                
                % 计算线性传播场
                A1_linear(1,:) = Pump_t;
                A2_linear(end,:) = Probe_t;
                
                % 在当前扩展分区的情况下，传播到扩展分区两端的线性结果
                for i = 1:Nt_local-1
                    A1_linear(2:end,i+1) = A1_linear(1:end-1,i) * exp(-alpha*dz);
                    A2_linear(1:end-1,i+1) = A2_linear(2:end,i) * exp(-alpha*dz);
                    A1_linear(1,i+1) = Pump_t(i+1);
                    A2_linear(end,i+1) = Probe_t(i+1);
                end
                
                % 在扩展分区左端的线性探测光幅度变化
                A2_linear_receiver = A2_linear(1, :);
                
                %% *** 6. 使用parfor并行计算（现在只有3个频率点）- 真实响应求和方法 ***
                parfor freq_idx = 1:n_sweep
                    Omega = Omega_list(freq_idx);
                    
                    [A2_complex, A2_linear_out, power_total, power_linear, q_meet] = run_one_freq(...
                        Omega, ...
                        A1_linear, A2_linear, A2_linear_receiver, partition_start_calc, ...
                        current_Omega_B_calc, current_z_calc, t_local, Delta_t, dz, idx_meet_z, ...
                        g0, gB, gamma, Fiber_Aeff, vc, alpha, true); % true表示计算从分区左端传播到z=0的损耗
                    
                    % *** 修改：给探测到的总功率添加噪声 ***
                    power_total_noisy = addNoiseToPowerTotal(power_total, detector_snr_range, enable_detector_noise);

                    % 保存结果，还是局部的变量，只是已经乘以了传播到z=0的衰减
                    A2_complex_matrix(:, freq_idx) = A2_complex;
                    A2_linear_complex_matrix(:, freq_idx) = A2_linear_out;
                    power_total_matrix(:, freq_idx) = power_total_noisy;  % 保存加噪声后的总功率
                    power_linear_matrix(:, freq_idx) = power_linear;
                    stokes_power(:, freq_idx) = power_total_noisy - power_linear;  % 用加噪声的总功率计算斯托克斯信号
                    q_meeting_point(:, freq_idx) = q_meet;
                end
                
                % 局部时间变化量（还未复制到全局）
                probe_map_SBS = power_total_matrix;
                stokes_map_SBS = stokes_power;
                A2_complex = A2_complex_matrix;
                A2_linear_complex = A2_linear_complex_matrix;
                acoustic_map = q_meeting_point;
                
                %% BGS提取与可视化
                pump_position = @(t) vc * (t - t0_p_global);
                probe_position = @(t) L - vc * (t - t0_s_global);
                
                syms t_meet_sym
                meet_eq = pump_position(t_meet_sym) == probe_position(t_meet_sym);
                t_meet_sol = double(solve(meet_eq, t_meet_sym));
                meeting_point_actual = pump_position(t_meet_sol);
                
                num_windows = round(probe_length / pump_length);
                window_time = Pump_width;
                time_window = round(window_time / Delta_t);
                
                all_BGS = zeros(num_windows, n_sweep, 'single');
                all_BPS = zeros(num_windows, n_sweep, 'single');
                all_BPGR = zeros(num_windows, n_sweep, 'single');
                
                window_positions = zeros(num_windows, 2, 'single');
                for w = 1:num_windows
                    if w == 1
                        window_positions(w, :) = [meeting_point_actual - spatial_resolution, meeting_point_actual];
                    else
                        window_positions(w, :) = [meeting_point_actual + (w-2)*spatial_resolution, meeting_point_actual + (w-1)*spatial_resolution];
                    end
                end
                
                % *** 提取BGS和BPS ***
                for window = 1:num_windows
                    start_idx = idx_arrive + (window-1) * time_window;
                    end_idx = min(start_idx + time_window, Nt_local);
                    
                    if start_idx >= Nt_local || end_idx > Nt_local || start_idx <= 0
                        continue;
                    end
                    
                    for freq_idx = 1:n_sweep
                        window_stokes = stokes_map_SBS(start_idx:end_idx, freq_idx);
                        all_BGS(window, freq_idx) = mean(window_stokes);
                        
                        A2_sig = A2_complex(start_idx:end_idx, freq_idx);
                        phase_diff = angle(mean(A2_sig));
                        all_BPS(window, freq_idx) = phase_diff;
                        
                        gain_val = abs(all_BGS(window, freq_idx));
                        if gain_val > 1e-12
                            all_BPGR(window, freq_idx) = phase_diff / gain_val;
                        else
                            all_BPGR(window, freq_idx) = 0;
                        end
                    end
                end
                
                % 计算每个窗口的BFS
                BFS_distribution = zeros(num_windows, 1, 'single');
                for window = 1:num_windows
                    [~, max_idx] = max(all_BGS(window, :));
                    BFS_distribution(window) = (Omega_list(max_idx)/(2*pi)-10.8e9)/1e6;
                end
                
                % 保存频点并映射到全局时间
                stokes_pick = stokes_map_SBS(:, freq_pick);
                acoustic_pick = acoustic_map(:, freq_pick);
                
                % 创建全局时间轴上的零矩阵，使用正确的延迟时间将局部结果映射到全局时间轴
                pad_stokes = single(zeros(Nt, n_pick));
                pad_acoustic = single(zeros(Nt, n_pick));
                
                % 全局到达z=0的时间及时间索引
                stokes_global_delay = t0_s_global + L/vc;
                stokes_idx_offset = round(stokes_global_delay / Delta_t);
                
                % 全局两脉冲相遇（代表声场产生）的时间及时间索引
                acoustic_global_delay = t_meet;
                acoustic_idx_offset = round(acoustic_global_delay / Delta_t);
                
                % 局部情况下，斯托克斯和声场出现的时间索引
                stokes_local_start_idx = idx_arrive;
                acoustic_local_start_idx = idx_meet;
                
                % 计算有效斯托克斯信号的结束索引（长度用探测光脉宽，而不是一直到Nt_local）
                probe_pulse_time_points = round(Probe_width / Delta_t);
                stokes_length = min(probe_pulse_time_points, Nt_local - stokes_local_start_idx + 1);
                acoustic_length = Nt_local - acoustic_local_start_idx + 1;
                
                % 全局情况下，在z=0处斯托克斯光开始时间索引和结束时间索引
                stokes_g_start = stokes_idx_offset + 1;
                stokes_g_stop = min(stokes_g_start + stokes_length - 1, Nt);
                stokes_valid_length = min(stokes_length, stokes_g_stop - stokes_g_start + 1);
                
                acoustic_g_start = acoustic_idx_offset + 1;
                acoustic_g_stop = min(acoustic_g_start + acoustic_length - 1, Nt);
                acoustic_valid_length = min(acoustic_length, acoustic_g_stop - acoustic_g_start + 1);
                
                if stokes_g_start <= Nt && stokes_g_start + stokes_length - 1 <= Nt && stokes_local_start_idx > 0
                    pad_stokes(stokes_g_start:stokes_g_start+stokes_length-1, :) = stokes_pick(stokes_local_start_idx:stokes_local_start_idx+stokes_length-1, :);
                end
                
                if acoustic_g_start <= Nt && acoustic_g_start + acoustic_length - 1 <= Nt && acoustic_local_start_idx > 0
                    pad_acoustic(acoustic_g_start:acoustic_g_start+acoustic_length-1, :) = acoustic_pick(acoustic_local_start_idx:Nt_local, :);
                end
                
                % *** 修改：为深度学习保存有效的斯托克斯信号（只保存有效段）***
                if DL_DATA_GENERATION
                    stokes_end_idx = min(stokes_local_start_idx + probe_pulse_time_points - 1, Nt_local);
                    effective_stokes_signal = stokes_pick(stokes_local_start_idx:stokes_end_idx, :);
                    if ~exist('all_effective_stokes', 'var')
                        all_effective_stokes = cell(num_segments_valid, 1);
                    end
                    all_effective_stokes{segment_idx} = effective_stokes_signal;
                end
                
                % 保存段的计算结果，当前分区下计算出来的随时间变化的结果
                all_probe_map{segment_idx} = power_total_matrix;
                all_stokes_map{segment_idx} = stokes_map_SBS;
                all_A2_complex{segment_idx} = A2_complex;
                all_A2_linear_complex{segment_idx} = A2_linear_complex;
                
                all_segment_3D_stokes{segment_idx} = pad_stokes;
                all_segment_3D_acoustic{segment_idx} = pad_acoustic;
                
                all_BGS_results{segment_idx} = all_BGS;
                all_BPS_results{segment_idx} = all_BPS;
                all_BPGR_results{segment_idx} = all_BPGR;
                all_window_positions{segment_idx} = window_positions;
                all_BFS_distribution{segment_idx} = BFS_distribution;
                
                full_fiber_positions = [full_fiber_positions; mean(window_positions, 2)];
                full_fiber_BFS = [full_fiber_BFS; BFS_distribution];
                
                % 清理当前段计算出来的大数组
                clear A2_complex_matrix power_total_matrix power_linear_matrix q_meeting_point A1_linear A2_linear;
            end
            
            % 更新主进度条
            waitbar(partition_idx/num_partitions, h_main, ...
                sprintf('完成计算分区 %d/%d (%.1f%%)', partition_idx, num_partitions, ...
                       partition_idx/num_partitions*100));
        end
        
        % *** 修改：深度学习数据保存（不包含基线段）***
        if DL_DATA_GENERATION && SAVE_DL_DATA
            save_dl_training_sample(sample_idx, strain_regions, all_effective_stokes, ...
                                        segment_ranges, DL_DATA_PATH, dataset_type, valid_fiber_length, Delta_t, Omega_list, segment_length, ...
                                        z_full, Omega_B_full, Omega_B_base, MIN_FREQ_SHIFT, MAX_FREQ_SHIFT);
        end

        fprintf('样本 %d/%d 完成 (快速求和方法)\n', sample_idx, TOTAL_SAMPLES);
        
        % 关闭主进度条
        close(h_main);
        
        % 清理内存
        clear all_segment_3D_stokes all_segment_3D_acoustic all_effective_stokes;
    end
    
    % *** 修改：生成数据集摘要（不包含基线段）***
    generate_dataset_summary(DL_DATA_PATH, TOTAL_SAMPLES, NUM_TRAIN_SAMPLES, NUM_VAL_SAMPLES, NUM_TEST_SAMPLES, valid_fiber_length, num_segments_valid, segment_length, partition_length, MIN_FREQ_SHIFT, MAX_FREQ_SHIFT);

    % 可视化第一个训练样本作为示例
    fprintf('\n=== 可视化示例样本（快速求和方法）===\n');
    try
        visualize_dl_sample(DL_DATA_PATH, 1, 'train', segment_length);
        fprintf('已生成第1个训练样本的可视化图形（快速求和方法）\n');
    catch ME
        fprintf('可视化失败: %s\n', ME.message);
    end

    fprintf('深度学习数据生成完成（快速求和方法）！\n');
    delete(gcp('nocreate'));
    return;
end

% 正常模式：使用固定的应变区域（如果不是深度学习模式）
strain_regions = [
    50, 55, 20;
    100, 105, 25;
    150, 155, 15;
];

% 执行正常的BOTDA仿真...
% [这里可以添加正常模式的代码]

disp(['程序完成。总运行时间: ', num2str(toc/60), ' 分钟']);

%% ================== 函数定义区域 ==================
% 所有函数必须定义在脚本的最后








% *** 统一随机应变区域生成函数（排除最后一段版本）***
function strain_regions = generate_random_strain_regions_exclude_last(valid_fiber_length, dataset_type, min_freq_shift, max_freq_shift)
    % 为所有数据集（train、val、test）生成完全随机的应变区域
    % 输入参数:
    %   valid_fiber_length: 有效光纤长度 (m)，通常为180m（排除最后一段）
    %   dataset_type: 数据集类型 ('train', 'val', 'test')
    %   min_freq_shift: 最小频移值 (MHz)
    %   max_freq_shift: 最大频移值 (MHz)
    % 输出参数:
    %   strain_regions: [起始位置, 结束位置, 频移值] 的矩阵

    % *** 设置随机数种子，确保真正的随机性 ***
    % 使用更简单但有效的方法：基于当前时间生成有效的种子值

    % 使用clock函数获取当前时间
    time_vec = clock();  % [年 月 日 时 分 秒]
    % 将秒数转换为微秒级精度，取小数部分
    microsec_part = round((time_vec(6) - floor(time_vec(6))) * 1000000);
    % 结合分钟和秒的整数部分
    seed_base = time_vec(5) * 100 + floor(time_vec(6));  % 分钟*100 + 秒
    seed_value = seed_base * 1000000 + microsec_part;  % 组合成最终种子

    % 确保种子值为正整数且在有效范围内
    seed_value = abs(round(seed_value));  % 确保为正整数
    max_seed = 2^31 - 1;  % 使用更保守的限制 (2147483647)
    seed_value = mod(seed_value, max_seed) + 1;  % 确保在1到max_seed之间

    % 调试输出
    fprintf('生成随机种子: %d (范围: 1-%d)\n', seed_value, max_seed);

    rng(seed_value);  % 使用处理后的种子值


    % *** 5%概率生成零频移样本 ***
    if rand() < 0.05  % 5%的概率生成一个完全没有频移的样本
        strain_regions = []; % 返回一个空的应变区域矩阵
        fprintf('  → 生成一个零频移样本\n');
        return; % 直接返回
    end


    % *** 应变区域数量真正随机生成（5-15个之间）***
    min_regions = 5;
    max_regions = 15;
    num_regions = randi([min_regions, max_regions]);

    strain_regions = [];
    freq_shifts = [];

    % 生成指定数量的随机应变区域
    for i = 1:num_regions
        % *** 真正随机的区域长度生成（0.5m概率稍高）***
        % 最小长度0.5m，最大长度10.0m，精确到小数点后一位
        min_length = 0.5;
        max_length = 10.0;

        % 使用加权随机：0.5m长度有更高概率（约5%概率选择0.5m）
        if rand() < 0.05
            region_length = 0.5;  % 5%概率直接选择0.5m
        else
            % 95%概率在0.5-10.0m范围内均匀分布
            region_length = min_length + rand() * (max_length - min_length);
        end
        region_length = round(region_length * 10) / 10;  % 保留一位小数

        % *** 真正随机的频移值生成 ***
        % 使用传入的频移范围参数，精确到小数点后两位
        freq_shift = min_freq_shift + rand() * (max_freq_shift - min_freq_shift);
        freq_shift = round(freq_shift * 100) / 100;  % 保留两位小数

        % *** 随机起始位置生成 ***
        % 确保区域完全在有效光纤长度内（0-180m），留出缓冲区
        buffer_start = 5.0;  % 起始缓冲区5m
        buffer_end = 10.0;   % 结束缓冲区10m
        max_start = valid_fiber_length - region_length - buffer_end;

        if max_start > buffer_start
            % 随机选择起始位置，精确到小数点后一位
            region_start = buffer_start + rand() * (max_start - buffer_start);
            region_start = round(region_start * 10) / 10;  % 保留一位小数
            region_end = region_start + region_length;

            % 添加到应变区域列表
            strain_regions = [strain_regions; region_start, region_end, freq_shift];
            freq_shifts = [freq_shifts; freq_shift];
        end
    end

    % 按起始位置排序，确保区域按空间顺序排列
    if ~isempty(strain_regions)
        strain_regions = sortrows(strain_regions, 1);
        freq_shifts = strain_regions(:, 3);  % 更新频移列表
    end

    % 安全检查：确保至少有5个区域（符合设定的最小值）
    if size(strain_regions, 1) < min_regions
        % 如果生成的区域数量不足，随机补充区域
        needed_regions = min_regions - size(strain_regions, 1);

        for j = 1:needed_regions
            % 随机生成补充区域的长度（0.5m概率为5%）
            if rand() < 0.05
                backup_length = 0.5;
            else
                backup_length = min_length + rand() * (max_length - min_length);
            end
            backup_length = round(backup_length * 10) / 10;

            % 随机生成补充区域的频移
            backup_freq_shift = min_freq_shift + rand() * (max_freq_shift - min_freq_shift);
            backup_freq_shift = round(backup_freq_shift * 100) / 100;

            % 随机生成补充区域的起始位置
            buffer_start = 5.0;
            buffer_end = 10.0;
            max_start_backup = valid_fiber_length - backup_length - buffer_end;

            if max_start_backup > buffer_start
                backup_start = buffer_start + rand() * (max_start_backup - buffer_start);
                backup_start = round(backup_start * 10) / 10;
                backup_end = backup_start + backup_length;

                % 添加随机生成的补充区域
                strain_regions = [strain_regions; backup_start, backup_end, backup_freq_shift];
            end
        end

        % 重新排序
        strain_regions = sortrows(strain_regions, 1);
        freq_shifts = strain_regions(:, 3);
    end

    % 输出调试信息
    fprintf('  → %s数据集: 生成%d个应变区域，长度范围[%.1f-%.1fm]，使用全局归一化范围[%.2f,%.2f]MHz\n', ...
            dataset_type, size(strain_regions, 1), ...
            min(strain_regions(:,2) - strain_regions(:,1)), ...
            max(strain_regions(:,2) - strain_regions(:,1)), ...
            min_freq_shift, max_freq_shift);
end

% *** 修改：深度学习数据保存函数（不包含基线段版本）***
function save_dl_training_sample(sample_idx, strain_regions, stokes_signals, segment_ranges, save_path, dataset_type, valid_fiber_length, Delta_t, Omega_list, segment_length, z_full, Omega_B_full, Omega_B_base, min_freq_shift, max_freq_shift)
    % 保存单个训练样本（不包含基线段）
    % 新增参数：
    %   min_freq_shift: 最小频移值 (MHz)
    %   max_freq_shift: 最大频移值 (MHz)
    
    if ~exist(save_path, 'dir')
        mkdir(save_path);
    end

    type_path = fullfile(save_path, dataset_type);
    if ~exist(type_path, 'dir')
        mkdir(type_path);
    end

    sample_data = struct();
    sample_data.sample_id = sample_idx;
    sample_data.dataset_type = dataset_type;
    sample_data.timestamp = datetime('now');
    sample_data.fiber_length = valid_fiber_length;  % 使用有效光纤长度
    sample_data.strain_regions = strain_regions;
    sample_data.num_strain_regions = size(strain_regions, 1);
    sample_data.segment_ranges = segment_ranges;
    sample_data.num_segments = size(segment_ranges, 1);
    sample_data.segment_length = segment_length;
    sample_data.Delta_t = Delta_t;
    sample_data.Omega_list = Omega_list;
    sample_data.exclude_last_segment = true;  % 标记为排除最后一段的版本
    sample_data.include_baseline_segments = false;  % 标记为不包含基线段的版本
    sample_data.fast_method = true;  % 标记为快速求和方法

    % *** 修改：对斯托克斯信号进行归一化（噪声已在power_total中添加）***
    stokes_signals_normalized = cell(size(stokes_signals));

    for i = 1:length(stokes_signals)
        if ~isempty(stokes_signals{i})
            signal_matrix = stokes_signals{i};
            [time_points, freq_points] = size(signal_matrix);

            % 转换为double精度以避免精度问题
            signal_matrix_double = double(signal_matrix);

            normalized_matrix = zeros(time_points, freq_points);

            % 对每个频率点分别归一化（噪声已经在power_total中添加了）
            for freq_idx = 1:freq_points
                freq_signal = signal_matrix_double(:, freq_idx);

                % 只进行min-max归一化，不再添加噪声
                freq_signal_norm = normalizeStokesSignal(freq_signal);

                normalized_matrix(:, freq_idx) = freq_signal_norm;
            end

            stokes_signals_normalized{i} = single(normalized_matrix);
        else
            stokes_signals_normalized{i} = [];
        end
    end

    sample_data.stokes_signals = stokes_signals_normalized;  % 归一化的信号（用于训练，噪声已在power_total中添加）
    sample_data.stokes_signals_raw = stokes_signals;  % 原始信号（用于参考）

    % *** 修改：使用前面仿真计算时的BFS分布，不重新生成 ***
    % 从前面计算的Omega_B_full中提取频移分布
    fiber_positions = z_full(z_full <= valid_fiber_length);  % 只取有效光纤长度内的位置
    frequency_shift_distribution = (Omega_B_full(z_full <= valid_fiber_length) - Omega_B_base) / (2*pi*1e6);  % 转换为MHz

    % 应用高斯平滑
    frequency_shift_distribution_filtered = apply_gaussian_smoothing(frequency_shift_distribution, 0.1, 0.5, [0.01 0.99], 4); % 0.1表示仿真用的空间网格为0.1m，0.5表示上升/下降沿长度为0.5m
    % [0.01 0.99]表示表示上升/下降沿从1%-99%这一段曲线，而不是10%-90%

    % *** 新增：对高斯平滑后的BFS标签进行全局极值min-max归一化 ***
    bfs_stats_file = fullfile(save_path, 'bfs_stats.mat');

    % 使用固定的频移范围进行归一化（全局一致性）
    fixed_freq_shift_range = [min_freq_shift, max_freq_shift];  % 使用传入的全局频移范围
    frequency_shift_distribution_normalized = normalizeBFS_fixed(frequency_shift_distribution_filtered, fixed_freq_shift_range, bfs_stats_file, 'fwd');

    % 保存原始、平滑后和归一化后的BFS分布
    sample_data.frequency_shift_distribution = frequency_shift_distribution;  % 原始BFS分布（用于可视化）
    sample_data.frequency_shift_distribution_filtered = frequency_shift_distribution_filtered;  % 平滑后BFS分布（用于可视化）
    sample_data.frequency_shift_distribution_normalized = frequency_shift_distribution_normalized;  % 平滑后归一化BFS分布（用于训练）
    sample_data.fiber_positions = fiber_positions;
    sample_data.gaussian_filter_info = struct('dx', 0.1, ...
                                             'target_edge', 0.5, ...
                                             'sigma', 0.5/2.563);

    filename = sprintf('%s_sample_%04d.mat', dataset_type, sample_idx);
    filepath = fullfile(type_path, filename);
    save(filepath, 'sample_data');

    if mod(sample_idx, 5) == 0
        fprintf('已保存第 %d 个样本到: %s (快速方法)\n', sample_idx, filepath);
    end
end

% *** 修改：生成数据集摘要函数（不包含基线段版本）***
function generate_dataset_summary(data_path, num_samples, train_samples, val_samples, test_samples, valid_fiber_length, num_segments_valid, segment_length, partition_length, min_freq_shift, max_freq_shift)
    % 生成深度学习数据集的摘要信息（不包含基线段版本）
    % 新增参数：
    %   min_freq_shift: 最小频移值 (MHz)
    %   max_freq_shift: 最大频移值 (MHz)

    summary_file = fullfile(data_path, 'dataset_summary_fast.txt');
    fid = fopen(summary_file, 'w');

    if fid == -1
        warning('无法创建摘要文件');
        return;
    end

    fprintf(fid, '=== BOTDA深度学习训练数据集摘要（快速求和方法）===\n');
    fprintf(fid, '生成时间: %s\n', datestr(now));
    fprintf(fid, '样本总数: %d\n', num_samples);
    fprintf(fid, '数据集划分:\n');
    fprintf(fid, '  - 训练集: %d 样本 (70%%)\n', train_samples);
    fprintf(fid, '  - 验证集: %d 样本 (30%%)\n', val_samples);
    fprintf(fid, '  - 测试集: %d 样本\n', test_samples);
    fprintf(fid, '\n--- 段配置信息 ---\n');
    fprintf(fid, '段长度: %.0fm\n', segment_length);
    fprintf(fid, '光纤总段数: 10段 (1-10段，第10段被排除)\n');
    fprintf(fid, '有效段数: 9段 (第1-9段)\n');
    fprintf(fid, '保存段数: %d段 (第1-9段)\n', num_segments_valid);
    fprintf(fid, '\n--- 快速求和方法优势 ---\n');
    fprintf(fid, '使用真实响应求和求解方法替代SBS三波耦合方程求解方法\n');
    fprintf(fid, '计算速度提升显著，适用于短脉冲情况\n');
    fprintf(fid, '近似条件：幅度增益和损失较小，相互作用导致的幅度变化可忽略\n');
    fprintf(fid, '\n--- 光纤配置 ---\n');
    fprintf(fid, '光纤总长度: 200m\n');
    fprintf(fid, '有效光纤长度: %.1fm (用于应变区域生成)\n', valid_fiber_length);
    fprintf(fid, '分区长度: %.0fm\n', partition_length);
    fprintf(fid, '分区数量: 2个\n');
    fprintf(fid, '\n--- 应变区域配置 ---\n');
    fprintf(fid, '应变区域长度选项: 0.5m, 1m, 1.5m, 2m, 3m, 4m, 5m, 6m, 8m, 10m\n');
    fprintf(fid, '应变区域数量: 2-6个（随机样本），多样化配置（测试样本）\n');
    fprintf(fid, '频移范围: %.1fMHz 到 +%.1fMHz\n', min_freq_shift, max_freq_shift);
    fprintf(fid, '应变区域限制: 只在有效光纤长度内 (0-%.1fm)\n', valid_fiber_length);
    fprintf(fid, '测试样本: 15种多样化固定配置，适配有效光纤长度\n');
    fprintf(fid, '\n--- 保存的频率点信息 ---\n');
    fprintf(fid, '保存3个频率点的斯托克斯信号:\n');
    fprintf(fid, '- 频率点1: 10.73 GHz\n');
    fprintf(fid, '- 频率点2: 10.80 GHz\n');
    fprintf(fid, '- 频率点3: 10.87 GHz\n');
    fprintf(fid, '扫频步长: 70MHz\n');
    fprintf(fid, '\n--- 边界伪影消除 ---\n');
    fprintf(fid, '使用重叠扩展计算域方法消除分区边界伪影\n');
    fprintf(fid, '重叠区长度: 1.5倍泵浦脉冲长度 (15m)\n');
    fprintf(fid, '第一分区扩展: 0-%.0fm -> 0-%.0fm\n', partition_length, partition_length + 15);
    fprintf(fid, '第二分区扩展: %.0fm-200m -> %.0fm-200m\n', partition_length, partition_length - 15);
    fprintf(fid, '最后一段处理: 完全排除，避免不可消除的光纤末端伪影\n');
    fprintf(fid, '\n--- 数据归一化说明 ---\n');
    fprintf(fid, '每个频率点的斯托克斯信号单独归一化到[0,1]范围\n');

    fclose(fid);
    fprintf('数据集摘要已保存到: %s\n', summary_file);
end

% *** 修改：数据可视化函数（不包含基线段版本）***
function visualize_dl_sample(data_path, sample_idx, dataset_type, segment_length)
    % 可视化深度学习样本数据（不包含基线段版本）
    
    if nargin < 3
        dataset_type = 'train';
    end

    sample_file = fullfile(data_path, dataset_type, sprintf('%s_sample_%04d.mat', dataset_type, sample_idx));

    if ~exist(sample_file, 'file')
        error('样本文件不存在: %s', sample_file);
    end

    fprintf('加载样本文件: %s\n', sample_file);
    data = load(sample_file);
    sample_data = data.sample_data;

    % 创建大图形窗口
    figure('Name', sprintf('BOTDA深度学习样本 %d 可视化', sample_idx), 'Position', [100 100 1600 1000]);

    % *** 子图1: 所有段的斯托克斯信号三线图展示***
    subplot(2, 1, 1); % 上半部分
    
    stokes_signals = sample_data.stokes_signals;
    num_segments = length(stokes_signals);
    Delta_t = sample_data.Delta_t;
    
    % 使用不同颜色表示不同频率点
    colors = ['r', 'g', 'b']; % 红、绿、蓝分别对应3个频率点
    freq_labels = {'10.73 GHz', '10.80 GHz', '10.87 GHz'};
    
    hold on;
    legend_handles = [];
    legend_labels = {};
    
    % 为每个段绘制三线图
    for seg_idx = 1:num_segments
        if ~isempty(stokes_signals{seg_idx})
            signal_matrix = stokes_signals{seg_idx}; % [时间点数 × 频率点数]
            time_points = size(signal_matrix, 1);
            
            % 计算真实时间轴 (ns)
            time_axis = (0:time_points-1) * Delta_t * 1e9; % 转换为ns
            
            % 绘制该段的3个频率点信号（三线图方式）
            for freq_idx = 1:min(3, size(signal_matrix, 2))
                freq_signal = signal_matrix(:, freq_idx);
                
                % 构造与时间向量等长的段编号数组，用作 y 轴
                y_val = seg_idx * ones(size(time_axis));  % 段编号从1开始
                
                % 绘制三线图
                h = plot3(time_axis, y_val, freq_signal, colors(freq_idx), 'LineWidth', 1.5, 'LineStyle', '-');
                
                % 只为第一段添加图例
                if seg_idx == 1
                    legend_handles(end+1) = h;
                    legend_labels{end+1} = freq_labels{freq_idx};
                end
            end
        end
    end
    
    hold off;
    xlabel('时间 (ns)', 'FontSize', 12);
    ylabel('段编号', 'FontSize', 12);
    zlabel('归一化斯托克斯功率', 'FontSize', 12);
    title(sprintf('样本%d 所有段斯托克斯信号三线图（power_{total}加噪声后归一化）', sample_idx), 'FontSize', 14);
    legend(legend_handles, freq_labels, 'Location', 'best');
    grid on;
    view(3); % 3D视角
    
    % 设置y轴刻度（1-9段）
    set(gca, 'YTick', 1:num_segments);
    ylim([0.5, num_segments + 0.5]);
    
    % 添加文本说明
    text(max(xlim)*0.7, max(ylim)*0.9, max(zlim)*0.9, ...
        sprintf('总段数: %d\n有效段: 第1-9段', num_segments), ...
        'FontSize', 10, 'FontWeight', 'bold', 'Color', 'red');

    % *** 修改：子图2: BFS分布标签（x轴每20m标记）***
    subplot(2, 1, 2); % 下半部分
    % 原始BFS分布用蓝色实线（前面仿真计算时使用的）
    plot(sample_data.fiber_positions, sample_data.frequency_shift_distribution, 'b-', 'LineWidth', 3);
    hold on;
    % 高斯平滑后用红色虚线（用于模型训练的标签）
    plot(sample_data.fiber_positions, sample_data.frequency_shift_distribution_filtered, 'r--', 'LineWidth', 2);
    
    % 标记应变区域
    strain_regions = sample_data.strain_regions;
    % 计算两个分布的y轴范围
    y_max = max([max(sample_data.frequency_shift_distribution), max(sample_data.frequency_shift_distribution_filtered)]);
    y_min = min([min(sample_data.frequency_shift_distribution), min(sample_data.frequency_shift_distribution_filtered)]);
    y_range = max(abs([y_max, y_min])) * 2;
    
    for i = 1:size(strain_regions, 1)
        region_start = strain_regions(i, 1);
        region_end = strain_regions(i, 2);
        region_shift = strain_regions(i, 3);
        region_length = region_end - region_start;
        
        % 绘制应变区域背景
        fill([region_start, region_end, region_end, region_start], ...
             [y_min-0.1*y_range, y_min-0.1*y_range, y_max+0.1*y_range, y_max+0.1*y_range], ...
             'yellow', 'FaceAlpha', 0.3, 'EdgeColor', 'none');
        
        % 添加区域标签
        text((region_start+region_end)/2, y_max+0.05*y_range, ...
            sprintf('%.1fm\n%+.2fMHz', region_length, region_shift), ...
            'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'FontSize', 10);
    end
    
    hold off;
    xlabel('光纤位置 (m)', 'FontSize', 12);
    ylabel('频移 (MHz)', 'FontSize', 12);
    title('BFS分布标签', 'FontSize', 14);
    legend('原始BFS分布（仿真计算）', '高斯平滑（训练标签）', 'Location', 'best');
    grid on;
    xlim([0, sample_data.fiber_length]); % 只显示有效光纤范围

    % *** 关键修改：设置x轴标签每20m（segment_length间隔）显示一次 ***
    x_min = 0;
    x_max = sample_data.fiber_length;
    x_tick_start = 0;  % 从0开始
    x_tick_end = floor(x_max / segment_length) * segment_length;   % 到最后一个20的倍数
    x_ticks = x_tick_start:segment_length:x_tick_end;              % 每20m一个刻度
    set(gca, 'XTick', x_ticks);

    % *** 新增：在图内x轴稍微上方添加段编号标注（在每个段的中间位置）***
    y_text_pos = y_max - 0.1*y_range;  % 段编号标注位置（在图内，x轴上方一点）

    % 第1-9段：每段20m，中间位置为段开始+10m
    for seg_num = 1:9
        seg_center = (seg_num - 1) * segment_length + segment_length/2;  % 段中心位置
        text(seg_center, y_text_pos, sprintf('段%d', seg_num), ...
            'HorizontalAlignment', 'center', 'FontWeight', 'bold', ...
            'FontSize', 9, 'Color', 'black', 'BackgroundColor', 'white', ...
            'EdgeColor', 'black', 'Margin', 2);
    end
    
    % *** 修改：创建独立的figure显示所有段的详细信号（两行五列布局）***
    figure('Name', sprintf('段1-9详细信号 - 样本%d', sample_idx), 'Position', [150 50 1600 900]);

    % 计算子图布局：两行，每行最多5个子图
    total_segments = num_segments;
    cols = 5;  % 每行5个子图
    rows = 2;  % 两行

    % 显示所有段的详细信号
    for seg_idx = 1:total_segments
        if seg_idx <= rows * cols  % 确保不超过子图数量限制
            subplot(rows, cols, seg_idx);

            if ~isempty(stokes_signals{seg_idx})
                signal_matrix = stokes_signals{seg_idx};
                time_points = size(signal_matrix, 1);
                time_axis = (0:time_points-1) * Delta_t * 1e9; % 转换为ns

                hold on;
                for freq_idx = 1:min(3, size(signal_matrix, 2))
                    plot(time_axis, signal_matrix(:, freq_idx), colors(freq_idx), 'LineWidth', 1.5);
                end
                hold off;

                % 确定段的类型和位置信息（段1-9）
                seg_type = sprintf('段%d', seg_idx);
                seg_start = (seg_idx - 1) * segment_length;
                seg_end = seg_idx * segment_length;
                seg_range = sprintf('(%.0f-%.0fm)', seg_start, seg_end);

                xlabel('时间 (ns)', 'FontSize', 9);
                ylabel('归一化功率', 'FontSize', 9);
                title(sprintf('%s\n%s', seg_type, seg_range), 'FontSize', 10, 'FontWeight', 'bold');

                % 只在第一个子图显示图例
                if seg_idx == 1
                    legend(freq_labels, 'Location', 'northeast', 'FontSize', 8);
                end

                grid on;
                set(gca, 'FontSize', 8);
            else
                % 如果信号为空，显示提示
                text(0.5, 0.5, '无信号数据', 'HorizontalAlignment', 'center', ...
                     'VerticalAlignment', 'middle', 'FontSize', 12, 'Color', 'red');
                title(sprintf('段%d (无数据)', seg_idx - 1), 'FontSize', 10);
                set(gca, 'XTick', [], 'YTick', []);
            end
        end
    end

    % 添加总标题
    sgtitle(sprintf('样本%d - 所有段详细信号对比 (共%d段)', sample_idx, total_segments), ...
           'FontSize', 16, 'FontWeight', 'bold');

    % 总标题
    sgtitle(sprintf('BOTDA学习样本 %d 数据可视化 (%s集) - 应变区域数: %d - 包含基线段', ...
           sample_idx, dataset_type, sample_data.num_strain_regions), ...
           'FontSize', 16, 'FontWeight', 'bold');

    % *** 新增：Figure 3 - 展示归一化的BFS标签 ***
    figure('Name', sprintf('归一化的BFS标签 - 样本%d', sample_idx), 'Position', [250 150 1400 600]);

    % 绘制归一化的BFS分布
    plot(sample_data.fiber_positions, sample_data.frequency_shift_distribution_normalized, 'b-', 'LineWidth', 3);
    hold on;

    % 标记应变区域
    y_max = max(sample_data.frequency_shift_distribution_normalized);
    y_min = min(sample_data.frequency_shift_distribution_normalized);
    y_range = max(abs([y_max, y_min])) * 2;
    if y_range == 0, y_range = 1; end

    for i = 1:size(strain_regions, 1)
        region_start = strain_regions(i, 1);
        region_end = strain_regions(i, 2);
        region_shift = strain_regions(i, 3);

        fill([region_start, region_end, region_end, region_start], ...
             [y_min-0.1*y_range, y_min-0.1*y_range, y_max+0.1*y_range, y_max+0.1*y_range], ...
             'yellow', 'FaceAlpha', 0.3, 'EdgeColor', 'none');

        text((region_start+region_end)/2, y_max+0.05*y_range, ...
            sprintf('%.1fm\n%+.2fMHz', region_end-region_start, region_shift), ...
            'HorizontalAlignment', 'center', 'FontWeight', 'bold', 'FontSize', 9);
    end

    xlabel('光纤位置 (m)', 'FontSize', 12);
    ylabel('归一化BFS值', 'FontSize', 12);
    title('归一化的BFS标签（高斯平滑后全局极值归一化到[0,1]，用于训练）', 'FontSize', 14);
    grid on;
    xlim([0, sample_data.fiber_length]);
    ylim([y_min-0.2*y_range, y_max+0.2*y_range]);

    % 添加归一化信息
    text(0.02, 0.98, sprintf('全局极值归一化到[0,1]\n极值范围: [%.2f, %.2f] MHz\n高斯平滑：0.5m上升沿\n用于深度学习训练', fixed_freq_shift_range(1), fixed_freq_shift_range(2)), ...
        'Units', 'normalized', 'VerticalAlignment', 'top', ...
        'FontSize', 10, 'BackgroundColor', 'white', 'EdgeColor', 'black');

    fprintf('可视化完成（包含基线段，快速方法）！\n');
    fprintf('样本包含 %d 个段，其中基线段2个，有效段9个\n', num_segments);
    fprintf('基线段位置: 第0段和第%d段（使用run_one_freq计算）\n', num_segments-1);
    fprintf('应变区域数量: %d\n', sample_data.num_strain_regions);

    % 显示应变区域信息
    fprintf('应变区域详情（限制在有效光纤长度 %.1fm 内）:\n', sample_data.fiber_length);
    for i = 1:size(strain_regions, 1)
        region_length = strain_regions(i, 2) - strain_regions(i, 1);
        fprintf('  区域%d: %.1f-%.1fm (长度%.1fm), %+.2fMHz\n', i, ...
               strain_regions(i,1), strain_regions(i,2), region_length, strain_regions(i,3));
    end

    % 显示SNR信息
    fprintf('\nSNR信息（每个段的每个频率点）:\n');
    for seg_idx = 1:min(3, num_segments)  % 只显示前3个段的SNR信息
        if ~isempty(sample_data.snr_info{seg_idx})
            snr_data = sample_data.snr_info{seg_idx};
            fprintf('  段%d SNR: ', seg_idx-1);
            for freq_idx = 1:length(snr_data.snr_dB)
                fprintf('%.1fdB ', snr_data.snr_dB(freq_idx));
            end
            fprintf('\n');
        end
    end
    
    % 检查是否使用了基线信号
    if isfield(sample_data, 'realistic_baseline') && sample_data.realistic_baseline
        fprintf('✅ 使用真实布里渊响应基线信号（run_one_freq方法）\n');
    else
        fprintf('⚠️  使用简化基线信号\n');
    end
end

% *** 快速单频率计算函数（真实响应求和方法）***
function [A2_complex, A2_linear, power_total, power_linear, q_meet] = run_one_freq(Omega, ...
    A1_linear, A2_linear, A2_linear_receiver, partition_start_calc, ...
    current_Omega_B, current_z, t_local, Delta_t, dz, idx_meet_z, ...
    g0, gB, gamma, Fiber_Aeff, vc, alpha, judge)
    % 使用真实响应求和求解方法的快速计算函数
    
    Nt_local = numel(t_local);
    Nz = numel(current_z);
    
    % 初始化斯托克斯信号贡献
    a_s_contributions_at_partition_left_time = zeros(Nz, Nt_local, 'single');
    % 初始化声场
    q_store = zeros(Nz, Nt_local, 'single');
    
    % 使用double精度计算复指数，避免single精度在高频时的相位误差
    t_local_double = double(t_local);
    Omega_double = double(Omega);
    
    % 遍历每个空间位置计算其对接收端斯托克斯信号的贡献
    for iz = 1:Nz
        % 当前位置的泵浦和探测光场（局部）
        current_Pump_at_z_iz = A1_linear(iz, :);
        current_Probe_at_z_iz = A2_linear(iz, :);
        
        % 使用未化简的g(t)，得到√(Ω_B^2-γ^2 )
        Omega_eff_iz = sqrt(current_Omega_B(iz)^2 - gamma^2);
        
        % 声场核函数（使用double精度计算复指数）
        complex_exp_term = exp(1i * Omega_double * t_local_double);
        g_ac_iz_kernel = single((sqrt(2*pi) / Omega_eff_iz) ...
                        .* complex_exp_term ...
                        .* exp(-gamma * t_local_double) ...
                        .* sin(Omega_eff_iz * t_local_double));
        
        % 驱动项系数
        K_factor_S_Q = -1i * gB * gamma * current_Omega_B(iz) / Fiber_Aeff;
        
        % 驱动项（泵浦和探测光的乘积）
        S_Q_at_z_iz = K_factor_S_Q * current_Pump_at_z_iz .* conj(current_Probe_at_z_iz);
        
        % 计算声场（通过卷积）
        Q_at_z_iz_conv = Delta_t * conv(g_ac_iz_kernel, S_Q_at_z_iz, 'full');
        Q_at_z_iz = [0, Q_at_z_iz_conv(1:Nt_local-1)];

        q_store(iz, :) = Q_at_z_iz; % 存储真实响应方法产生的声场

        % 在位置iz处斯托克斯光的产生速率
        source_rate_delta_as = (g0 / 2) * current_Pump_at_z_iz .* conj(Q_at_z_iz);
        
        % 在位置iz新产生的斯托克斯光
        delta_As_generated = source_rate_delta_as * dz;
        
        % 计算传播到扩展分区左端的时间延迟和衰减
        delay_to_receiver = round((current_z(iz) - partition_start_calc) / vc / Delta_t);
        atten_factor = exp(-alpha * (current_z(iz) - partition_start_calc));
        
        % 应用时间延迟和衰减
        shifted_signal = zeros(1, Nt_local, 'single');
        if delay_to_receiver >= 0 && delay_to_receiver < Nt_local
            end_idx = Nt_local - delay_to_receiver;
            if end_idx > 0
                shifted_signal(1+delay_to_receiver:Nt_local) = ...
                    delta_As_generated(1:end_idx) * atten_factor;
            end
        end
 
        % 保存当前位置的贡献
        a_s_contributions_at_partition_left_time(iz, :) = shifted_signal;
    end
    
    % 叠加所有位置的斯托克斯信号贡献
    current_total_sbs_field = sum(a_s_contributions_at_partition_left_time, 1);
    
    % 在扩展分区左端计算总的接收信号
    total_A2_at_partition_left = A2_linear_receiver + current_total_sbs_field;

    % 计算从本扩展分区左端传回到全局z=0的损耗
    % 只计算真实分区中的正常段损耗；对于虚拟分区中的基线段，只考虑传播到当前虚拟分区左端的损耗，不考虑传播到z=0的损耗
    if judge
        loss_extra = exp(-alpha * partition_start_calc);
    else
        loss_extra = 1;
    end

    % 得到z=0处探测光和线性传播结果的幅度和功率大小
    A2_complex = complex(total_A2_at_partition_left * loss_extra);
    A2_linear = complex(A2_linear_receiver * loss_extra);
    power_total = abs(A2_complex).^2;
    power_linear = abs(A2_linear).^2;
    q_meet = q_store(idx_meet_z, :);
end

% 颜色生成工具函数
function color = indexToColor(idx)
    golden_ratio_conjugate = 0.618033988749895;
    h = mod(idx * golden_ratio_conjugate, 1);
    s = 0.85;
    v = 0.9;
    color = hsv2rgb([h s v]);
end


% *** 高斯平滑函数 ***
function smoothed = apply_gaussian_smoothing( ...
        data, dx, target_edge, edgeFrac, truncSig)
% APPLY_GAUSSIAN_SMOOTHING  高斯平滑且保峰值，但让边沿=target_edge
%
%   smoothed = apply_gaussian_smoothing(data, dx, target_edge)
%   smoothed = apply_gaussian_smoothing(data, dx, target_edge, edgeFrac, truncSig)
%
% INPUT
%   data        1-D 数据向量
%   dx          采样间隔 (m)
%   target_edge 目标上/下沿宽度 (m)，默认为 0-100 % 宽
%   edgeFrac    [f1 f2]，默认 [0.01 0.99]；若要 10-90 % 宽的上升/下降沿，传 [0.1 0.9]
%   truncSig    核截断 σ 倍数，默认 4
%
% OUTPUT
%   smoothed    平滑后的数据（峰值恢复，边沿宽度=target_edge）

    if nargin < 4 || isempty(edgeFrac), edgeFrac = [0.01 0.99]; end
    if nargin < 5 || isempty(truncSig), truncSig = 4;           end

    % ---------- σ ----------
    f1 = edgeFrac(1);  f2 = edgeFrac(2);
    sigma = target_edge / (sqrt(2)*(erfinv(2*f2-1) - erfinv(2*f1-1)));

    % ---------- 高斯核 ----------
    halfLen = ceil(truncSig*sigma/dx);
    x       = (-halfLen:halfLen)*dx;
    g       = exp(-0.5*(x/sigma).^2);
    g       = g / sum(g);

    % ---------- 卷积 ----------
    convRaw = conv(data, g, 'same');

    % ---------- 局部幅度补偿 ----------
    mask   = double(data ~= 0);
    weight = conv(mask, g, 'same');
    smoothed = convRaw;                     % 先复制

    % 找到所有连续的“1”区间
    if any(mask)
        seg = bwlabel(mask);                % 需要 Image Processing Toolbox
        for k = 1:max(seg)
            idxSeg = (seg == k);
            wMax   = max(weight(idxSeg));   % 该峰自己的最大权重
            smoothed(idxSeg) = smoothed(idxSeg) / (wMax + eps);
        end
    end
    % 如果没有 Image Processing Toolbox，可以用简单循环代替：
    % 
    % run = false; start = 0;
    % for i = 1:numel(mask)
    %     if mask(i) && ~run            % 起点
    %         run = true; start = i;
    %     elseif ~mask(i) && run        % 终点
    %         run = false;
    %         idxSeg = start:i-1;
    %         wMax   = max(weight(idxSeg));
    %         smoothed(idxSeg) = smoothed(idxSeg) / (wMax + eps);
    %     end
    % end
end

% *** 给总探测功率添加噪声函数 ***
function power_total_noisy = addNoiseToPowerTotal(power_total, snrRange_dB, enable_noise)
% addNoiseToPowerTotal  给探测到的总功率添加白高斯噪声
%
%   输入参数
%     power_total   : 数组，探测到的总功率信号（可以是列向量或矩阵）
%     snrRange_dB   : [SNRmin SNRmax] — 期望的SNR窗口，例如 [23 33]
%     enable_noise  : 逻辑值，是否启用噪声（默认true）
%
%   输出参数
%     power_total_noisy : 加噪声后的总功率信号
%     snrUsed          : 本次调用随机选择的SNR (dB)
%     sigmaUsed        : 实际注入的零均值高斯噪声的σ

    arguments
        power_total double
        snrRange_dB (1,2) double = [23 33]
        enable_noise (1,1) logical = true
    end

    % 如果不启用噪声，直接返回原信号
    if ~enable_noise
        power_total_noisy = power_total;
        snrUsed = inf;
        sigmaUsed = 0;
        return;
    end

    % 1) 计算输入信号功率
    P_sig = sum(power_total) / length(power_total);

    % 2) 在请求窗口内随机选择SNR
    snrUsed = snrRange_dB(1) + rand() * diff(snrRange_dB);

    % 噪声功率
    Power_noise = P_sig / 10^(snrUsed/10);

%     % 3) 将SNR转换为所需的噪声方差
%     sigmaUsed = sqrt(Power_noise);

    % 4) 注入白高斯噪声 (零均值)
    power_total_noisy = power_total + Power_noise * randn(size(power_total));

end

% *** 斯托克斯信号(加噪后)归一化函数 ***
function sigN = normalizeStokesSignal(sigPower)
% normalizeStokesSignal  对斯托克斯信号进行min-max归一化到[0,1]
%
%   输入参数
%     sigPower      : 列向量，斯托克斯功率信号
%
%   输出参数
%     sigN          : min-max归一化的信号 [0,1]

    arguments
        sigPower (:,1) double
    end

    % Min-max归一化到[0,1]
    sMin = min(sigPower);
    sMax = max(sigPower);

    if sMax > sMin
        sigN = (sigPower - sMin) / (sMax - sMin);
    else
        sigN = zeros(size(sigPower));  % 平线回退
    end
end


% *** BFS标签全局极值min-max归一化/反归一化函数 ***
function bfsN = normalizeBFS_fixed(bfs, freq_shift_range, statsFile, mode)
% normalizeBFS_fixed  使用全局极值进行min-max归一化/反归一化到[0,1]   全局归一化
%
%   bfsN = normalizeBFS_fixed(bfs, freq_shift_range, 'bfs_stats.mat', 'fwd'); % 归一化
%   bfs  = normalizeBFS_fixed(bfsN, [], 'bfs_stats.mat', 'inv');             % 反归一化
%
%   输入参数
%     bfs             : 列向量，BFS频移值
%     freq_shift_range: [min_freq, max_freq] 固定的频移范围，例如 [-40, 50]
%     statsFile       : 保存极值的 .mat 文件
%     mode            : 'fwd' | 'inv'
%
%   归一化公式 (min-max):
%     forward : x' = (x - min) / (max - min)  ∈ [0,1]
%     inverse : x  = x' * (max - min) + min

    arguments
        bfs (:,1) double
        freq_shift_range (1,2) double = [-40, 50] % 全局BFS极值（保持默认值以兼容旧调用）
        statsFile (1,1) string = ''
        mode (1,:) char {mustBeMember(mode,{'fwd','inv'})} = 'fwd'
    end

    switch mode
        case 'fwd'   % ========== 归一化 ==========
            % 使用固定的极值
            bfs_min = freq_shift_range(1);
            bfs_max = freq_shift_range(2);

            % 保存极值到文件（用于反归一化）
            if ~isempty(statsFile)
                stats = struct('min', bfs_min, 'max', bfs_max);
                save(statsFile, 'stats');
            end

            % Min-max归一化到 [0,1]
            bfsN = (bfs - bfs_min) / (bfs_max - bfs_min);

        case 'inv'   % ========== 反归一化 ==========
            if isempty(statsFile) || ~isfile(statsFile)
                error("找不到 %s，无法反归一化！", statsFile);
            end

            % 从文件读取极值
            tmp = load(statsFile, "stats");
            stats = tmp.stats;
            bfs_min = stats.min;
            bfs_max = stats.max;

            % 反归一化
            bfsN = bfs * (bfs_max - bfs_min) + bfs_min;
    end
end
