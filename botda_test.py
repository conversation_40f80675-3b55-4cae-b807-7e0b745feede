# ---------------------------------------------------------------
#  BOTDA 超分辨率网络测试脚本
#  
#  功能：
#  1. 加载训练好的模型
#  2. 在测试集上评估模型性能
#  3. 生成预测结果和可视化
#  4. 计算各种评估指标
#  
#  数据格式：
#  - 输入：归一化后的斯托克斯信号 [0,1]
#  - 标签：高斯平滑后归一化的BFS分布 [0,1]（不用于训练）
#  - 输出：预测的BFS分布 [0,1]
# ---------------------------------------------------------------

import os
import glob
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
import scipy.io as sio
from tqdm import tqdm
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Optional
import json
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

from botda_model import HybridUNetTCN
from botda_train import BOTDADataset


class BOTDATestDataset(BOTDADataset):  # BOTDATestDataset继承自父类 BOTDADataset，因此这个类自动就拥有了 BOTDADataset 的所有方法，天生就就有了 __len__ 和 __getitem__ 方法，不需要再重复写一遍了
    """
    BOTDA测试数据集类
    继承自训练数据集，但专门用于测试
    """
    
    def __init__(self, data_dir: str, window_size: int = 5, max_samples: Optional[int] = None):  # 这个 __init__ 方法是 BOTDATestDataset 唯一自己定义的东西，作用就是对从父类那里继承来的行为进行一点点“微调”或“定制”，让它专门服务于测试
        """
        初始化测试数据集
        
        参数：
        - data_dir: 数据目录路径
        - window_size: 窗口大小（段数）
        - max_samples: 最大样本数（用于调试）
        """
        super().__init__(data_dir, 'test', window_size, max_samples)
        # super(): 这是一个特殊的函数，它代表“我的父类”。在这里，super()就等同于BOTDADataset。
        # .__init__(...): 调用父类的__init__方法。也就是说，让父类来完成大部分的初始化工作。
        # 工作流程：
        # 当后面在 main 函数中创建 BOTDATestDataset 的实例时：test_dataset = BOTDATestDataset(DATA_DIR, WINDOW_SIZE, MAX_SAMPLES)
        # Python 会调用 BOTDATestDataset 的 __init__ 方法。
        # __init__ 方法立刻调用父类 BOTDADataset 的 __init__ 方法。
        # 父类 BOTDADataset 的 __init__ 方法接收到指令后，就会去 DATA_DIR 目录下寻找名为 test 的子文件夹，并加载里面的数据文件。
        # 初始化完成后，test_dataset 这个对象就准备好了。它包含了所有测试数据的信息。
        # 当 DataLoader 需要数据时，它会调用 test_dataset这个对象的 __getitem__ 方法。由于 BOTDATestDataset 这个子类自己没有定义 __getitem__，就会自动使用从父类 BOTDADataset 继承来的__getitem__，实现数据的读取和处理。

def load_model(model_path: str,
               window_size: int = 5,
               base_channels: int = 64,
               time_points_per_segment: int = 400,
               space_points_per_segment: int = 200,
               device: torch.device = None) -> HybridUNetTCN:
    """
    加载训练好的HybridUNetTCN模型

    参数：
    - model_path: 模型文件路径
    - window_size: 窗口大小
    - base_channels: 基础通道数
    - time_points_per_segment: 每段时间点数
    - space_points_per_segment: 每段空间点数
    - device: 计算设备

    返回：
    - 加载的模型
    """
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # 创建HybridUNetTCN模型
    # 调用 HybridUNetTCN 类的构造函数，传入所有配置参数，创建一个全新的模型实例，它目前所有的权重（weights）和偏置（biases）都是随机初始化的
    model = HybridUNetTCN(
        input_freq_channels=3,
        window_segments=window_size,
        time_points_per_segment=time_points_per_segment,
        output_segments=window_size - 1,
        space_points_per_segment=space_points_per_segment,
        base_channels=base_channels,
        depth=4,
        bottleneck_attn_heads=8
    ).to(device)
    
    # 加载模型权重
    # 兼容不同版本的PyTorch，新版本使用weights_only=True避免安全警告
    try:
        # PyTorch 2.0+ 版本支持weights_only参数
        checkpoint = torch.load(model_path, map_location=device, weights_only=True)
    except TypeError:
        # PyTorch 1.x 版本不支持weights_only参数，使用传统方式
        checkpoint = torch.load(model_path, map_location=device)
    # torch.load(): PyTorch的“读档”函数。它会读取model_path指定的文件；
    # map_location = device: 告诉PyTorch，在加载数据时，应该把所有张量都放到device上。可以避免一个常见的问题：在有GPU的机器上训练并保存了模型，然后想在只有CPU的机器上加载它。如果没有map_location，程序会报错，因为它找不到GPU。有了这个参数，PyTorch会自动处理设备映射。
    # 加载模型权重（兼容旧模型）
    try:
        model.load_state_dict(checkpoint['model_state_dict'])
    except RuntimeError as e:
        print(f"警告：模型结构不匹配，尝试部分加载: {e}")
        # 尝试部分加载，忽略不匹配的键
        model_dict = model.state_dict()
        pretrained_dict = {k: v for k, v in checkpoint['model_state_dict'].items()
                          if k in model_dict and v.size() == model_dict[k].size()}
        model_dict.update(pretrained_dict)
        model.load_state_dict(model_dict)
        print(f"成功加载 {len(pretrained_dict)}/{len(checkpoint['model_state_dict'])} 个参数")
    # model.load_state_dict(): 模型对象的一个方法，专门用来加载状态字典（state dictionary）
    # checkpoint['model_state_dict']: 从检查点字典中，只取出最关心的部分——模型的权重和偏置
    
    print(f"模型已从 {model_path} 加载")
    print(f"训练轮数: {checkpoint.get('epoch', 'Unknown')}")
    print(f"验证损失: {checkpoint.get('val_loss', 'Unknown')}")
    
    return model

def handle_overlapping_predictions(predictions: np.ndarray, targets: np.ndarray,
                                 test_dataset) -> tuple:
    """
    处理重叠预测：对每个文件内的重叠段取平均值

    参数：
    - predictions: (N, 800) 或 (N, 4, 200) 所有窗口的预测结果
    - targets: (N, 800) 或 (N, 4, 200) 所有窗口的真实标签
    - test_dataset: 测试数据集，用于获取窗口信息

    返回：
    - processed_predictions: 处理后的预测结果 (N, 4, 200)
    - processed_targets: 处理后的真实标签 (N, 4, 200)
    """
    print("处理重叠预测...")

    # 修复：确保输入数据是3维格式 (N, 4, space_points)
    output_segments = 4
    space_points = 200

    if predictions.ndim == 2:  # (N, 800)
        predictions = predictions.reshape(-1, output_segments, space_points)
        targets = targets.reshape(-1, output_segments, space_points)

    print(f"输入数据形状: predictions={predictions.shape}, targets={targets.shape}")

    # 获取数据集信息
    windows_per_sample = test_dataset.windows_per_sample
    num_files = len(test_dataset.file_paths)
    window_size = test_dataset.window_size
    num_segments = len(test_dataset.valid_segments)

    print(f"每个文件窗口数: {windows_per_sample}")
    print(f"测试集文件数: {num_files}")
    print(f"每个文件段数: {num_segments}")

    # 检查是否有向前扩展窗口
    stride = window_size - 1
    standard_windows = 0
    pos = 0
    # 计算标准窗口的数量
    while pos + window_size <= num_segments:
        standard_windows += 1
        pos += stride

    has_extra_window = False
    overlap_segments = []

    if standard_windows > 0:
        last_segment_idx = num_segments - 1  # 最后一段的索引
        last_window_start = (standard_windows - 1) * stride  # 最后一个标准窗口的起始索引   从0开始
        last_window_end = last_window_start + window_size - 1  # 最后一个标准窗口的结束索引   从0开始

        if last_segment_idx > last_window_end:  # 说明最后一段没有被最后一个标准窗口包含进去，也就是说有扩展窗口
            has_extra_window = True
            extra_window_start = num_segments - window_size  # 扩展窗口的起始位置索引   从0开始
            extra_window_end = num_segments - 1  # 扩展窗口的结束位置索引   从0开始

            # 计算重叠段 - 注意：每个窗口只预测前4段，不是全部5段，因此找的是这四段的重叠，而不是窗口的五段有哪些重叠了
            # 注意这里生成列表的时候，只生成到右边界的前一个元素，即暗含了不预测窗口最后一段
            last_standard_predicted = list(range(last_window_start, last_window_end))  # 最后一个标准窗口预测的段
            extra_window_predicted = list(range(extra_window_start, extra_window_end))  # 向前扩展窗口预测的段
            overlap_segments = list(set(last_standard_predicted) & set(extra_window_predicted))  # 取最后一个标准窗口和扩展窗口的交集，即找到重叠段

            print(f"检测到向前扩展窗口")
            print(f"最后标准窗口预测段: {last_standard_predicted}")
            print(f"向前扩展窗口预测段: {extra_window_predicted}")
            print(f"重叠段: {overlap_segments}")

    if not has_extra_window:
        print("无重叠段，直接返回")
        return predictions, targets

    # 处理重叠预测
    # 对每个文件内的重叠段进行处理
    processed_predictions = []
    processed_targets = []

    # 根据 windows_per_sample 从 predictions 数组中，切出只属于当前文件的滑窗样本
    for file_idx in range(num_files):
        # 当前这个文件下，得到包含的滑窗样本在全局的开始索引file_start_idx，而file_end_idx表示下一个文件的第一个滑窗样本在全局的索引，因为下面切片的时候，只会切片到右边界的前一个，所以这里需要取到下一个文件的第一个滑窗样本，从0开始
        file_start_idx = file_idx * windows_per_sample
        file_end_idx = file_start_idx + windows_per_sample

        # 当前文件下，所有的预测滑窗样本
        file_predictions = predictions[file_start_idx:file_end_idx]  # predictions 维度是(N, 4, space_points)，这里是对第一个维度进行切片，从file_start_idx到file_end_idx-1，得到(windows_per_sample, 4, space_points)
        file_targets = targets[file_start_idx:file_end_idx]

        if windows_per_sample == standard_windows + 1:  # 确认当前文件确实包含一个扩展窗口
            # 最后两个窗口需要处理重叠（最后一个标准窗口和向前扩展窗口）
            # 将这个文件的预测file_predictions分成两部分：所有标准窗口的预测，和最后一个扩展窗口的预测
            standard_pred = file_predictions[:-1]  # 取出所有标准窗口预测的结果    从头开始到倒数第一个元素之前结束，-1是最后一个元素的索引，同样取左不取右    也是对file_predictions的第一个维度进行切片
            extra_pred = file_predictions[-1:]     # 取出最后的向前扩展窗口预测的结果   从倒数第一个元素开始一直取到结尾，所以只会取出最后一个元素

            standard_target = file_targets[:-1]
            extra_target = file_targets[-1:]

            # 对重叠段取平均值
            for seg_idx in overlap_segments:  # 循环遍历前面找到的重叠段索引
                # 计算该重叠段在标准窗口和扩展窗口中的相对位置
                std_relative_idx = seg_idx - last_window_start  # 得到在最后一个标准窗口的第几段，从0开始
                extra_relative_idx = seg_idx - extra_window_start  # 得到在向前扩展窗口的第几段，从0开始

                if 0 <= std_relative_idx < window_size - 1 and 0 <= extra_relative_idx < window_size - 1:  # 确保重叠段在各自窗口的局部索引没有超出范围
                    # 取平均值
                    avg_pred = (standard_pred[-1, std_relative_idx] + extra_pred[0, extra_relative_idx]) / 2  # -1表示取最后一个标准窗口预测结果，std_relative_idx表示取对应的重叠段的预测结果，即~200个空间点的bfs分布的Numpy数组
                    # 0表示取的第一个向前扩展窗口，不过本来也就只有一个，extra_relative_idx也表示取对应的重叠段
                    avg_target = (standard_target[-1, std_relative_idx] + extra_target[0, extra_relative_idx]) / 2  # 对于重叠段，标签肯定是一样的，相加除以2结果还是不变

                    # 更新两个窗口的值
                    # 将平均后的结果，同时写回到standard_pred和extra_pred的相应位置。这样，两个窗口对这个重叠段的预测就变得完全一致了
                    standard_pred[-1, std_relative_idx] = avg_pred
                    extra_pred[0, extra_relative_idx] = avg_pred
                    standard_target[-1, std_relative_idx] = avg_target
                    extra_target[0, extra_relative_idx] = avg_target

            # 合并处理后的结果   得到的维度是(windows_per_sample, 4, space_points)
            file_processed_pred = np.concatenate([standard_pred, extra_pred], axis=0)
            file_processed_target = np.concatenate([standard_target, extra_target], axis=0)

            print(f"文件{file_idx}: 处理了重叠段{overlap_segments}")
        else:
            # 无向前扩展窗口，直接使用原始结果
            file_processed_pred = file_predictions
            file_processed_target = file_targets

        processed_predictions.append(file_processed_pred)
        processed_targets.append(file_processed_target)

    # 重新组合所有文件的结果     得到的维度是(N, 4, space_points)  N是所有的滑窗样本数    只是让重叠部分一样了，但是还是没有把重叠段变成一个
    final_predictions = np.concatenate(processed_predictions, axis=0)
    final_targets = np.concatenate(processed_targets, axis=0)

    print(f"重叠处理完成: {predictions.shape} -> {final_predictions.shape}")
    return final_predictions, final_targets

def evaluate_model(model: nn.Module,
                  test_loader: DataLoader,
                  test_dataset,  # 新增参数
                  device: torch.device) -> Tuple[Dict, List, List]:
    # 上面括号中每个参数后面的冒号表示类型提示，如test_loader: DataLoader表示期望接收一个名为 test_loader 的参数，并且这个参数应该是一个 torch.utils.data.DataLoader 对象。
    # 目的是增加代码的可读性和健壮性，但它本身并不强制执行。
    """
    评估模型性能
    
    参数：
    - model: 训练好的模型
    - test_loader: 测试数据加载器
    - device: 计算设备
    
    返回：
    - metrics: 评估指标字典
    - predictions: 预测结果列表
    - targets: 真实标签列表
    """
    # 设置模型为评估模式
    model.eval()
    
    all_predictions = []  # 预测结果
    all_targets = []  # 真实结果
    total_loss = 0.0
    num_batches = 0
    
    criterion = nn.MSELoss()  # 使用均方误差损失
    
    print("开始模型评估...")
    
    with torch.no_grad():
        pbar = tqdm(test_loader, desc="测试中")

        for batch_idx, (stokes, bfs_target) in enumerate(pbar):
            # 数据移到GPU
            stokes = stokes.to(device)
            bfs_target = bfs_target.to(device)
            
            # 前向传播
            bfs_pred = model(stokes)  # (B, output_segments, space_points_per_segment)

            # 调试：检查维度
            if batch_idx == 0:  # 只在第一个batch打印
                print(f"模型输出维度: {bfs_pred.shape}")
                print(f"目标维度: {bfs_target.shape}")

            # 关键修复：直接计算损失，不再flatten，保持几何对应关系
            loss = criterion(bfs_pred, bfs_target)  # (B,4,200) vs (B,4,200)
            total_loss += loss.item()
            num_batches += 1

            # 收集预测结果和真实标签，为了后续指标计算需要flatten
            all_predictions.append(bfs_pred.view(bfs_pred.size(0), -1).cpu().numpy())  # 仅为指标计算flatten
            all_targets.append(bfs_target.view(bfs_target.size(0), -1).cpu().numpy())  # 仅为指标计算flatten
            # .cpu()：将数据从GPU移回CPU，因为后续的Numpy和Scikit-learn操作通常在CPU上进行
            # .numpy(): 将PyTorch张量转换成Numpy数组

            # 更新 tqdm 进度条的后缀信息，实时显示当前批次的损失值
            pbar.set_postfix({'损失': f'{loss.item():.6f}'})
    
    # 合并所有批次的结果     维度是(N, 4, space_points)  N是所有的滑窗样本数
    predictions = np.concatenate(all_predictions, axis=0)
    targets = np.concatenate(all_targets, axis=0)
    # np.concatenate: 使用Numpy的concatenate函数将列表all_predictions（其中每个元素是一个批次的Numpy数组）合并成一个大的、完整的Numpy数组predictions。对all_targets做同样的操作
    # axis = 0表示沿着批次维度进行拼接
    
    # 计算损失值
    avg_loss = total_loss / num_batches  # 预测只有一个epoch，因为只需要预测一次完整的所有滑窗样本即可，不需要循环

    # *** 新增：处理重叠预测（在展平之前） ***
    predictions, targets = handle_overlapping_predictions(predictions, targets, test_dataset)

    # 加载BFS统计信息用于反归一化
    bfs_min, bfs_max = load_bfs_stats()

    # 反归一化预测和目标值以计算真实的性能指标
    predictions_denorm = denormalize_bfs(predictions, bfs_min, bfs_max)
    targets_denorm = denormalize_bfs(targets, bfs_min, bfs_max)

    # 展平数组计算全局指标（使用反归一化的数据）
    pred_flat = predictions_denorm.flatten()
    target_flat = targets_denorm.flatten()

    print(f"反归一化后的数据范围:")
    print(f"  预测值: [{pred_flat.min():.2f}, {pred_flat.max():.2f}] MHz")
    print(f"  真实值: [{target_flat.min():.2f}, {target_flat.max():.2f}] MHz")
    # 生成 test_loader的时候，由于在 DataLoader 中设置了 shuffle=False，因此 __getitem__ 方法会从 idx=0 开始，然后是 idx=1, idx=2, idx=3, ... 一直到 len(test_dataset) - 1 按索引顺序生成样本
    # 假如我有三个完整测试的样本test_sample_0001.mat，test_sample_0002.mat，test_sample_0003.mat，然后每个mat文件都代表一次完整的测量，因此每个文件里面一共有9段，假如窗长为5，那么一个mat文件就可以生成2个样本，那么一共就有3*2个新样本，然后顺序加载：
    # test_sample_0001.mat → 窗口0, 窗口1
    # test_sample_0002.mat → 窗口2, 窗口3
    # test_sample_0003.mat → 窗口4, 窗口5
    # 样本索引顺序：DataLoader会按顺序生成样本索引0, 1, 2, 3, 4, 5
    # 最终结果： pred_flat和target_flat是6个样本按顺序拼接的结果
    # 空间连续性：由于样本顺序对应文件顺序，最终结果相当于三根完整光纤的预测结果按顺序拼接，空间上完全没有错乱

    # 创建存放所有量化指标的字典
    # 除了'mse_loss'外，其它参数都是通过调用非常成熟和可靠的第三方库函数来计算的
    # 注意：将numpy类型转换为Python原生类型，以便JSON序列化
    # 注意：这里使用反归一化的数据计算指标，单位为MHz
    metrics = {
        'mse_loss': float(avg_loss),  # 平均损失（仍使用归一化数据）
        'mae': float(mean_absolute_error(target_flat, pred_flat)),  # 平均绝对误差 (MHz)
        'rmse': float(np.sqrt(mean_squared_error(target_flat, pred_flat))),  #　均方根误差 (MHz)
        'r2_score': float(r2_score(target_flat, pred_flat)),  # R²分数 (决定系数)，衡量模型解释能力的指标，越接近1越好
        'max_error': float(np.max(np.abs(pred_flat - target_flat))),  # 预测值和真实值之间的最大绝对差异 (MHz)
        'mean_pred': float(np.mean(pred_flat)),  # 预测值的均值 (MHz)
        'mean_target': float(np.mean(target_flat)),  # 真实值的均值 (MHz)
        'std_pred': float(np.std(pred_flat)),  # 预测值的标准差 (MHz)
        'std_target': float(np.std(target_flat))  # 真实值的标准差 (MHz)
    }
    
    return metrics, predictions, targets
    # predictions: 包含所有样本预测结果的完整Numpy数组
    # targets: 包含所有样本真实标签的完整Numpy数组


def extract_single_fiber_result(predictions: np.ndarray, targets: np.ndarray,
                               test_dataset, file_index: int) -> tuple:
    """
    提取单个文件的完整光纤预测结果，并融合重复段

    参数：
    - predictions: (N, 4, space_points) 所有窗口的预测结果
    - targets: (N, 4, space_points) 所有窗口的真实标签
    - test_dataset: 测试数据集
    - file_index: 要提取的文件索引（从0开始）

    返回：
    - fiber_prediction: (total_segments, space_points) 完整光纤预测结果
    - fiber_target: (total_segments, space_points) 完整光纤真实标签
    """
    windows_per_sample = test_dataset.windows_per_sample
    num_files = len(test_dataset.file_paths)

    if file_index >= num_files:
        raise ValueError(f"文件索引{file_index}超出范围，总文件数{num_files}")

    print(f"提取第{file_index}个文件的完整光纤结果...")

    # 当前这个文件下，得到包含的滑窗样本在全局的开始索引file_start_idx，而file_end_idx表示下一个文件的第一个滑窗样本在全局的索引   从0开始
    file_start_idx = file_index * windows_per_sample
    file_end_idx = file_start_idx + windows_per_sample

    # 取出这个文件所对应的预测滑窗样本   取左不取右
    file_predictions = predictions[file_start_idx:file_end_idx]  # (windows_per_sample, 4, space_points)
    file_targets = targets[file_start_idx:file_end_idx]

    # 检查是否有重复段需要融合
    window_size = test_dataset.window_size
    num_segments = len(test_dataset.valid_segments)
    stride = window_size - 1

    print(f"文件{file_index}的窗口范围: {file_start_idx}-{file_end_idx-1}")
    print(f"窗口数: {windows_per_sample}")
    print(f"注意：每个文件有{num_segments}段，但最后一段无法预测，所以最终只有{num_segments-1}段结果")

    # 计算标准窗口数
    standard_windows = 0
    pos = 0
    while pos + window_size <= num_segments:
        standard_windows += 1
        pos += stride

    has_extra_window = False
    if standard_windows > 0:
        last_segment_idx = num_segments - 1  # 最后一段的索引
        last_window_start = (standard_windows - 1) * stride  # 最后一个标准窗口的起始位置索引  从0开始
        last_window_end = last_window_start + window_size - 1  # 最后一个标准窗口的结束位置索引   从0开始

        if last_segment_idx > last_window_end:
            has_extra_window = True

    if not has_extra_window:
        # 无重复段，按窗口顺序拼接预测结果
        print("无重复段，直接返回原始结果")
        # 注意：这里应该按照窗口的空间顺序拼接，而不是简单reshape
        # 每个窗口预测4段，2个窗口总共预测8段，但这不等于完整的8段光纤
        # 实际上应该是：窗口0预测段0-3，窗口1预测段4-7
        segments_list = []
        targets_list = []

        for w in range(windows_per_sample):
            segments_list.append(file_predictions[w])  # (4, space_points)
            targets_list.append(file_targets[w])

        fiber_prediction = np.concatenate(segments_list, axis=0)  # (total_segments, space_points)
        fiber_target = np.concatenate(targets_list, axis=0)

        print(f"调试信息 - segments_list长度: {len(segments_list)}")
        print(f"调试信息 - 第一个segment形状: {segments_list[0].shape}")
        print(f"调试信息 - fiber_prediction形状: {fiber_prediction.shape}")
        print(f"调试信息 - fiber_target形状: {fiber_target.shape}")
    else:
        # 有重复段，需要融合
        print("检测到重复段，进行融合...")

        # 计算重复段    取左不取右
        extra_window_start = num_segments - window_size  # 扩展窗口的起始位置索引   从0开始
        last_standard_predicted = list(range(last_window_start, last_window_start + window_size - 1))  # 最后一个标准窗口预测的段
        extra_window_predicted = list(range(extra_window_start, extra_window_start + window_size - 1))  # 向前扩展窗口预测的段
        overlap_segments = list(set(last_standard_predicted) & set(extra_window_predicted))

        print(f"最后标准窗口预测段: {last_standard_predicted}")
        print(f"向前扩展窗口预测段: {extra_window_predicted}")
        print(f"重复段: {overlap_segments}")

        # 拼接非重复部分
        segments_list = []
        targets_list = []

        # 添加前面的标准窗口（除了最后一个标准窗口）
        for w in range(standard_windows - 1):
            segments_list.append(file_predictions[w])  # (4, space_points)
            targets_list.append(file_targets[w])

        # 处理最后一个标准窗口和向前扩展窗口
        # 得到最后一个标准窗口和向前扩展窗口的预测结果
        last_standard_window = file_predictions[standard_windows - 1]  # (4, space_points)
        extra_window = file_predictions[standard_windows]  # (4, space_points)

        # 得到最后一个标准窗口和向前扩展窗口的标签结果
        last_standard_target = file_targets[standard_windows - 1]
        extra_target = file_targets[standard_windows]

        # 修复：按物理索引排序融合段，避免重复
        concat_map_pred = {}  # key: segment_idx, val: segment_bfs
        concat_map_target = {}

        # 添加最后一个标准窗口的预测段
        for i, seg_idx in enumerate(last_standard_predicted):
            concat_map_pred[seg_idx] = last_standard_window[i]
            concat_map_target[seg_idx] = last_standard_target[i]

        # 添加向前扩展窗口的非重复段
        for i, seg_idx in enumerate(extra_window_predicted):
            if seg_idx not in overlap_segments:
                # 非重复段，直接添加
                concat_map_pred[seg_idx] = extra_window[i]
                concat_map_target[seg_idx] = extra_target[i]

        # 按物理索引排序并拼接
        sorted_indices = sorted(concat_map_pred.keys())
        segments_list = [concat_map_pred[i] for i in sorted_indices]
        targets_list = [concat_map_target[i] for i in sorted_indices]

        # 拼接所有段
        fiber_prediction = np.concatenate([seg[np.newaxis, :] for seg in segments_list], axis=0)
        fiber_target = np.concatenate([seg[np.newaxis, :] for seg in targets_list], axis=0)

        print(f"融合完成: {file_predictions.shape} -> {fiber_prediction.shape}")

    return fiber_prediction, fiber_target

def load_bfs_stats(stats_file: str = "./BOTDA_Dataset/bfs_stats.mat") -> tuple:
    """
    加载BFS归一化统计信息

    参数：
    - stats_file: 统计文件路径

    返回：
    - bfs_min: BFS最小值
    - bfs_max: BFS最大值
    """
    try:
        import scipy.io as sio
        stats_data = sio.loadmat(stats_file)
        print(f"BFS统计文件键: {list(stats_data.keys())}")

        # 修复：正确的访问方式
        if 'stats' in stats_data:
            # 新的数据结构：stats包含一个元组(min_array, max_array)
            stats = stats_data['stats'][0][0]  # 获取第一个元素
            bfs_min = float(stats[0][0][0])  # 第一个数组是min
            bfs_max = float(stats[1][0][0])  # 第二个数组是max
        elif 'min' in stats_data and 'max' in stats_data:
            # 旧的数据结构
            bfs_min = float(stats_data['min'][0][0][0][0])
            bfs_max = float(stats_data['max'][0][0][0][0])
        else:
            raise ValueError("无法找到BFS统计信息")

        print(f"加载BFS统计信息: min={bfs_min:.2f} MHz, max={bfs_max:.2f} MHz")
        return bfs_min, bfs_max
    except Exception as e:
        print(f"警告：无法加载BFS统计文件 {stats_file}: {e}")
        print("使用默认值: min=-40 MHz, max=50 MHz")
        return -40.0, 50.0

def denormalize_bfs(bfs_normalized: np.ndarray, bfs_min: float, bfs_max: float) -> np.ndarray:
    """
    BFS反归一化

    参数：
    - bfs_normalized: 归一化的BFS值 [0,1]
    - bfs_min: BFS最小值
    - bfs_max: BFS最大值

    返回：
    - bfs_denormalized: 反归一化的BFS值 (MHz)

    公式：bfsN = bfs * (bfs_max - bfs_min) + bfs_min
    """
    return bfs_normalized * (bfs_max - bfs_min) + bfs_min

def plot_single_fiber_result(fiber_prediction: np.ndarray, fiber_target: np.ndarray,
                            save_dir: str, file_index: int):
    """
    绘制单个完整光纤的预测结果对比

    参数：
    - fiber_prediction: (total_segments, space_points) 完整光纤预测结果（归一化）
    - fiber_target: (total_segments, space_points) 完整光纤真实标签（归一化）
    - save_dir: 保存目录
    - file_index: 文件索引
    """
    print(f"绘制第{file_index}个文件的完整光纤结果...")

    # 加载BFS统计信息并反归一化
    bfs_min, bfs_max = load_bfs_stats()

    # 反归一化BFS值
    fiber_prediction_denorm = denormalize_bfs(fiber_prediction, bfs_min, bfs_max)
    fiber_target_denorm = denormalize_bfs(fiber_target, bfs_min, bfs_max)

    print(f"反归一化完成: [{bfs_min:.1f}, {bfs_max:.1f}] MHz")

    # 展平为一维数组
    pred_flat = fiber_prediction_denorm.flatten()
    target_flat = fiber_target_denorm.flatten()

    # 创建空间位置轴（假设每段20m，每段200个空间点）
    segment_length = 20  # 每段长度(m)
    space_points_per_segment = fiber_prediction.shape[1]
    total_segments = fiber_prediction.shape[0]

    # 生成空间位置
    space_positions = []
    for seg in range(total_segments):
        seg_start = seg * segment_length
        seg_positions = np.linspace(seg_start, seg_start + segment_length,
                                  space_points_per_segment, endpoint=False)
        space_positions.extend(seg_positions)

    space_positions = np.array(space_positions)

    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # 1. 完整光纤预测结果对比
    axes[0, 0].plot(space_positions, target_flat, 'b-', label='Truth', linewidth=1.5)
    axes[0, 0].plot(space_positions, pred_flat, 'r--', label='Prediction', linewidth=1.5)
    axes[0, 0].set_xlabel('Fiber Position (m)')
    axes[0, 0].set_ylabel('BFS (MHz)')
    axes[0, 0].set_title(f'Complete Fiber Prediction Comparison - File {file_index}')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 添加段分界线
    for seg in range(1, total_segments):
        seg_pos = seg * segment_length
        axes[0, 0].axvline(x=seg_pos, color='gray', linestyle=':', alpha=0.5)

    # 2. 预测误差分布
    error = pred_flat - target_flat
    axes[0, 1].plot(space_positions, error, 'g-', linewidth=1)
    axes[0, 1].set_xlabel('Fiber Position (m)')
    axes[0, 1].set_ylabel('Prediction Error (MHz)')
    axes[0, 1].set_title('Prediction Error Distribution')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].axhline(y=0, color='black', linestyle='-', alpha=0.5)

    # 3. 散点图对比
    axes[1, 0].scatter(target_flat, pred_flat, alpha=0.6, s=1)
    min_val = min(target_flat.min(), pred_flat.min())
    max_val = max(target_flat.max(), pred_flat.max())
    axes[1, 0].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
    axes[1, 0].set_xlabel('Truth (MHz)')
    axes[1, 0].set_ylabel('Prediction (MHz)')
    axes[1, 0].set_title('Prediction vs Truth')
    axes[1, 0].grid(True, alpha=0.3)

    # 4. 误差统计
    rmse = np.sqrt(np.mean(error**2))
    mae = np.mean(np.abs(error))
    r2 = 1 - np.sum(error**2) / np.sum((target_flat - np.mean(target_flat))**2)

    axes[1, 1].hist(error, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[1, 1].set_xlabel('Prediction Error (MHz)')
    axes[1, 1].set_ylabel('Frequency')
    axes[1, 1].set_title('Error Distribution Histogram')
    axes[1, 1].grid(True, alpha=0.3)

    # 添加统计信息
    stats_text = f'RMSE: {rmse:.4f}\nMAE: {mae:.4f}\nR²: {r2:.4f}'
    axes[1, 1].text(0.05, 0.95, stats_text, transform=axes[1, 1].transAxes,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    plt.tight_layout()

    # 保存图片
    save_path = os.path.join(save_dir, f'single_fiber_result_file_{file_index}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"完整光纤结果图已保存: {save_path}")

    # plt.show()  # 注释掉，避免弹出图形窗口
    plt.close()

def calculate_fiber_positions(sample_idx: int, windows_per_sample: int,
                            window_size: int = 5, segment_length: float = 20.0,
                            space_resolution: float = 0.1) -> np.ndarray:
    """
    计算样本在完整光纤中的实际位置

    参数：
    - sample_idx: 样本索引
    - windows_per_sample: 每个完整文件的窗口数
    - window_size: 窗口大小（段数）
    - segment_length: 每段长度（米）
    - space_resolution: 空间分辨率（米）

    返回：
    - 每个预测段的光纤位置数组 (4, 200)
    """
    # 计算在该文件中的窗口索引
    window_idx = sample_idx % windows_per_sample

    # 计算窗口的起始段索引（基于滑窗策略）
    stride = window_size - 1  # 步长=4
    window_start_segment = window_idx * stride

    # 每段的空间点数
    space_points_per_segment = int(segment_length / space_resolution)  # 20m / 0.1m = 200

    # 计算每个预测段的光纤位置
    positions = []
    for seg in range(4):  # 预测4段
        segment_idx = window_start_segment + seg
        start_pos = segment_idx * segment_length  # 段的起始位置（米）
        # 生成该段内的位置数组
        segment_positions = np.linspace(start_pos, start_pos + segment_length,
                                      space_points_per_segment, endpoint=False)
        positions.append(segment_positions)

    return np.array(positions)


def plot_results(predictions: np.ndarray,
                targets: np.ndarray,
                save_dir: str,
                num_samples: int = 5,
                windows_per_sample: int = 3):
    """
    绘制预测结果

    参数：
    - predictions: 预测结果 (N, segments, length) - 归一化
    - targets: 真实标签 (N, segments, length) - 归一化
    - save_dir: 保存目录
    - num_samples: 显示的样本数量
    - windows_per_sample: 每个完整文件的窗口数
    """
    os.makedirs(save_dir, exist_ok=True)

    # 加载BFS统计信息
    bfs_min, bfs_max = load_bfs_stats()

    # 调试信息：打印数组维度
    print(f"调试信息 - predictions形状: {predictions.shape}")
    print(f"调试信息 - targets形状: {targets.shape}")

    # 随机选择几个样本进行可视化
    num_samples = min(num_samples, len(predictions))  # predictions 是一个三维Numpy数组，其维度（shape）为 (N, 4, space_points)，N: 代表样本的数量（Number of samples），当对一个多维的Numpy数组使用 len() 函数时，Python会返回其第一个维度的大小
    sample_indices = np.random.choice(len(predictions), num_samples, replace=False)  # 从所有样本的索引（0 到 len(predictions)-1）中，随机、不重复地（replace=False）抽取 num_samples 个索引
    
    for i, idx in enumerate(sample_indices):  # 遍历之前抽取出来的几个索引
        # predictions维度：(N, 4, space_points)，pred维度：(4, space_points)，4表示一个窗口中预测的段数
        pred = predictions[idx]  # (4, space_points) - 归一化
        target_raw = targets[idx]    # (total_space_points,) - 归一化

        # 调试信息
        print(f"样本 {idx}: pred形状={pred.shape}, target_raw形状={target_raw.shape}")

        # 检查pred的维度并相应处理
        if len(pred.shape) == 1:  # pred是1维的 (800,)
            # 将pred重新整形为段的形式
            num_segments = 4  # 假设有4段
            space_points_per_segment = pred.shape[0] // num_segments  # 800 // 4 = 200
            pred = pred.reshape(num_segments, space_points_per_segment)  # (4, 200)
            target = target_raw.reshape(num_segments, space_points_per_segment)  # (4, 200)
        else:  # pred是2维的 (4, 200)
            num_segments = pred.shape[0]
            space_points_per_segment = pred.shape[1]  # 直接从pred获取每段的空间点数
            # target_raw应该和pred有相同的形状
            if target_raw.shape == pred.shape:
                target = target_raw  # 已经是正确的形状
            else:
                target = target_raw.reshape(num_segments, space_points_per_segment)

        print(f"重新整形后: pred形状={pred.shape}, target形状={target.shape}")

        # 反归一化BFS值
        pred_denorm = denormalize_bfs(pred, bfs_min, bfs_max)
        target_denorm = denormalize_bfs(target, bfs_min, bfs_max)

        num_segments = pred.shape[0]  # 获取pred的第一个维度，即4，一个样本或一个滑窗中预测了几段，这里是4段

        # 计算该样本的光纤位置
        fiber_positions = calculate_fiber_positions(idx, windows_per_sample)

        # 创建子图
        fig, axes = plt.subplots(num_segments, 1, figsize=(12, 3*num_segments))  # 创建一个包含 num_segments 行、1 列的子图网格。每个段（segment）都将有自己独立的图
        if num_segments == 1:
            axes = [axes]  # 当只画一个子图时，plt.subplots 返回的 axes 不是一个列表，而是一个单独的对象

        for seg in range(num_segments):
            ax = axes[seg]

            # 获取该段的光纤位置
            segment_positions = fiber_positions[seg]

            # 绘制预测和真实值（使用反归一化的值）
            ax.plot(segment_positions, target_denorm[seg], 'b-', label='Truth BFS', linewidth=2)
            ax.plot(segment_positions, pred_denorm[seg], 'r--', label='Predicted BFS', linewidth=2)

            # 计算该段的误差（使用反归一化的值）
            segment_mae = np.mean(np.abs(pred_denorm[seg] - target_denorm[seg]))  # 平均绝对误差
            segment_rmse = np.sqrt(np.mean((pred_denorm[seg] - target_denorm[seg])**2))  #　均方根误差 (Root Mean Squared Error)

            # 计算该段的起始和结束位置
            start_pos = segment_positions[0]
            end_pos = segment_positions[-1] + (segment_positions[1] - segment_positions[0])  # 加上一个间隔

            ax.set_title(f'Segment {seg+1} ({start_pos:.1f}-{end_pos:.1f}m) - MAE: {segment_mae:.2f} MHz, RMSE: {segment_rmse:.2f} MHz')
            ax.set_xlabel('Fiber Position (m)')
            ax.set_ylabel('BFS Shift (MHz)')
            ax.set_ylim(bfs_min, bfs_max)  # 统一y轴范围为bfs_min和bfs_max
            ax.legend()
            ax.grid(True, alpha=0.3)

        # 自动调整子图参数，填充整个图像区域，避免标签重叠
        plt.tight_layout()
        # 保存为PNG文件
        plt.savefig(os.path.join(save_dir, f'sample_{idx}_prediction.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    # 绘制整体统计图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 预测 vs 真实值散点图
    # 反归一化所有预测结果
    predictions_denorm = denormalize_bfs(predictions, bfs_min, bfs_max)
    targets_denorm = denormalize_bfs(targets, bfs_min, bfs_max)

    # 三维数组 (N, segments, length)展平为一个一维列表
    pred_flat = predictions_denorm.flatten()
    target_flat = targets_denorm.flatten()

    # 随机采样点以避免图像过于密集
    sample_size = min(10000, len(pred_flat))
    sample_idx = np.random.choice(len(pred_flat), sample_size, replace=False)  # 从所有点的索引中，随机、不重复地抽取 sample_size 个索引出来

    # axes[0, 0].scatter(...): 在左上角的子图上绘制散点图
    axes[0, 0].scatter(target_flat[sample_idx], pred_flat[sample_idx], alpha=0.5, s=1)
    axes[0, 0].plot([target_flat.min(), target_flat.max()],
                    [target_flat.min(), target_flat.max()], 'r--', linewidth=2)
    axes[0, 0].set_xlabel('Truth BFS (MHz)')
    axes[0, 0].set_ylabel('Predicted BFS (MHz)')
    axes[0, 0].set_title('Prediction vs Truth')
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 误差分布直方图
    # 计算每个点的预测误差（预测值 - 真实值）
    errors = pred_flat - target_flat
    axes[0, 1].hist(errors, bins=50, alpha=0.7, edgecolor='black')
    axes[0, 1].axvline(0, color='red', linestyle='--', linewidth=2)
    axes[0, 1].set_xlabel('Prediction Error')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].set_title(f'Error Distribution (Mean: {np.mean(errors):.4f})')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 按段统计的性能
    # 检查模型在不同“段”（segment）上的表现是否一致
    segment_maes = []
    segment_rmses = []

    # 检查targets的维度并相应处理 - 修复：使用反归一化后的值计算段级指标
    if len(targets.shape) == 3:  # 3维数组 (N, segments, space_points)
        for seg in range(predictions.shape[1]):  # 循环遍历每一个段，predictions.shape[1]就是预测段的数量，即4段
            seg_pred = predictions[:, seg, :].flatten()
            seg_target = targets[:, seg, :].flatten()
            # 反归一化后计算指标
            seg_pred_denorm = denormalize_bfs(seg_pred, bfs_min, bfs_max)
            seg_target_denorm = denormalize_bfs(seg_target, bfs_min, bfs_max)
            segment_maes.append(mean_absolute_error(seg_target_denorm, seg_pred_denorm))
            segment_rmses.append(np.sqrt(mean_squared_error(seg_target_denorm, seg_pred_denorm)))
    elif len(targets.shape) == 2:  # 2维数组 (N, total_space_points)
        # 假设每段有相同的空间点数
        total_space_points = targets.shape[1]
        num_segments = predictions.shape[1]
        space_points_per_segment = total_space_points // num_segments

        for seg in range(num_segments):
            start_idx = seg * space_points_per_segment
            end_idx = (seg + 1) * space_points_per_segment

            seg_pred = predictions[:, start_idx:end_idx].flatten()  # 修复：predictions现在是2维的
            seg_target = targets[:, start_idx:end_idx].flatten()
            # 反归一化后计算指标
            seg_pred_denorm = denormalize_bfs(seg_pred, bfs_min, bfs_max)
            seg_target_denorm = denormalize_bfs(seg_target, bfs_min, bfs_max)
            segment_maes.append(mean_absolute_error(seg_target_denorm, seg_pred_denorm))
            segment_rmses.append(np.sqrt(mean_squared_error(seg_target_denorm, seg_pred_denorm)))
    else:
        print(f"❌ 不支持的targets维度: {targets.shape}")
        return
    
    x_pos = np.arange(len(segment_maes))
    # segment_maes是一个列表，里面存放了每个“段”的平均绝对误差（MAE）。len(segment_maes)就是这个列表的长度，也就是总共有多少个段，在这里是4段。
    # np.arange(...):是Numpy库的一个函数，用来创建一个等差数组，np.arange(4) 会生成一个数组 [0, 1, 2, 3]，这个数组将作为每个“段”类别在X轴上的中心位置
    width = 0.35  # 柱状图单根柱子的宽度
    
    axes[1, 0].bar(x_pos - width/2, segment_maes, width, label='MAE', alpha=0.7)
    axes[1, 0].bar(x_pos + width/2, segment_rmses, width, label='RMSE', alpha=0.7)
    axes[1, 0].set_xlabel('Segment Number')
    axes[1, 0].set_ylabel('Error')
    axes[1, 0].set_title('Performance Comparison by Segment')
    axes[1, 0].set_xticks(x_pos)
    axes[1, 0].set_xticklabels([f'Seg{i+1}' for i in range(len(segment_maes))])  # 设置X轴的刻度位置和标签，使其显示为“段1”、“段2”等，而不是默认的0, 1, 2
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. Cumulative Error Distribution
    abs_errors = np.abs(errors)
    sorted_errors = np.sort(abs_errors)  # 将绝对误差从小到大排序
    cumulative = np.arange(1, len(sorted_errors) + 1) / len(sorted_errors)  # 计算累积概率。np.arange(1, N+1)/N会生成一个从1/N到1的序列，对应每个排好序的误差的累积概率
    
    axes[1, 1].plot(sorted_errors, cumulative, linewidth=2)
    axes[1, 1].set_xlabel('Absolute Error')
    axes[1, 1].set_ylabel('Cumulative Probability')
    axes[1, 1].set_title('Cumulative Error Distribution')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 添加百分位数标记
    percentiles = [50, 90, 95, 99]
    for p in percentiles:
        error_p = np.percentile(abs_errors, p)
        axes[1, 1].axvline(error_p, color='red', linestyle='--', alpha=0.7)
        axes[1, 1].text(error_p, p/100, f'{p}%', rotation=90, va='bottom')

        # 特别标记99%对应的x值在横轴上
        if p == 99:
            axes[1, 1].annotate(f'{error_p:.3f}MHz',
                               xy=(error_p, 0),
                               xytext=(error_p, -0.05),
                               ha='center', va='top',
                               fontsize=10, fontweight='bold',
                               arrowprops=dict(arrowstyle='->', color='red', lw=1.5))

    # 自动调整子图布局，防止标题和标签重叠
    plt.tight_layout()
    # 保存图片
    plt.savefig(os.path.join(save_dir, 'overall_performance.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"结果图已保存到: {save_dir}")


def main():
    """主测试函数"""
    # ==================== 配置参数 ====================
    # 数据配置
    DATA_DIR = "./SNR25_35_Dataset"        # 数据目录
    MODEL_PATH = "./checkpoints/best_model.pth"  # 模型路径
    RESULTS_DIR = "./test_results"      # 结果保存目录
    
    # 模型配置（需要与训练时一致）
    WINDOW_SIZE = 5
    BASE_CHANNELS = 32
    
    # 测试配置
    BATCH_SIZE = 32
    NUM_WORKERS = 8
    MAX_SAMPLES = None  # 测试样本数限制
    
    # 创建结果目录
    os.makedirs(RESULTS_DIR, exist_ok=True)
    
    # ==================== 设备配置 ====================
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # ==================== 加载模型 ====================
    print("加载模型...")
    # 需要从实际数据中获取维度参数
    print("从测试数据中获取模型维度参数...")
    temp_dataset = BOTDATestDataset(DATA_DIR, WINDOW_SIZE, 1)  # 只加载一个样本获取维度
    if len(temp_dataset) > 0:
        sample_stokes, sample_bfs = temp_dataset[0]
        time_points_per_segment = sample_stokes.shape[1] // WINDOW_SIZE
        # 修复：BFS标签现在是(4,200)形状，每段空间点数是第二个维度
        space_points_per_segment = sample_bfs.shape[1]
        print(f"从数据中获取的维度: 时间点/段={time_points_per_segment}, 空间点/段={space_points_per_segment}")
    else:
        print("警告：无法从数据中获取维度，使用默认值")
        time_points_per_segment = 400
        space_points_per_segment = 200

    model = load_model(
        MODEL_PATH, WINDOW_SIZE, BASE_CHANNELS,
        time_points_per_segment, space_points_per_segment, device
    )
    
    # ==================== 加载测试数据 ====================
    print("加载测试数据...")
    test_dataset = BOTDATestDataset(DATA_DIR, WINDOW_SIZE, MAX_SAMPLES)

    # 验证第一个测试文件是否有向前扩展窗口
    print("\n验证窗口生成策略...")
    if len(test_dataset.file_paths) > 0:  # 确保测试数据目录里至少有一个文件
        first_file = test_dataset.file_paths[0]  # 取出第一个文件
        print(f"检查第一个测试文件: {first_file}")

        # 模拟训练代码中的窗口计算逻辑
        num_segments = len(test_dataset.valid_segments)   # 9段
        stride = WINDOW_SIZE - 1

        # 计算标准窗口数量
        standard_windows = 0
        pos = 0
        window_positions = []  # 记录每个窗口（包含标准窗口和扩展窗口）起始位置索引
        while pos + WINDOW_SIZE <= num_segments:
            window_positions.append(pos)
            standard_windows += 1
            pos += stride

        # 检查是否需要向前扩展
        last_segment_idx = num_segments - 1
        has_extra_window = False
        if standard_windows > 0:
            last_window_start = window_positions[-1]
            last_window_end = last_window_start + WINDOW_SIZE - 1

            if last_segment_idx > last_window_end:  # 有扩展窗口
                extra_window_start = num_segments - WINDOW_SIZE  # 扩展窗口起始位置索引
                window_positions.append(extra_window_start)
                has_extra_window = True

        print(f"总段数: {num_segments}")
        print(f"标准窗口数: {standard_windows}")
        print(f"窗口位置: {window_positions}")
        print(f"是否有向前扩展窗口: {has_extra_window}")

        # 计算每个文件的窗口数
        windows_per_sample = standard_windows + (1 if has_extra_window else 0)
        print(f"每个文件的窗口数: {windows_per_sample}")

        if has_extra_window:
            # 分析重叠情况
            last_standard_start = window_positions[-2]  # 取倒数第二个窗口起始位置索引，即最后一个标准窗口起始位置索引
            extra_window_start = window_positions[-1]  # 扩展窗口起始位置索引

            # 取左不取右，生成列表
            last_standard_predicted = list(range(last_standard_start, last_standard_start + WINDOW_SIZE - 1))
            extra_window_predicted = list(range(extra_window_start, extra_window_start + WINDOW_SIZE - 1))

            # 重叠的预测段，而不是窗口内的所有重叠段都考虑，因为一个窗口内只预测前面四段，最后一段不预测
            overlap_segments = list(set(last_standard_predicted) & set(extra_window_predicted))

            print(f"最后标准窗口预测段: {last_standard_predicted}")
            print(f"向前扩展窗口预测段: {extra_window_predicted}")
            print(f"重叠段: {overlap_segments}")
        else:
            print("无重叠段")
    else:
        # 如果没有测试文件，使用默认值
        windows_per_sample = 3
    print()
    test_loader = DataLoader(
        test_dataset,
        batch_size=BATCH_SIZE,
        shuffle=False,
        num_workers=NUM_WORKERS,
        pin_memory=True
    )
    
    print(f"测试样本数: {len(test_dataset)}")
    
    # ==================== 模型评估 ====================
    print("评估模型性能...")
    metrics, predictions, targets = evaluate_model(model, test_loader, test_dataset, device)
    
    # ==================== 打印结果 ====================
    print("\n" + "="*50)
    print("测试结果:")
    print("="*50)
    for key, value in metrics.items():
        if isinstance(value, float):
            print(f"{key:15s}: {value:.6f}")
        else:
            print(f"{key:15s}: {value}")
    
    # ==================== 保存结果 ====================
    # 保存评估指标
    metrics_path = os.path.join(RESULTS_DIR, "test_metrics.json")
    with open(metrics_path, 'w') as f:
        json.dump(metrics, f, indent=2)
    
    # 保存预测结果
    results_path = os.path.join(RESULTS_DIR, "test_results.npz")
    np.savez_compressed(results_path, 
                       predictions=predictions, 
                       targets=targets,
                       metrics=metrics)
    
    # ==================== 单个文件完整光纤结果展示 ====================
    num_files = len(test_dataset.file_paths)
    print(f"\n检测到{num_files}个测试文件")

    # 自动选择第一个文件进行展示（避免交互式输入问题）
    if num_files > 0:
        file_index = 0  # 自动选择第一个文件
        print(f"自动选择展示第{file_index}个文件的完整光纤结果")

        try:
            # 提取并融合单个文件的结果
            fiber_pred, fiber_target = extract_single_fiber_result(
                predictions, targets, test_dataset, file_index
            )

            print(f"完整光纤结果维度: {fiber_pred.shape}")
            if len(fiber_pred.shape) == 2:
                print(f"预测段数: {fiber_pred.shape[0]}")
                print(f"每段空间点数: {fiber_pred.shape[1]}")
            else:
                print(f"❌ 维度错误：期望2D数组，实际得到{len(fiber_pred.shape)}D数组")
                print(f"需要重新整形数据...")

            # 绘制完整光纤结果
            plot_single_fiber_result(fiber_pred, fiber_target, RESULTS_DIR, file_index)
            print(f"✅ 单个文件结果图已保存")

        except Exception as e:
            print(f"❌ 生成单个文件结果图时出错: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ 没有找到测试文件")

    # ==================== 生成可视化 ====================
    print("生成可视化结果...")
    plot_results(predictions, targets, RESULTS_DIR, num_samples=5, windows_per_sample=windows_per_sample)  # 取5个样本数量显示
    
    print(f"\n测试完成！结果已保存到: {RESULTS_DIR}")
    print(f"主要性能指标:")
    print(f"  RMSE: {metrics['rmse']:.6f}")
    print(f"  MAE:  {metrics['mae']:.6f}")
    print(f"  R²:   {metrics['r2_score']:.6f}")


if __name__ == "__main__":
    main()
