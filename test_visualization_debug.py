#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可视化函数调试版本
"""

import sys
import os
sys.path.append('.')

from botda_train import BOTDADataset, visualize_first_training_sample

def test_visualization():
    """测试可视化函数"""
    
    print("测试可视化函数...")
    
    # 配置参数
    DATA_DIR = "./BOTDA_Dataset"
    WINDOW_SIZE = 5
    
    try:
        # 创建训练数据集
        print("创建训练数据集...")
        train_dataset = BOTDADataset(DATA_DIR, 'train', WINDOW_SIZE, stride=None, max_samples=1)
        
        print(f"数据集文件数量: {len(train_dataset.file_paths)}")
        print(f"数据集样本数量: {len(train_dataset)}")
        
        if len(train_dataset.file_paths) > 0:
            print(f"第一个文件路径: {train_dataset.file_paths[0]}")
            
            # 检查文件是否存在
            if os.path.exists(train_dataset.file_paths[0]):
                print("✅ 文件存在，开始可视化...")
                # 调用可视化函数
                visualize_first_training_sample(train_dataset)
            else:
                print("❌ 文件不存在")
            
        else:
            print("❌ 没有找到训练数据文件")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_visualization()
