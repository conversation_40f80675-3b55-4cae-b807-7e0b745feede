# ---------------------------------------------------------------
#  HybridUNetTCN: 混合U-Net与TCN的BOTDA信号处理网络
#
#  设计目标:
#  - 输入: (B, 3, W*T) 拼接后的多段斯托克斯时间序列   W是窗长  T是一段斯托克斯的时间点数
#  - 输出: (B, 1, (W-1)*P) 预测的BFS空间序列    P是一段的空间点数
#
#  架构:
#  1. U-Net骨架: 处理输入输出分辨率不匹配问题
#  2. TCN模块: 作为U-Net的基本构建块，高效提取多尺度特征
#  3. 注意力瓶颈: 在网络最深处建立全局关联
#  4. 精确的输出层: 确保输出维度符合物理空间栅格数
#
#  新的滑窗策略:
#  - 窗口大小: 5段
#  - 输入: 第1,2,3,4,5段的斯托克斯信号拼接
#  - 输出: 只预测前4段的BFS分布（第1,2,3,4段）
#  - 步长: window_size-1=4（非重叠滑窗）
# ---------------------------------------------------------------

import torch
import torch.nn as nn
import torch.nn.functional as F

# -------- 基础构建块 --------

class ResDilatedBlock(nn.Module):
    """
    一维残差空洞卷积块 (TCN核心)
    保持输入输出长度不变。
    """
    def __init__(self, channels: int, dilation: int = 1, dropout_rate: float = 0):
        super().__init__()
        self.net = nn.Sequential(
            # (1) 空洞卷积: 增大感受野，不增加计算量   dilation=dilation: 空洞卷积，同样大小的卷积核（3x3）感受野更大   padding=dilation: 确保经过这层卷积后，序列的长度保持不变
            nn.Conv1d(in_channels=channels, out_channels=channels, kernel_size=3, padding=dilation, dilation=dilation),
            # (2) 归一化: 稳定训练
            nn.BatchNorm1d(channels),
            # (3) 激活函数: 引入非线性
            nn.GELU(),
            # (4) Dropout: 防止过拟合
            nn.Dropout(dropout_rate),
            # (5) 1x1卷积: 融合通道信息，增加模型容量
            nn.Conv1d(in_channels=channels, out_channels=channels, kernel_size=1),
            # (6) 归一化
            nn.BatchNorm1d(channels),
            # (7) Dropout: 防止过拟合
            nn.Dropout(dropout_rate)
        )
        self.activation = nn.GELU()

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.activation(x + self.net(x))  # 形状相同才可以相加
    # ResDilatedBlock 的设计目的，不是为了压缩或扩展特征的数量（通道数），而是为了在保持通道数不变的前提下，对特征进行提炼和深化，让网络学习到更复杂、更抽象的模式

class SelfAttentionBlock(nn.Module):
    """
    自注意力模块
    用于网络瓶颈处，建立全局依赖。
    """
    def __init__(self, channels: int, num_heads: int = 8):
        super().__init__()
        # (1) 多头注意力层
        self.mha = nn.MultiheadAttention(embed_dim=channels, num_heads=num_heads, batch_first=True)
        # nn.MultiheadAttention:会计算序列中每个点对其他所有点的“重要性”，然后根据这个重要性加权求和，从而让每个点都包含了全局的信息
        # batch_first = True: 告诉注意力层，输入的第一个维度是批次大小（Batch size）
        # num_heads:多头注意力机制中的“头”的数量，每个头会从不同的角度（子空间）去分析信号，关注不同的重点，最后，综合起来，得到一个更全面、更鲁棒的结论
        # 注意embed_dim除以num_heads的结果要是个整数
        # (2) 层归一化    与 BatchNorm 不同，LayerNorm 是在通道维度上进行归一化
        self.norm = nn.LayerNorm(channels)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        # x: (B, C, L) -> (B, L, C) for MHA
        x_t = x.permute(0, 2, 1)  # x.permute(...) 是一个维度重排操作   (0, 2, 1): “保持第0个维度不变，把原来的第2个维度（长度L）放到新位置1，把原来的第1个维度（通道C）放到新位置2”
        # LayerNorm is applied on the last dimension (features)
        x_norm = self.norm(x_t)
        attn_output, _ = self.mha(x_norm, x_norm, x_norm)  # 注意力机制需要三个输入：查询（Query）、键（Key）、值（Value），即Q,K,V    在自注意力中，这三个输入都来自同一个源，也就是处理好的 x_norm
        # Add & Norm (residual connection) and permute back
        output = (x_t + attn_output).permute(0, 2, 1)  # 残差连接，同时将格式转换回来，以便后续的卷积层可以继续处理
        return output

# -------- 主网络: HybridUNetTCN --------

class HybridUNetTCN(nn.Module):
    def __init__(self,
                 # 不传入参数的时候才使用这些值
                 # 输入参数
                 input_freq_channels: int = 3,
                 window_segments: int = 5,
                 time_points_per_segment: int = 400,
                 # 输出参数
                 output_segments: int = 4,
                 space_points_per_segment: int = 200,
                 # 网络架构参数
                 base_channels: int = 64,  # 网络内部通道数
                 # 3个通道太少: 只能表示原始的3个频率信息；64个通道: 可以学习64种不同的特征组合；64是经验值: 在很多信号处理任务中表现良好
                 depth: int = 4,  # U-Net结构的深度，即要进行多少次下采样和上采样  depth=4意味着要压缩4次，再还原4次
                 bottleneck_attn_heads: int = 8,  # 在网络最深处的瓶颈层，SelfAttentionBlock要用几个“头”
                 dropout_rate: float = 0):  # Dropout比率，防止过拟合
        super().__init__()

        # 保存维度信息
        self.input_length = window_segments * time_points_per_segment  # 输入的时间点数，大约是5*200ns/0.5ns=2000
        self.output_length = output_segments * space_points_per_segment  # 输入的空间点数，大约是4*20m/0.1m=800

        # 保存所有维度参数（forward函数需要）
        self.window_segments = window_segments
        self.time_points_per_segment = time_points_per_segment
        self.output_segments = output_segments
        self.space_points_per_segment = space_points_per_segment

        print(f"\n=== 模型初始化参数 ===")
        print(f"输入维度: ({input_freq_channels}, {self.input_length})")
        print(f"输出维度: ({output_segments}, {space_points_per_segment})")
        print(f"窗口段数: {window_segments}, 输出段数: {output_segments}")
        print(f"每段时间点: {time_points_per_segment}, 每段空间点: {space_points_per_segment}")

        # 1. Stem: 初始特征提取层
        self.stem = nn.Sequential(
            nn.Conv1d(in_channels=input_freq_channels, out_channels=base_channels, kernel_size=7, padding=3),  # 将原始的物理意义明确的3通道输入转换为更抽象的64通道，
            # 使用一个较大的卷积核7*7来捕捉初始的局部模式，padding=3 保证信号长度不变
            nn.BatchNorm1d(base_channels),
            nn.GELU()
        )

        # 2. Encoder: 编码器，逐步下采样，类似于池化，虽然细节模糊了，但是可以增大感受野
        self.encoder_blocks = nn.ModuleList()  # 创建一个特殊的“零件盒”，用来存放所有的编码器模块
        ch = base_channels  # ch 是一个追踪器，用来记录当前循环中数据的通道数。初始值为64
        for i in range(depth):  # 循环4次，构建4个编码器模块。
            block = nn.Sequential(
                ResDilatedBlock(ch, dilation=2**i, dropout_rate=dropout_rate),  # 在每个阶段，先用之前定义的一维残差空洞卷积块，对特征进行提炼和深化
                nn.Conv1d(in_channels=ch, out_channels=ch * 2, kernel_size=2, stride=2) # 下采样
                # stride=2: 使得信号的长度减半
                # ch, ch * 2: 使得信号的通道数翻倍
                # 效果: 每一轮循环，信号长度减半，特征数量翻倍（因为下面有ch *= 2）。信息被不断地“压缩”和“提炼”
            )
            self.encoder_blocks.append(block)  # 把刚创建好的模块装进零件盒
            ch *= 2  # 更新追踪器，为下一次循环做准备

        # 3. Bottleneck: 瓶颈层，特征最抽象，序列最短
        self.bottleneck = nn.Sequential(
            # ResDilatedBlock(ch, ...): 再进行一次特征深化
            ResDilatedBlock(ch, dilation=2**depth, dropout_rate=dropout_rate),  # ch 的值: 经过4轮编码器后，ch 的值是 64 * 2 * 2 * 2 * 2 = 1024
            SelfAttentionBlock(ch, num_heads=bottleneck_attn_heads)  # 在信息最浓缩、序列最短的时候，调用我们之前定义的多头注意力模块，来建立全局依赖关系
        )

        # 4. Decoder: 解码器，逐步上采样，恢复细节，精确定位
        self.decoder_blocks = nn.ModuleList()
        for i in reversed(range(depth)):  # 倒序循环（从3到0），与编码器路径对称
            block = nn.Sequential(
                # 使用转置卷积进行上采样，也叫“转置卷积”或“反卷积”，是Conv1d的逆操作
                nn.ConvTranspose1d(in_channels=ch, out_channels=ch // 2, kernel_size=2, stride=2),
                # stride=2: 使得信号的长度翻倍
                # ch, ch // 2: 使得信号的通道数减半
                # 效果: 每一轮循环，信号长度加倍，特征数量减半。逐步地将浓缩的信息“解压”和“还原”
                ResDilatedBlock(ch // 2, dilation=2**i, dropout_rate=dropout_rate)
            )
            self.decoder_blocks.append(block)
            ch //= 2  # 更新追踪器，为下一次循环做准备

        # 5. Head层 - 完全卷积设计，保持空间局部性
        # 计算下采样比例
        self.time_points_per_segment = time_points_per_segment
        self.space_points_per_segment = space_points_per_segment
        r = self.time_points_per_segment // self.space_points_per_segment  # 400//200 = 2
        assert r * self.space_points_per_segment == self.time_points_per_segment, \
               f"time_points_per_segment ({self.time_points_per_segment}) 必须能被 space_points_per_segment ({self.space_points_per_segment}) 整除"

        # 5. Head层 - 正确的时空变换设计
        self.head = nn.Sequential(
            nn.Conv1d(base_channels, base_channels // 2, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm1d(base_channels // 2),
            nn.GELU(),
            nn.Dropout(dropout_rate),  # 添加Dropout防止过拟合
            # 最终输出的通道数就是我们要预测的段数
            nn.Conv1d(base_channels // 2, output_segments, kernel_size=1)
        )

        print(f"Head层结构 (正确的时空变换):")
        print(f"  第一层卷积: {base_channels} -> {base_channels // 2}, kernel=3, padding=1")
        print(f"  BatchNorm + GELU")
        print(f"  第二层卷积: {base_channels // 2} -> {output_segments}, kernel=1")
        print(f"  每个通道对应一个预测段")
        print(f"  后续通过插值实现时空变换: 2000 -> {space_points_per_segment}")
        print(f"  最终输出形状: (batch, {output_segments}, {space_points_per_segment})")
        print(f"  保持空间局部性: ✓")

        # 6. 预先创建所有可能的通道调整层 - 避免动态创建导致TracerWarning
        self.channel_adapters = nn.ModuleDict()

        # 预先创建U-Net中可能需要的通道调整层
        # 动态计算所有可能的通道数组合
        possible_adapters = []

        # 计算编码器通道数序列（编码器每层的输出通道数）
        encoder_channels = []
        ch = base_channels
        for i in range(depth):
            ch *= 2  # 编码器每层将通道数翻倍
            encoder_channels.append(ch)  # 记录输出通道数

        # 计算解码器通道数序列
        decoder_channels = []
        ch = encoder_channels[-1]  # 瓶颈层通道数 (例如：3072)
        for i in range(depth):
            ch //= 2  # 每层减半
            decoder_channels.append(ch)  # [1536, 768, 384, 192, 96]

        # 创建跳跃连接的通道适配器
        # 跳跃连接：编码器第i层 -> 解码器第(depth-1-i)层
        for i in range(depth):
            from_ch = encoder_channels[depth-1-i]  # 编码器对应层（倒序）
            to_ch = decoder_channels[i]            # 解码器对应层
            possible_adapters.append((str(from_ch), str(to_ch)))

        # print(f"编码器通道数序列: {encoder_channels}")
        # print(f"解码器通道数序列: {decoder_channels}")
        # print(f"通道适配器: {possible_adapters}")

        for from_ch, to_ch in possible_adapters:
            adapter_key = f"{from_ch}_to_{to_ch}"
            self.channel_adapters[adapter_key] = nn.Conv1d(int(from_ch), int(to_ch), kernel_size=1)

        print("===================\n")

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        x: (B, C_f, W*T) - 输入的拼接斯托克斯信号
        """
        # 移除动态维度检查以避免TracerWarning
        # 在训练前应确保输入维度正确

        # 1. Stem
        x = self.stem(x)

        # 2. Encoder
        skips = []  # 用来存放“跳跃连接”所需的数据
        for block in self.encoder_blocks:  # 遍历在 __init__ 中创建的4个编码器模块
            x = block(x)  # 数据 x 依次通过每个编码器模块。每个模块都会让 x 的长度减半，通道数翻倍
            skips.append(x)  # 在每次下采样之后，将结果 x 存入 skips 中。这些是保留了丰富细节的“高清快照”，供后面解码器使用

        # 3. Bottleneck
        x = self.bottleneck(x)  # 将经过4次压缩后，信息最浓缩的 x 被送入瓶颈层

        # 4. Decoder
        for block, skip in zip(self.decoder_blocks, reversed(skips)):
            x = block(x)  # 数据 x 先通过解码器模块。每个模块会使 x 的长度翻倍，通道数减半
            # 跳跃连接 - 使用预先创建的通道调整层避免TracerWarning
            # 调整skip的长度以匹配x
            x_length = x.size(-1)
            skip = F.interpolate(skip, size=x_length, mode='linear', align_corners=False)

            # 使用预先创建的通道调整层处理通道数不匹配
            skip_channels = skip.size(1)
            x_channels = x.size(1)
            adapter_key = f"{skip_channels}_to_{x_channels}"

            # 如果需要通道调整且有对应的调整层，则使用它
            if adapter_key in self.channel_adapters:
                skip = self.channel_adapters[adapter_key](skip)

            x = x + skip  # 将经过上采样、携带了高级抽象信息（来自瓶颈层）的 x，与保留了低级细节信息（来自编码器快照）的 skip 相加。这使得模型既有全局观，又不失局部精度

        # 5. Head层 - 正确的对角提取，避免跨段污染
        x = self.head(x)  # (B, 64, 2000) -> (B, 4, 2000)

        B, C, L = x.shape
        seg_L = self.time_points_per_segment  # 400
        assert L == self.window_segments * seg_L, \
            f"Expected length {self.window_segments*seg_L}, got {L}"

        # 显式段维
        x = x.view(B, C, self.window_segments, seg_L)   # (B,4,5,400)

        # 截取将被预测的前 output_segments 个物理段
        x = x[:, :, :self.output_segments, :]           # (B,4,4,400)

        # --- 关键修复：对角提取，通道 i 对应段 i ---
        # 避免跨段混合，保持空间对应关系
        diag_list = []
        for i in range(self.output_segments):
            # x[:, channel=i, segment=i, :] -> (B, seg_L)
            diag_list.append(x[:, i, i, :])
        x = torch.stack(diag_list, dim=1)               # (B,4,400)

        # 段内下采样到空间分辨率
        x = F.interpolate(x, size=self.space_points_per_segment,
                          mode='linear', align_corners=False)  # (B,4,200)

        return x  # 保持正确的空间对应关系，避免毛刺

# -------- 测试代码 --------
if __name__ == '__main__':
    # 测试参数
    W = 5
    T = 400  # 200ns / 0.5ns
    P = 200  # 20m / 0.1m

    # 实例化模型
    model = HybridUNetTCN(
        window_segments=W,
        time_points_per_segment=T,
        output_segments=W - 1,
        space_points_per_segment=P,
        base_channels=64,
        depth=4,
        bottleneck_attn_heads=8,
        dropout_rate=0.5  # 添加dropout_rate参数
    )

    print("模型架构:")
    print(model)

    # 创建测试输入
    batch_size = 4
    freq_channels = 3
    input_length = W * T
    dummy_input = torch.randn(batch_size, freq_channels, input_length)
    # torch.randn: 创建一个由标准正态分布的随机数填充的张量
    # 对于维度测试，不关心张量里的具体数值，只关心它是否具有正确的形状

    print(f"\n输入形状: {dummy_input.shape}")

    # 前向传播测试
    with torch.no_grad():
        output = model(dummy_input)

    print(f"输出形状: {output.shape}")

    # 期望的输出形状 (新格式：batch, output_segments, space_points_per_segment)
    expected_output_segments = W - 1  # 4
    expected_space_points = P  # 200
    print(f"期望输出形状: ({batch_size}, {expected_output_segments}, {expected_space_points})")

    # 检查输出维度是否匹配
    assert output.shape == (batch_size, expected_output_segments, expected_space_points), "输出维度不匹配！"

    print("\n模型测试成功！输入输出维度匹配。")
