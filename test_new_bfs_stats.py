#!/usr/bin/env python3
"""
测试新的BFS统计信息
"""

import scipy.io as sio
import numpy as np
import os

def test_new_bfs_stats():
    """测试新的BFS统计信息"""
    stats_file = "./BOTDA_Dataset/bfs_stats.mat"
    
    if os.path.exists(stats_file):
        try:
            stats_data = sio.loadmat(stats_file)
            print(f"BFS统计文件键: {list(stats_data.keys())}")

            # 检查文件修改时间
            import datetime
            mod_time = os.path.getmtime(stats_file)
            mod_datetime = datetime.datetime.fromtimestamp(mod_time)
            print(f"文件修改时间: {mod_datetime}")

            # 修复：正确的访问方式
            if 'stats' in stats_data:
                # 新的数据结构：stats包含一个元组(min_array, max_array)
                stats = stats_data['stats'][0][0]  # 获取第一个元素
                print(f"stats内容: {stats}")
                print(f"stats类型: {type(stats)}")
                print(f"stats长度: {len(stats)}")
                
                bfs_min = float(stats[0][0][0])  # 第一个数组是min
                bfs_max = float(stats[1][0][0])  # 第二个数组是max
                
                print(f"✅ BFS统计信息: min={bfs_min:.2f} MHz, max={bfs_max:.2f} MHz")
                
                # 检查是否是期望的值
                if abs(bfs_min - (-40.0)) < 0.01 and abs(bfs_max - 50.0) < 0.01:
                    print("✅ BFS范围正确！")
                else:
                    print(f"❌ BFS范围不正确，期望 [-40.0, 50.0]")
                    
                return bfs_min, bfs_max
            else:
                print("❌ 无法找到stats键")
                return None, None
                
        except Exception as e:
            print(f"❌ 加载失败: {e}")
            import traceback
            traceback.print_exc()
            return None, None
    else:
        print(f"❌ BFS统计文件不存在: {stats_file}")
        return None, None

def check_new_data_files():
    """检查新生成的数据文件"""
    print("\n=== 检查新生成的数据文件 ===")
    
    # 检查各个文件夹
    folders = ['train', 'val', 'test']
    for folder in folders:
        folder_path = f"./BOTDA_Dataset/{folder}"
        if os.path.exists(folder_path):
            files = os.listdir(folder_path)
            mat_files = [f for f in files if f.endswith('.mat')]
            print(f"{folder}文件夹: {len(mat_files)} 个.mat文件")
            
            if mat_files:
                # 检查第一个文件的修改时间
                first_file = os.path.join(folder_path, mat_files[0])
                mod_time = os.path.getmtime(first_file)
                mod_datetime = datetime.datetime.fromtimestamp(mod_time)
                print(f"  最新文件: {mat_files[0]}, 修改时间: {mod_datetime}")
        else:
            print(f"{folder}文件夹: 不存在")

if __name__ == "__main__":
    import datetime
    test_new_bfs_stats()
    check_new_data_files()
