#!/usr/bin/env python3
"""
测试单个光纤结果提取和融合功能
"""

def test_extract_single_fiber():
    """
    测试单个光纤结果提取功能
    """
    print("=== 单个光纤结果提取测试 ===\n")

    # 模拟参数
    num_files = 2
    num_segments = 10  # 每个文件10段
    window_size = 5
    space_points_per_segment = 200
    
    # 计算窗口信息
    stride = window_size - 1  # 4
    standard_windows = 0
    pos = 0
    while pos + window_size <= num_segments:
        standard_windows += 1
        pos += stride
    
    # 检查是否需要向前扩展
    last_segment_idx = num_segments - 1
    has_extra_window = False
    if standard_windows > 0:
        last_window_start = (standard_windows - 1) * stride
        last_window_end = last_window_start + window_size - 1
        
        if last_segment_idx > last_window_end:
            has_extra_window = True
    
    windows_per_sample = standard_windows + (1 if has_extra_window else 0)
    
    print(f"测试参数:")
    print(f"  文件数: {num_files}")
    print(f"  每文件段数: {num_segments}")
    print(f"  窗口大小: {window_size}")
    print(f"  标准窗口数: {standard_windows}")
    print(f"  是否有向前扩展: {has_extra_window}")
    print(f"  每文件窗口数: {windows_per_sample}")
    
    # 生成模拟数据（使用列表代替numpy）
    total_windows = num_files * windows_per_sample
    predictions = []
    targets = []

    for w in range(total_windows):
        window_pred = []
        window_target = []
        for s in range(4):  # 4段
            segment_pred = [1.0] * space_points_per_segment
            segment_target = [1.0] * space_points_per_segment
            window_pred.append(segment_pred)
            window_target.append(segment_target)
        predictions.append(window_pred)
        targets.append(window_target)
    
    print(f"\n模拟数据窗口数: {len(predictions)}")

    # 模拟重叠处理后的数据（重叠段值相同）
    if has_extra_window:
        extra_window_start = num_segments - window_size
        last_standard_predicted = list(range(last_window_start, last_window_start + 4))
        extra_window_predicted = list(range(extra_window_start, extra_window_start + 4))
        overlap_segments = list(set(last_standard_predicted) & set(extra_window_predicted))

        print(f"重叠段: {overlap_segments}")

        for file_idx in range(num_files):
            file_start = file_idx * windows_per_sample
            last_std_idx = file_start + standard_windows - 1
            extra_idx = file_start + standard_windows

            # 让重叠段的值相同
            for seg_idx in overlap_segments:
                std_relative_idx = seg_idx - last_window_start
                extra_relative_idx = seg_idx - extra_window_start

                if 0 <= std_relative_idx < 4 and 0 <= extra_relative_idx < 4:
                    # 设置相同的值
                    for point_idx in range(space_points_per_segment):
                        avg_val = (predictions[last_std_idx][std_relative_idx][point_idx] +
                                  predictions[extra_idx][extra_relative_idx][point_idx]) / 2
                        predictions[last_std_idx][std_relative_idx][point_idx] = avg_val
                        predictions[extra_idx][extra_relative_idx][point_idx] = avg_val
                        targets[last_std_idx][std_relative_idx][point_idx] = avg_val
                        targets[extra_idx][extra_relative_idx][point_idx] = avg_val
    
    # 测试提取单个文件结果
    for file_index in range(num_files):
        print(f"\n=== 提取文件{file_index}的结果 ===")
        
        # 计算该文件的窗口索引范围
        file_start_idx = file_index * windows_per_sample
        file_end_idx = file_start_idx + windows_per_sample
        
        file_predictions = predictions[file_start_idx:file_end_idx]
        file_targets = targets[file_start_idx:file_end_idx]

        print(f"文件{file_index}的窗口范围: {file_start_idx}-{file_end_idx-1}")
        print(f"文件数据窗口数: {len(file_predictions)}")

        # 展平前的维度
        total_segments_before = len(file_predictions) * 4
        print(f"直接展平段数: {total_segments_before}")
        print(f"每段空间点数: {space_points_per_segment}")
        
        # 模拟融合重复段
        if has_extra_window:
            print("检测到重复段，进行融合...")
            
            # 拼接非重复部分
            segments_list = []
            
            # 添加前面的标准窗口（除了最后一个）
            for w in range(standard_windows - 1):
                segments_list.append(file_predictions[w])
            
            # 处理最后一个标准窗口和向前扩展窗口
            last_standard_window = file_predictions[standard_windows - 1]
            extra_window = file_predictions[standard_windows]
            
            # 添加最后标准窗口的所有段
            segments_list.append(last_standard_window)
            
            # 添加向前扩展窗口的非重复段
            for seg_idx in extra_window_predicted:
                if seg_idx not in overlap_segments:
                    extra_relative_idx = seg_idx - extra_window_start
                    segments_list.append(extra_window[extra_relative_idx:extra_relative_idx+1])
            
            # 拼接所有段
            fiber_prediction = []
            for seg_list in segments_list:
                if len(seg_list) == 4:  # 整个窗口
                    fiber_prediction.extend(seg_list)
                else:  # 单个段
                    fiber_prediction.extend(seg_list)

            print(f"融合后段数: {len(fiber_prediction)}")

            # 验证融合效果
            print("融合前后对比:")
            print(f"  融合前: {len(file_predictions)}个窗口 × 4段 = {len(file_predictions) * 4}段")
            print(f"  融合后: {len(fiber_prediction)}段")

            # 计算预期的段数
            expected_segments = (standard_windows - 1) * 4 + 4 + (4 - len(overlap_segments))
            print(f"  预期段数: {expected_segments}")
            print(f"  注意：原始{num_segments}段，最后一段无法预测，所以最终只有{num_segments-1}段")

            if len(fiber_prediction) == expected_segments == num_segments - 1:
                print("  ✅ 融合正确")
            else:
                print("  ❌ 融合错误")
        else:
            print("无重复段，直接拼接")
            fiber_prediction = []
            for window in file_predictions:
                fiber_prediction.extend(window)
            print(f"拼接后段数: {len(fiber_prediction)}")

if __name__ == "__main__":
    test_extract_single_fiber()
