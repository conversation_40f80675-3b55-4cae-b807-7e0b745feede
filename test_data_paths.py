#!/usr/bin/env python3
"""
测试数据路径设置是否正确
"""

import os
import glob

def test_data_structure():
    """
    测试数据目录结构是否正确
    """
    print("=== 数据路径验证测试 ===\n")
    
    # 检查主数据目录
    data_dir = "./BOTDA_Dataset"
    print(f"检查主数据目录: {data_dir}")
    
    if not os.path.exists(data_dir):
        print(f"❌ 主数据目录不存在: {data_dir}")
        print("请确保BOTDA_Dataset文件夹在当前目录下")
        return False
    else:
        print(f"✅ 主数据目录存在")
    
    # 检查bfs_stats.mat文件
    bfs_stats_path = os.path.join(data_dir, "bfs_stats.mat")
    print(f"\n检查BFS统计文件: {bfs_stats_path}")
    
    if not os.path.exists(bfs_stats_path):
        print(f"❌ BFS统计文件不存在: {bfs_stats_path}")
    else:
        print(f"✅ BFS统计文件存在")
    
    # 检查各个子目录
    subdirs = ['train', 'val', 'test']
    all_good = True
    
    for subdir in subdirs:
        subdir_path = os.path.join(data_dir, subdir)
        print(f"\n检查{subdir}目录: {subdir_path}")
        
        if not os.path.exists(subdir_path):
            print(f"❌ {subdir}目录不存在")
            all_good = False
            continue
        else:
            print(f"✅ {subdir}目录存在")
        
        # 检查该目录下的mat文件
        pattern = os.path.join(subdir_path, f"{subdir}_sample_*.mat")
        files = sorted(glob.glob(pattern))
        
        print(f"   查找模式: {pattern}")
        print(f"   找到文件数: {len(files)}")
        
        if len(files) == 0:
            print(f"❌ {subdir}目录下没有找到数据文件")
            all_good = False
        else:
            print(f"✅ 找到{len(files)}个{subdir}文件")
            # 显示前几个文件名
            for i, file_path in enumerate(files[:3]):
                filename = os.path.basename(file_path)
                print(f"   - {filename}")
            if len(files) > 3:
                print(f"   - ... 还有{len(files)-3}个文件")
    
    return all_good

def test_file_loading():
    """
    测试文件加载是否正常
    """
    print(f"\n=== 文件加载测试 ===")
    
    data_dir = "./BOTDA_Dataset"
    
    # 测试训练文件
    train_pattern = os.path.join(data_dir, "train", "train_sample_*.mat")
    train_files = sorted(glob.glob(train_pattern))
    
    if len(train_files) == 0:
        print("❌ 没有找到训练文件，无法进行加载测试")
        return False
    
    first_train_file = train_files[0]
    print(f"测试加载第一个训练文件: {first_train_file}")
    
    try:
        import scipy.io as sio
        sample_data = sio.loadmat(first_train_file)
        
        print("✅ 文件加载成功")
        print("文件中的变量:")
        
        for key in sample_data.keys():
            if not key.startswith('__'):
                var = sample_data[key]
                if hasattr(var, 'shape'):
                    print(f"   {key}: {var.shape}")
                else:
                    print(f"   {key}: {type(var).__name__}")
        
        # 检查关键变量
        required_vars = ['stokes_signals', 'frequency_shift_distribution_normalized', 'fiber_positions']
        missing_vars = []
        
        for var_name in required_vars:
            if var_name not in sample_data:
                missing_vars.append(var_name)
        
        if missing_vars:
            print(f"❌ 缺少必要变量: {missing_vars}")
            return False
        else:
            print("✅ 所有必要变量都存在")
            return True
            
    except Exception as e:
        print(f"❌ 文件加载失败: {e}")
        return False

def test_path_consistency():
    """
    测试代码中的路径设置是否一致
    """
    print(f"\n=== 路径一致性检查 ===")
    
    # 检查训练代码中的路径
    train_file = "./botda_train.py"
    test_file = "./botda_test.py"
    
    files_to_check = [
        (train_file, "训练代码"),
        (test_file, "测试代码")
    ]
    
    for file_path, file_desc in files_to_check:
        print(f"\n检查{file_desc}: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查数据目录设置
            if 'DATA_DIR = "./BOTDA_Dataset"' in content:
                print(f"✅ {file_desc}中的数据路径设置正确")
            elif 'DATA_DIR = "./DL_DATA"' in content:
                print(f"❌ {file_desc}中的数据路径设置错误 (仍然是./DL_DATA)")
            else:
                print(f"⚠️  {file_desc}中未找到DATA_DIR设置")
            
            # 检查BFS统计文件路径
            if './BOTDA_Dataset/bfs_stats.mat' in content:
                print(f"✅ {file_desc}中的BFS统计文件路径正确")
            elif './DL_DATA/bfs_stats.mat' in content:
                print(f"❌ {file_desc}中的BFS统计文件路径错误")
            
        except Exception as e:
            print(f"❌ 读取{file_desc}失败: {e}")

def main():
    """
    主测试函数
    """
    print("开始验证数据路径设置...\n")
    
    # 测试数据目录结构
    structure_ok = test_data_structure()
    
    # 测试文件加载
    loading_ok = test_file_loading()
    
    # 测试路径一致性
    test_path_consistency()
    
    print(f"\n=== 总结 ===")
    if structure_ok and loading_ok:
        print("✅ 所有测试通过，数据路径设置正确")
        print("可以正常运行训练和测试代码")
    else:
        print("❌ 存在问题，请检查:")
        if not structure_ok:
            print("   - 数据目录结构不正确")
        if not loading_ok:
            print("   - 文件加载失败")
        print("请确保BOTDA_Dataset目录结构正确")

if __name__ == "__main__":
    main()
