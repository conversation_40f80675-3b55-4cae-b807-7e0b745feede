# 测试训练配置修改
import time

def test_early_stopping_logic():
    """测试早停逻辑"""
    print("=" * 50)
    print("早停逻辑测试")
    print("=" * 50)

    # 模拟参数
    MIN_EPOCHS = 5
    MAX_EPOCHS = 20
    EARLY_STOP_PATIENCE = 3

    # 模拟验证损失序列（先下降后上升）
    val_losses = [1.0, 0.8, 0.6, 0.5, 0.4, 0.45, 0.5, 0.55, 0.6, 0.65, 0.7, 0.75]

    best_val_loss = float('inf')
    early_stop_counter = 0
    best_epoch = 0

    print(f"配置: MIN_EPOCHS={MIN_EPOCHS}, MAX_EPOCHS={MAX_EPOCHS}, PATIENCE={EARLY_STOP_PATIENCE}")
    print(f"模拟验证损失: {val_losses}")
    print("修改后逻辑: 只有在达到最少训练轮数后才开始计算早停计数器")
    print()

    for epoch in range(MAX_EPOCHS):
        if epoch < len(val_losses):
            val_loss = val_losses[epoch]
        else:
            val_loss = 0.8  # 后续保持高损失

        print(f"Epoch {epoch+1}: val_loss={val_loss:.3f}", end="")

        # 检查是否是最佳模型
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_epoch = epoch
            early_stop_counter = 0
            print(f" -> 新最佳! 重置计数器")
        else:
            # 修改后的逻辑：只有在达到最少训练轮数后才开始计算早停计数器
            if epoch >= MIN_EPOCHS - 1:
                early_stop_counter += 1
                print(f" -> 计数器: {early_stop_counter}/{EARLY_STOP_PATIENCE}")
            else:
                print(f" -> 最少轮数阶段，不计数 ({epoch+1}/{MIN_EPOCHS})")

        # 早停检查
        if epoch >= MIN_EPOCHS - 1:
            if early_stop_counter >= EARLY_STOP_PATIENCE:
                print(f"\n早停触发! 连续{EARLY_STOP_PATIENCE}轮无改善")
                print(f"最佳: {best_val_loss:.3f} (Epoch {best_epoch+1})")
                break

    print(f"\n最终结果:")
    print(f"- 训练轮数: {epoch+1}")
    print(f"- 最佳验证损失: {best_val_loss:.3f}")
    print(f"- 最佳轮数: {best_epoch+1}")

def test_realistic_scenario():
    """测试更真实的场景"""
    print("\n" + "=" * 50)
    print("真实场景测试 (MIN_EPOCHS=100, PATIENCE=150)")
    print("=" * 50)

    MIN_EPOCHS = 100
    EARLY_STOP_PATIENCE = 150

    # 模拟场景：前100轮有改善，后面150轮无改善
    best_val_loss = float('inf')
    early_stop_counter = 0
    best_epoch = 0

    print("模拟场景：前100轮持续改善，第100轮达到最佳，后面150轮无改善")

    # 模拟关键轮次
    key_epochs = [1, 50, 99, 100, 150, 200, 249, 250]

    for i, epoch in enumerate(key_epochs):
        epoch_idx = epoch - 1  # 转为0-based索引

        if epoch <= 100:
            val_loss = 1.0 - epoch * 0.009  # 持续下降到0.1
        else:
            val_loss = 0.11  # 后续保持较高损失

        print(f"Epoch {epoch}: val_loss={val_loss:.3f}", end="")

        if val_loss < best_val_loss:
            best_val_loss = val_loss
            best_epoch = epoch_idx
            early_stop_counter = 0
            print(f" -> 新最佳! 重置计数器")
        else:
            if epoch_idx >= MIN_EPOCHS - 1:
                early_stop_counter += (epoch - key_epochs[i-1] if i > 0 and key_epochs[i-1] >= MIN_EPOCHS else 1)
                print(f" -> 计数器: {early_stop_counter}/{EARLY_STOP_PATIENCE}")
            else:
                print(f" -> 最少轮数阶段")

        if epoch_idx >= MIN_EPOCHS - 1 and early_stop_counter >= EARLY_STOP_PATIENCE:
            print(f"\n早停触发! 在第{epoch}轮")
            break

    print(f"\n结果: 第100轮达到最佳(0.1)，第250轮触发早停")

def test_time_formatting():
    """测试时间格式化"""
    print("\n" + "=" * 50)
    print("时间格式化测试")
    print("=" * 50)
    
    test_times = [65, 3661, 7323, 86400]  # 秒
    
    for total_time in test_times:
        hours = int(total_time // 3600)
        minutes = int((total_time % 3600) // 60)
        seconds = int(total_time % 60)
        
        print(f"{total_time}秒 -> {hours:02d}:{minutes:02d}:{seconds:02d}")

def test_loss_curve_data():
    """测试损失曲线数据"""
    print("\n" + "=" * 50)
    print("损失曲线数据测试")
    print("=" * 50)
    
    # 模拟训练数据
    train_losses = [1.0, 0.8, 0.6, 0.5, 0.4, 0.45, 0.5]
    val_losses = [1.1, 0.9, 0.7, 0.6, 0.5, 0.55, 0.6]
    best_epoch = 4
    best_val_loss = 0.5
    
    print(f"训练损失: {train_losses}")
    print(f"验证损失: {val_losses}")
    print(f"最佳轮数: {best_epoch+1}")
    print(f"最佳验证损失: {best_val_loss}")
    
    # 模拟绘图数据
    epochs_range = list(range(1, len(train_losses) + 1))
    print(f"X轴(轮数): {epochs_range}")
    print(f"最佳点坐标: ({best_epoch+1}, {best_val_loss})")

if __name__ == "__main__":
    test_early_stopping_logic()
    test_realistic_scenario()
    test_time_formatting()
    test_loss_curve_data()

    print("\n" + "=" * 50)
    print("测试完成!")
    print("=" * 50)
