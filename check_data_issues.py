#!/usr/bin/env python3
"""
检查数据问题的脚本
"""

import scipy.io as sio
import numpy as np
import os

def check_bfs_stats():
    """检查BFS统计信息"""
    print("=== 检查BFS统计信息 ===")
    
    stats_file = "./BOTDA_Dataset/bfs_stats.mat"
    if os.path.exists(stats_file):
        try:
            stats = sio.loadmat(stats_file)
            print(f"BFS统计文件键: {list(stats.keys())}")
            
            # 尝试不同的访问方式
            for key in stats.keys():
                if not key.startswith('__'):
                    print(f"键 '{key}': {stats[key]}")
                    
            # 尝试标准访问方式
            if 'min' in stats and 'max' in stats:
                bfs_min = float(stats['min'][0][0][0][0])
                bfs_max = float(stats['max'][0][0][0][0])
                print(f"BFS范围: [{bfs_min:.2f}, {bfs_max:.2f}] MHz")
            else:
                print("❌ 无法找到min/max键")
                
        except Exception as e:
            print(f"❌ 读取BFS统计文件失败: {e}")
    else:
        print(f"❌ BFS统计文件不存在: {stats_file}")

def check_matlab_data():
    """检查MATLAB数据文件"""
    print("\n=== 检查MATLAB数据文件 ===")
    
    # 检查第一个训练文件
    train_file = "./BOTDA_Dataset/train/train_sample_0001.mat"
    if os.path.exists(train_file):
        try:
            data = sio.loadmat(train_file)
            print(f"MATLAB文件键: {list(data.keys())}")
            
            if 'sample_data' in data:
                sample_data = data['sample_data']
                print(f"sample_data类型: {type(sample_data)}")
                
                # 获取实际数据
                actual_data = sample_data['sample_data'][0, 0] if hasattr(sample_data, 'dtype') else sample_data
                if hasattr(actual_data, 'dtype'):
                    print(f"内部数据键: {list(actual_data.dtype.names)}")
                    
                    # 检查segment_ranges
                    if 'segment_ranges' in actual_data.dtype.names:
                        segment_ranges = actual_data['segment_ranges'][0, 0]
                        print(f"segment_ranges形状: {segment_ranges.shape}")
                        print(f"segment_ranges数据类型: {segment_ranges.dtype}")
                        print(f"segment_ranges内容:")
                        for i in range(min(len(segment_ranges), 5)):
                            print(f"  段{i}: [{segment_ranges[i, 0]:.6f}, {segment_ranges[i, 1]:.6f}]")
                    
                    # 检查BFS分布
                    if 'frequency_shift_distribution_normalized' in actual_data.dtype.names:
                        bfs = actual_data['frequency_shift_distribution_normalized'][0, 0]
                        print(f"BFS分布形状: {bfs.shape}")
                        print(f"BFS分布数据类型: {bfs.dtype}")
                        print(f"BFS分布范围: [{bfs.min():.6f}, {bfs.max():.6f}]")
                        
                        # 检查是否已经归一化
                        if bfs.min() >= 0 and bfs.max() <= 1:
                            print("✅ BFS数据已归一化到[0,1]")
                        else:
                            print("❌ BFS数据未正确归一化")
                    
                    # 检查应变区域信息
                    if 'strain_regions' in actual_data.dtype.names:
                        strain_regions = actual_data['strain_regions'][0, 0]
                        print(f"应变区域形状: {strain_regions.shape}")
                        print(f"应变区域数据类型: {strain_regions.dtype}")
                        
                        if len(strain_regions) > 0:
                            print("应变区域信息:")
                            for i, region in enumerate(strain_regions):
                                if hasattr(region, 'dtype'):
                                    start_pos = region['start_position'][0, 0][0, 0] if 'start_position' in region.dtype.names else 'N/A'
                                    end_pos = region['end_position'][0, 0][0, 0] if 'end_position' in region.dtype.names else 'N/A'
                                    length = region['length'][0, 0][0, 0] if 'length' in region.dtype.names else 'N/A'
                                    print(f"  区域{i}: 起始={start_pos}m, 结束={end_pos}m, 长度={length}m")
                        else:
                            print("❌ 没有应变区域信息")
                            
        except Exception as e:
            print(f"❌ 读取MATLAB文件失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print(f"❌ 训练文件不存在: {train_file}")

def check_generate_matlab_file():
    """检查MATLAB生成脚本的副本"""
    print("\n=== 检查MATLAB生成脚本 ===")
    
    matlab_file = "generate_yhbfuuju_qudnjixm - 副本.m"
    if os.path.exists(matlab_file):
        print(f"✅ 找到MATLAB脚本: {matlab_file}")
        
        # 读取文件内容查找关键参数
        try:
            with open(matlab_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找应变长度范围
            if 'min_length' in content and 'max_length' in content:
                lines = content.split('\n')
                for line in lines:
                    if 'min_length' in line and '=' in line:
                        print(f"  {line.strip()}")
                    if 'max_length' in line and '=' in line:
                        print(f"  {line.strip()}")
            
            # 查找频移范围
            if 'frequency_shift' in content.lower():
                lines = content.split('\n')
                for line in lines:
                    if 'frequency' in line.lower() and 'shift' in line.lower():
                        print(f"  {line.strip()}")
                        
        except Exception as e:
            print(f"❌ 读取MATLAB脚本失败: {e}")
    else:
        print(f"❌ MATLAB脚本不存在: {matlab_file}")

if __name__ == "__main__":
    check_bfs_stats()
    check_matlab_data()
    check_generate_matlab_file()
    
    print("\n=== 总结 ===")
    print("1. 检查BFS统计信息是否正确")
    print("2. 检查MATLAB数据中的segment_ranges数据类型")
    print("3. 检查应变区域长度是否符合预期(0.5-10m)")
    print("4. 检查BFS归一化是否正确")
