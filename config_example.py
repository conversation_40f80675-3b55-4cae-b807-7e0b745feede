#!/usr/bin/env python3
"""
config.py使用示例

展示如何在训练代码中使用配置文件
"""

from config import BOTDAConfig, get_debug_config, get_fast_config, get_high_quality_config

def example_usage():
    """配置文件使用示例"""
    
    print("=== Config.py 使用示例 ===\n")
    
    # 方法1: 使用默认配置
    print("1. 默认配置:")
    config = BOTDAConfig()
    config.print_config()
    
    # 方法2: 使用预定义配置
    print("\n2. 调试配置:")
    debug_config = get_debug_config()
    print(f"批大小: {debug_config.BATCH_SIZE}")
    print(f"最大轮数: {debug_config.MAX_EPOCHS}")
    print(f"基础通道数: {debug_config.BASE_CHANNELS}")
    
    print("\n3. 快速训练配置:")
    fast_config = get_fast_config()
    print(f"批大小: {fast_config.BATCH_SIZE}")
    print(f"最大轮数: {fast_config.MAX_EPOCHS}")
    print(f"基础通道数: {fast_config.BASE_CHANNELS}")
    
    # 方法3: 自定义配置
    print("\n4. 自定义配置:")
    custom_config = BOTDAConfig()
    custom_config.BATCH_SIZE = 12
    custom_config.BASE_CHANNELS = 96
    custom_config.LEARNING_RATE = 5e-4
    print(f"自定义批大小: {custom_config.BATCH_SIZE}")
    print(f"自定义基础通道数: {custom_config.BASE_CHANNELS}")
    print(f"自定义学习率: {custom_config.LEARNING_RATE}")
    
    # 方法4: 获取配置字典
    print("\n5. 配置字典:")
    model_config = config.get_model_config()
    training_config = config.get_training_config()
    data_config = config.get_data_config()
    
    print("模型配置字典:")
    for key, value in model_config.items():
        print(f"  {key}: {value}")
    
    print("\n如何在训练代码中使用:")
    print("""
# 在 botda_train.py 中的使用方法：

from config import BOTDAConfig, get_fast_config

def main():
    # 选择配置
    config = get_fast_config()  # 或者 BOTDAConfig()
    
    # 使用配置参数
    DATA_DIR = config.DATA_DIR
    BATCH_SIZE = config.BATCH_SIZE
    LEARNING_RATE = config.LEARNING_RATE
    BASE_CHANNELS = config.BASE_CHANNELS
    
    # 创建模型
    model = HybridUNetTCN(
        input_channels=config.STOKES_CHANNELS,
        base_channels=config.BASE_CHANNELS,
        depth=config.DEPTH,
        # ... 其他参数
    )
    
    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        batch_size=config.BATCH_SIZE,
        num_workers=config.NUM_WORKERS,
        pin_memory=config.PIN_MEMORY
    )
    
    # 创建优化器
    optimizer = torch.optim.Adam(
        model.parameters(),
        lr=config.LEARNING_RATE,
        weight_decay=config.WEIGHT_DECAY
    )
""")

if __name__ == "__main__":
    example_usage()
