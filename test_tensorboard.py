"""
测试TensorBoard是否正确安装
"""

try:
    import tensorboard
    print("✅ tensorboard 导入成功")
    print(f"TensorBoard版本: {tensorboard.__version__}")
except ImportError as e:
    print("❌ tensorboard 导入失败:", e)

try:
    from torch.utils.tensorboard import SummaryWriter
    print("✅ torch.utils.tensorboard 导入成功")
    
    # 创建一个简单的测试
    writer = SummaryWriter('./test_logs')
    writer.add_scalar('test/loss', 0.5, 1)
    writer.close()
    print("✅ TensorBoard写入测试成功")
    
except ImportError as e:
    print("❌ torch.utils.tensorboard 导入失败:", e)

try:
    import torch
    print(f"✅ PyTorch版本: {torch.__version__}")
except ImportError as e:
    print("❌ PyTorch 导入失败:", e)
