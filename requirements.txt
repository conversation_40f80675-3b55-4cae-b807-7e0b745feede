# BOTDA 深度学习信号处理系统依赖包
#
# 安装方法：
# pip install -r requirements.txt
#
# 推荐Python版本：3.8-3.11

# ========== 核心深度学习框架 ==========
torch>=1.12.0,<3.0.0
torchvision>=0.13.0
torchaudio>=0.12.0

# ========== 信号处理专用库 ==========
PyWavelets>=1.1.0         # pytorch_wavelets的隐式依赖，必须先安装
pytorch_wavelets==1.3.0  # 小波变换损失函数，解决MSE"和稀泥"问题，强化高频细节保持

# ========== 数据处理和科学计算 ==========
numpy>=1.21.0,<2.0.0
scipy>=1.7.0,<2.0.0
pandas>=1.3.0,<3.0.0      # 数据分析和CSV文件处理（compare_three_methods.py需要）

# ========== 机器学习工具 ==========
scikit-learn>=1.0.0,<2.0.0

# ========== 可视化和绘图 ==========
matplotlib>=3.5.0,<4.0.0

# ========== 训练监控和日志 ==========
tensorboard>=2.8.0,<3.0.0
tensorflow>=2.8.0,<3.0.0

# ========== 进度条和用户界面 ==========
tqdm>=4.62.0,<5.0.0

# ========== 可选依赖 ==========
# 数据分析（已移至必需依赖）

# 高级可视化（可选）
# seaborn>=0.11.0,<1.0.0

# 图像处理（可选）
# Pillow>=8.3.0,<11.0.0

# 配置文件处理（可选）
# pyyaml>=6.0,<7.0.0

# 数据存储（可选）
# h5py>=3.6.0,<4.0.0

# 实验跟踪（可选）
# wandb>=0.12.0,<1.0.0

# ========== 重要说明 ==========
# pytorch_wavelets 是小波感知损失函数的核心依赖
# 用于解决传统MSE损失的"和稀泥"问题，强化BOTDA-BFS信号的高频细节保持
# 如果安装失败，系统会自动回退到FFT频域损失替代方案
#
# 手动安装命令：
# pip install pytorch_wavelets==1.3.0
#
# 验证安装：
# python -c "from pytorch_wavelets import DWT1DForward; print('✅ pytorch_wavelets 安装成功')"
