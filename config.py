# ---------------------------------------------------------------
#  BOTDA 深度学习信号恢复网络配置文件
#
#  基于HybridUNetTCN架构，包含所有可配置的参数
#  注意：当前项目中此文件未被使用，参数直接在botda_train.py中设置
# --------------------------------------------------------------

import os
from typing import Dict, Any

class BOTDAConfig:
    """BOTDA深度学习信号恢复网络配置类"""

    def __init__(self):
        # ==================== 数据配置 ====================
        self.DATA_DIR = "./BOTDA_Dataset"              # 数据集根目录
        self.WINDOW_SIZE = 5                           # 滑窗大小（段数）
        self.STRIDE = 4                                # 滑窗步长（WINDOW_SIZE-1）
        self.MAX_SAMPLES_TRAIN = None                  # 训练样本数限制（None=全部）
        self.MAX_SAMPLES_VAL = None                    # 验证样本数限制
        self.MAX_SAMPLES_TEST = None                   # 测试样本数限制
        self.BFS_STATS_FILE = "./BOTDA_Dataset/bfs_stats.mat"  # BFS归一化统计文件
        
        # ==================== 模型配置 ====================
        self.STOKES_CHANNELS = 3                      # 斯托克斯信号频率通道数
        self.BASE_CHANNELS = 64                       # HybridUNetTCN基础通道数
        self.DEPTH = 4                                # U-Net编码器-解码器深度
        self.STEM_KERNEL_SIZE = 7                     # Stem层卷积核大小
        self.HEAD_STRIDE = 3                          # Head层步长（用于长度压缩）
        self.USE_ATTENTION = True                     # 是否在瓶颈层使用自注意力
        
        # ==================== 训练配置 ====================
        self.BATCH_SIZE = 8                           # 批大小
        self.MIN_EPOCHS = 100                         # 最少训练轮数
        self.MAX_EPOCHS = 400                         # 最大训练轮数
        self.LEARNING_RATE = 1e-3                     # 初始学习率
        self.WEIGHT_DECAY = 1e-5                      # 权重衰减（L2正则化）
        self.NUM_WORKERS = 4                          # 数据加载进程数
        self.PIN_MEMORY = True                        # 是否使用pin_memory加速
        
        # ==================== 优化器配置 ====================
        self.OPTIMIZER = "Adam"                       # 优化器类型
        self.LR_SCHEDULER = "ReduceLROnPlateau"       # 学习率调度器
        self.LR_PATIENCE = 50                         # 学习率调度耐心值
        self.LR_FACTOR = 0.5                          # 学习率衰减因子
        self.LR_MIN = 1e-6                            # 最小学习率
        
        # ==================== 损失函数配置 ====================
        self.LOSS_FUNCTION = "MSE"                    # 损失函数类型
        self.LOSS_WEIGHTS = None                      # 损失权重（如果需要）
        
        # ==================== 保存配置 ====================
        self.SAVE_DIR = "./checkpoints"               # 模型保存目录
        self.RESULTS_DIR = "./test_results"           # 测试结果保存目录
        self.TENSORBOARD_DIR = None                   # TensorBoard日志目录（None=自动生成）
        self.SAVE_FREQUENCY = 10                      # 检查点保存频率（每N个epoch）
        self.KEEP_BEST_ONLY = True                    # 是否只保留最佳模型
        
        # ==================== 可视化配置 ====================
        self.PLOT_SAMPLES = 5                         # 可视化样本数量
        self.PLOT_DPI = 300                           # 图像分辨率
        self.PLOT_FORMAT = "png"                      # 图像格式
        
        # ==================== 设备配置 ====================
        self.DEVICE = "auto"                          # 设备选择（"auto", "cpu", "cuda"）
        self.MIXED_PRECISION = False                  # 是否使用混合精度训练
        
        # ==================== 调试配置 ====================
        self.DEBUG_MODE = False                       # 调试模式
        self.VERBOSE = True                           # 详细输出
        self.LOG_FREQUENCY = 100                      # 日志输出频率
        
        # ==================== 验证配置 ====================
        self.VALIDATION_FREQUENCY = 1                # 验证频率（每N个epoch）
        self.EARLY_STOPPING = True                   # 是否启用早停
        self.EARLY_STOPPING_PATIENCE = 150           # 早停耐心值
        self.EARLY_STOPPING_MIN_DELTA = 1e-6         # 早停最小改善阈值
        
    def get_model_config(self) -> Dict[str, Any]:
        """获取HybridUNetTCN模型配置字典"""
        return {
            'window_size': self.WINDOW_SIZE,
            'stokes_channels': self.STOKES_CHANNELS,
            'base_channels': self.BASE_CHANNELS,
            'depth': self.DEPTH,
            'stem_kernel_size': self.STEM_KERNEL_SIZE,
            'head_stride': self.HEAD_STRIDE,
            'use_attention': self.USE_ATTENTION
        }
    
    def get_training_config(self) -> Dict[str, Any]:
        """获取训练配置字典"""
        return {
            'batch_size': self.BATCH_SIZE,
            'min_epochs': self.MIN_EPOCHS,
            'max_epochs': self.MAX_EPOCHS,
            'learning_rate': self.LEARNING_RATE,
            'weight_decay': self.WEIGHT_DECAY,
            'optimizer': self.OPTIMIZER,
            'lr_scheduler': self.LR_SCHEDULER,
            'lr_patience': self.LR_PATIENCE,
            'lr_factor': self.LR_FACTOR,
            'lr_min': self.LR_MIN,
            'early_stopping': self.EARLY_STOPPING,
            'early_stopping_patience': self.EARLY_STOPPING_PATIENCE
        }
    
    def get_data_config(self) -> Dict[str, Any]:
        """获取数据配置字典"""
        return {
            'data_dir': self.DATA_DIR,
            'window_size': self.WINDOW_SIZE,
            'stride': self.STRIDE,
            'max_samples_train': self.MAX_SAMPLES_TRAIN,
            'max_samples_val': self.MAX_SAMPLES_VAL,
            'max_samples_test': self.MAX_SAMPLES_TEST,
            'num_workers': self.NUM_WORKERS,
            'pin_memory': self.PIN_MEMORY,
            'bfs_stats_file': self.BFS_STATS_FILE
        }
    
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.SAVE_DIR,
            self.RESULTS_DIR
        ]

        # 如果指定了TensorBoard目录，也创建它
        if self.TENSORBOARD_DIR:
            directories.append(self.TENSORBOARD_DIR)
        else:
            # 自动生成TensorBoard目录
            tensorboard_dir = os.path.join(self.SAVE_DIR, "tensorboard")
            directories.append(tensorboard_dir)

        for directory in directories:
            os.makedirs(directory, exist_ok=True)
            if self.VERBOSE:
                print(f"目录已创建/确认: {directory}")
    
    def validate_config(self):
        """验证配置的有效性"""
        errors = []
        
        # 检查数据目录
        if not os.path.exists(self.DATA_DIR):
            errors.append(f"数据目录不存在: {self.DATA_DIR}")
        
        # 检查窗口大小
        if self.WINDOW_SIZE < 3:
            errors.append("窗口大小必须至少为3")

        # 检查步长
        if self.STRIDE != self.WINDOW_SIZE - 1:
            print(f"警告: 步长通常应该等于窗口大小-1，当前窗口={self.WINDOW_SIZE}, 步长={self.STRIDE}")
        
        # 检查批大小
        if self.BATCH_SIZE <= 0:
            errors.append("批大小必须大于0")
        
        # 检查学习率
        if self.LEARNING_RATE <= 0:
            errors.append("学习率必须大于0")
        
        # 检查模型深度
        if self.DEPTH <= 0:
            errors.append("U-Net深度必须大于0")

        # 检查基础通道数
        if self.BASE_CHANNELS <= 0 or self.BASE_CHANNELS % 8 != 0:
            errors.append("基础通道数必须大于0且为8的倍数")
        
        if errors:
            raise ValueError("配置验证失败:\n" + "\n".join(errors))
        
        if self.VERBOSE:
            print("配置验证通过")
    
    def print_config(self):
        """打印当前配置"""
        print("=" * 60)
        print("BOTDA深度学习信号恢复网络配置")
        print("=" * 60)
        
        sections = [
            ("数据配置", self.get_data_config()),
            ("模型配置", self.get_model_config()),
            ("训练配置", self.get_training_config())
        ]
        
        for section_name, section_config in sections:
            print(f"\n{section_name}:")
            print("-" * 30)
            for key, value in section_config.items():
                print(f"  {key:20s}: {value}")
        
        print("\n" + "=" * 60)


# ==================== 预定义配置 ====================

def get_debug_config() -> BOTDAConfig:
    """获取调试配置（小数据集，快速训练）"""
    config = BOTDAConfig()
    config.MAX_SAMPLES_TRAIN = 50
    config.MAX_SAMPLES_VAL = 20
    config.MAX_SAMPLES_TEST = 10
    config.MIN_EPOCHS = 5
    config.MAX_EPOCHS = 20
    config.BATCH_SIZE = 4
    config.BASE_CHANNELS = 32
    config.DEPTH = 2
    config.DEBUG_MODE = True
    config.VERBOSE = True
    return config


def get_fast_config() -> BOTDAConfig:
    """获取快速训练配置"""
    config = BOTDAConfig()
    config.BATCH_SIZE = 16
    config.MIN_EPOCHS = 50
    config.MAX_EPOCHS = 150
    config.BASE_CHANNELS = 32
    config.DEPTH = 3
    config.EARLY_STOPPING_PATIENCE = 50
    return config


def get_high_quality_config() -> BOTDAConfig:
    """获取高质量训练配置"""
    config = BOTDAConfig()
    config.BATCH_SIZE = 4
    config.MIN_EPOCHS = 200
    config.MAX_EPOCHS = 600
    config.BASE_CHANNELS = 128
    config.DEPTH = 5
    config.LEARNING_RATE = 5e-4
    config.EARLY_STOPPING_PATIENCE = 200
    return config


def get_large_window_config() -> BOTDAConfig:
    """获取大窗口配置（更多上下文）"""
    config = BOTDAConfig()
    config.WINDOW_SIZE = 7
    config.STRIDE = 6  # WINDOW_SIZE - 1
    config.BASE_CHANNELS = 96
    config.DEPTH = 4
    config.BATCH_SIZE = 6  # 大窗口需要更多内存，减小批大小
    return config


# ==================== 使用示例 ====================
if __name__ == "__main__":
    # 创建默认配置
    config = BOTDAConfig()
    
    # 验证配置
    try:
        config.validate_config()
        print("✅ 配置验证成功")
    except ValueError as e:
        print(f"❌ 配置验证失败: {e}")
    
    # 打印配置
    config.print_config()
    
    # 创建目录
    config.create_directories()
    
    print("\n可用的预定义配置:")
    print("- get_debug_config(): 调试配置（快速验证）")
    print("- get_fast_config(): 快速训练配置（平衡性能和速度）")
    print("- get_high_quality_config(): 高质量配置（最佳性能）")
    print("- get_large_window_config(): 大窗口配置（更多上下文信息）")

    print(f"\n注意：当前项目中此配置文件未被使用")
    print(f"所有参数都直接在 botda_train.py 中设置")
    print(f"如需使用此配置文件，请参考README.md中的说明")
