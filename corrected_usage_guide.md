# 三种方法BFS分布对比分析使用指南（修正版）

## 📋 概述

本指南基于正确的数据流：**test.m → erjp_tsyigsui_200m.m → compare_three_methods.py**

## 🔄 正确的使用流程

### 步骤1：生成测试样本
```matlab
% 在MATLAB中运行
test
```

**功能**：
- 生成包含0.5m频移分布的测试样本
- 保存归一化的BFS分布和归一化参数

**输出文件**：
- `dazuhv/test/test_sample_0001.mat` - 测试样本（包含归一化BFS分布）
- `dazuhv/bfs_stats.mat` - 归一化参数（bfs_min=-40, bfs_max=50）

### 步骤2：运行深度学习测试（可选）
```bash
python botda_test.py
```

**功能**：
- 对test.m生成的测试样本进行深度学习预测
- 保存预测结果

**输出文件**：
- `results/bfs_data_file_0.npz` - 深度学习预测结果

### 步骤3：运行MATLAB传统方法
```matlab
% 在MATLAB中运行
erjp_tsyigsui_200m
```

**修改后的功能**：
- **自动读取test.m生成的测试样本**
- **自动反归一化BFS分布**：`bfs_denormalized = bfs_normalized * (bfs_max - bfs_min) + bfs_min`
- **替换原始BFS计算**：使用test.m的真实BFS作为传统方法的输入
- **保存传统方法的处理结果**

**输出文件**：
- `results/matlab_bfs_measurement.mat` - MATLAB格式结果
- `results/matlab_bfs_measurement.csv` - CSV格式结果

### 步骤4：运行三方法对比分析
```bash
python compare_three_methods.py
```

**功能**：
- 读取test.m的真实BFS分布（作为Ground Truth）
- 读取深度学习预测结果（如果存在）
- 读取MATLAB传统方法结果
- 自动检测0.5m频移区域并生成对比图

**输出文件**：
- `results/three_methods_comparison.png` - 主对比图
- `results/strain_region_X_zoom.png` - 频移区域放大图

## 🔧 关键修改说明

### MATLAB文件修改要点

1. **数据读取**：
   ```matlab
   % 读取test.m测试样本
   test_data_path = 'dazuhv/test/test_sample_0001.mat';
   bfs_stats_path = 'dazuhv/bfs_stats.mat';
   
   % 加载数据
   loaded_sample = load(test_data_path);
   loaded_stats = load(bfs_stats_path);
   ```

2. **反归一化**：
   ```matlab
   % 提取归一化参数
   bfs_min = bfs_stats.min;  % -40 MHz
   bfs_max = bfs_stats.max;  % 50 MHz
   
   % 反归一化BFS分布
   bfs_denormalized = bfs_normalized * (bfs_max - bfs_min) + bfs_min;
   ```

3. **BFS分布替换**：
   ```matlab
   if use_test_data
       sorted_positions = z_positions_test;
       sorted_BFS = input_bfs_distribution;  % 使用test.m的真实BFS
   else
       % 原始计算逻辑
   end
   ```

### Python对比分析修改要点

1. **读取test.m真实BFS**：
   ```python
   # 读取test.m数据
   test_data = sio.loadmat('dazuhv/test/test_sample_0001.mat')
   bfs_stats = sio.loadmat('dazuhv/bfs_stats.mat')
   
   # 反归一化得到真实BFS
   target = bfs_normalized * (bfs_max - bfs_min) + bfs_min
   ```

2. **数据对齐**：
   ```python
   # 如果数据长度不一致，自动插值对齐
   if len(prediction) != len(target):
       prediction = np.interp(target_positions, pred_positions, prediction)
   ```

## 📊 数据流向图

```
test.m
├── 生成测试样本 (归一化BFS)
├── 保存到 dazuhv/test/test_sample_0001.mat
└── 保存归一化参数到 dazuhv/bfs_stats.mat
    │
    ├── → botda_test.py (可选)
    │   └── 深度学习预测 → results/bfs_data_file_0.npz
    │
    ├── → erjp_tsyigsui_200m.m
    │   ├── 读取测试样本
    │   ├── 反归一化BFS
    │   ├── 传统方法处理
    │   └── 保存结果 → results/matlab_bfs_measurement.csv
    │
    └── → compare_three_methods.py
        ├── 读取test.m真实BFS (Ground Truth)
        ├── 读取深度学习预测 (如果存在)
        ├── 读取MATLAB传统方法结果
        └── 生成对比图
```

## 🎯 三种BFS分布的含义

1. **真实BFS分布**：来自test.m，包含0.5m频移的Ground Truth
2. **深度学习预测**：模型对test.m测试样本的预测结果
3. **MATLAB传统方法**：传统BOTDA算法对test.m真实BFS的处理结果

## ⚠️ 重要注意事项

1. **数据来源**：所有方法都基于test.m生成的同一个测试样本
2. **归一化处理**：test.m保存的是归一化数据，需要反归一化后使用
3. **文件路径**：确保test.m生成的文件路径正确
4. **MATLAB版本**：需要支持`writematrix`函数（R2019a+）

## 🔍 故障排除

### 常见问题
1. **找不到test.m数据**：确保先运行test.m生成测试样本
2. **MATLAB读取失败**：检查文件路径是否正确
3. **反归一化错误**：确保bfs_stats.mat包含正确的min/max值
4. **数据长度不匹配**：系统会自动插值对齐

### 调试信息
每个步骤都会输出详细信息：
- 文件存在性检查
- 数据形状和统计信息
- 反归一化前后的数值范围
- 处理进度确认

通过这个修正的流程，您可以准确评估三种方法在0.5m频移识别上的性能差异！
