# ---------------------------------------------------------------
#  测试滑窗策略
#  
#  验证两种滑窗模式：
#  1. 重叠滑窗（stride=1）
#  2. 非重叠滑窗（stride=window_size-2）
# ---------------------------------------------------------------

from typing import List, <PERSON><PERSON>

def simulate_sliding_window(num_segments: int, window_size: int, stride: int) -> List[Tuple[int, ...]]:
    """
    模拟滑窗过程
    
    参数：
    - num_segments: 总段数
    - window_size: 窗口大小
    - stride: 步长
    
    返回：
    - 窗口列表，每个窗口包含段索引
    """
    windows = []
    
    if num_segments < window_size:
        return windows
    
    # 计算窗口数量
    last_start_pos = num_segments - window_size
    windows_count = (last_start_pos - 0) // stride + 1
    
    # 生成窗口
    for i in range(windows_count):
        start_pos = i * stride
        window = tuple(range(start_pos, start_pos + window_size))
        windows.append(window)
    
    return windows

def get_predicted_segments(windows: List[Tuple[int, ...]], window_size: int) -> List[Tuple[int, ...]]:
    """
    获取每个窗口预测的中间段
    
    参数：
    - windows: 窗口列表
    - window_size: 窗口大小
    
    返回：
    - 预测段列表
    """
    predicted = []
    
    for window in windows:
        # 中间段索引：去掉首尾段
        middle_segments = window[1:-1]
        predicted.append(middle_segments)
    
    return predicted

def test_sliding_window_strategies():
    """测试不同的滑窗策略"""
    
    print("=" * 60)
    print("滑窗策略测试")
    print("=" * 60)
    
    # 测试参数
    num_segments = 11  # 假设有11个段（0-10）
    window_size = 5    # 窗口大小为5
    
    print(f"测试场景：")
    print(f"  总段数: {num_segments} (段索引: 0-{num_segments-1})")
    print(f"  窗口大小: {window_size}")
    print(f"  预测段数: {window_size-2} (每个窗口预测中间段)")
    
    # 策略1：重叠滑窗（stride=1）
    print(f"\n{'='*40}")
    print("策略1：重叠滑窗 (stride=1)")
    print(f"{'='*40}")
    
    stride1 = 1
    windows1 = simulate_sliding_window(num_segments, window_size, stride1)
    predicted1 = get_predicted_segments(windows1, window_size)
    
    print(f"步长: {stride1}")
    print(f"窗口数量: {len(windows1)}")
    print(f"窗口详情:")
    
    for i, (window, pred) in enumerate(zip(windows1, predicted1)):
        print(f"  窗口{i+1}: 输入段{window} → 预测段{pred}")
    
    # 统计覆盖情况
    all_predicted1 = set()
    for pred in predicted1:
        all_predicted1.update(pred)
    
    print(f"预测覆盖的段: {sorted(all_predicted1)}")
    print(f"覆盖段数: {len(all_predicted1)}")
    
    # 策略2：非重叠滑窗（stride=window_size-2）
    print(f"\n{'='*40}")
    print("策略2：非重叠滑窗 (stride=window_size-2)")
    print(f"{'='*40}")
    
    stride2 = window_size - 2  # 3
    windows2 = simulate_sliding_window(num_segments, window_size, stride2)
    predicted2 = get_predicted_segments(windows2, window_size)
    
    print(f"步长: {stride2}")
    print(f"窗口数量: {len(windows2)}")
    print(f"窗口详情:")
    
    for i, (window, pred) in enumerate(zip(windows2, predicted2)):
        print(f"  窗口{i+1}: 输入段{window} → 预测段{pred}")
    
    # 统计覆盖情况
    all_predicted2 = set()
    for pred in predicted2:
        all_predicted2.update(pred)
    
    print(f"预测覆盖的段: {sorted(all_predicted2)}")
    print(f"覆盖段数: {len(all_predicted2)}")
    
    # 对比分析
    print(f"\n{'='*40}")
    print("策略对比分析")
    print(f"{'='*40}")
    
    print(f"重叠滑窗:")
    print(f"  - 窗口数量: {len(windows1)}")
    print(f"  - 覆盖段数: {len(all_predicted1)}")
    print(f"  - 数据利用率: 高（每个段可能被多次预测）")
    print(f"  - 训练样本数: 多")
    print(f"  - 计算开销: 高")
    
    print(f"\n非重叠滑窗:")
    print(f"  - 窗口数量: {len(windows2)}")
    print(f"  - 覆盖段数: {len(all_predicted2)}")
    print(f"  - 数据利用率: 中等（每个段只被预测一次）")
    print(f"  - 训练样本数: 少")
    print(f"  - 计算开销: 低")
    
    # 检查重叠情况
    overlap_segments = []
    for i, pred1 in enumerate(predicted1):
        for j, pred2 in enumerate(predicted1):
            if i != j:
                overlap = set(pred1) & set(pred2)
                if overlap:
                    overlap_segments.extend(overlap)
    
    print(f"\n重叠滑窗中被重复预测的段: {sorted(set(overlap_segments))}")
    
    # 测试不同窗口大小
    print(f"\n{'='*40}")
    print("不同窗口大小测试")
    print(f"{'='*40}")
    
    for ws in [3, 5, 7]:
        print(f"\n窗口大小 {ws}:")
        
        # 重叠滑窗
        w1 = simulate_sliding_window(num_segments, ws, 1)
        print(f"  重叠滑窗: {len(w1)}个窗口, 预测{ws-2}段/窗口")
        
        # 非重叠滑窗
        w2 = simulate_sliding_window(num_segments, ws, ws-2)
        print(f"  非重叠滑窗: {len(w2)}个窗口, 预测{ws-2}段/窗口")

def test_dataset_calculation():
    """测试数据集大小计算"""
    
    print(f"\n{'='*60}")
    print("数据集大小计算测试")
    print(f"{'='*60}")
    
    # 模拟参数
    num_files = 100        # 100个.mat文件
    num_segments = 11      # 每个文件11个段
    window_size = 5        # 窗口大小5
    
    print(f"测试参数:")
    print(f"  文件数量: {num_files}")
    print(f"  每文件段数: {num_segments}")
    print(f"  窗口大小: {window_size}")
    
    for stride_name, stride in [("重叠滑窗", 1), ("非重叠滑窗", window_size-2)]:
        windows_per_file = len(simulate_sliding_window(num_segments, window_size, stride))
        total_samples = num_files * windows_per_file
        
        print(f"\n{stride_name} (stride={stride}):")
        print(f"  每文件窗口数: {windows_per_file}")
        print(f"  总训练样本数: {total_samples}")
        print(f"  样本增长倍数: {windows_per_file}x")

if __name__ == "__main__":
    test_sliding_window_strategies()
    test_dataset_calculation()
    
    print(f"\n{'='*60}")
    print("测试完成！")
    print(f"{'='*60}")
    
    print(f"\n使用建议:")
    print(f"1. 重叠滑窗 (stride=1):")
    print(f"   - 适合: 数据量少，需要更多训练样本")
    print(f"   - 优点: 训练样本多，模型泛化能力强")
    print(f"   - 缺点: 计算开销大，可能过拟合")
    
    print(f"\n2. 非重叠滑窗 (stride=window_size-2):")
    print(f"   - 适合: 数据量大，追求训练效率")
    print(f"   - 优点: 计算效率高，避免数据泄露")
    print(f"   - 缺点: 训练样本少，可能欠拟合")
    
    print(f"\n3. 自定义步长:")
    print(f"   - 可以设置任意步长值")
    print(f"   - 在重叠和非重叠之间找平衡")
    print(f"   - 例如: stride=2 (中等重叠)")
