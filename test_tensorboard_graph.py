#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TensorBoard网络架构图功能
"""

import torch
import torch.nn as nn
from torch.utils.tensorboard import SummaryWriter
import os
import shutil

from botda_model import HybridUNetTCN

def test_tensorboard_graph():
    """测试TensorBoard网络架构图"""
    
    print("测试TensorBoard网络架构图功能...")
    
    # 清理旧的测试日志
    test_log_dir = "./test_graph_logs"
    if os.path.exists(test_log_dir):
        shutil.rmtree(test_log_dir)
    os.makedirs(test_log_dir, exist_ok=True)
    
    # 创建TensorBoard writer
    writer = SummaryWriter(log_dir=test_log_dir)
    
    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    try:
        # 创建模型
        print("创建模型...")
        model = HybridUNetTCN(
            input_freq_channels=3,
            window_segments=5,
            time_points_per_segment=400,
            output_segments=4,
            space_points_per_segment=200,
            base_channels=64,
            depth=4,
            bottleneck_attn_heads=8
        ).to(device)
        
        print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 创建示例输入
        dummy_input = torch.randn(1, 3, 5 * 400).to(device)  # (batch, channels, length)
        print(f"示例输入形状: {dummy_input.shape}")
        
        # 测试前向传播
        model.eval()
        with torch.no_grad():
            dummy_output = model(dummy_input)
            print(f"示例输出形状: {dummy_output.shape}")
        
        # 添加网络架构图
        print("添加网络架构图到TensorBoard...")
        # 使用CPU进行图追踪（避免CUDA兼容性问题）
        model_cpu = model.cpu()
        dummy_input_cpu = dummy_input.cpu()
        writer.add_graph(model_cpu, dummy_input_cpu)
        writer.flush()
        
        print("✅ 网络架构图已成功添加到TensorBoard")
        print(f"TensorBoard日志目录: {test_log_dir}")
        print(f"启动命令: tensorboard --logdir={test_log_dir}")
        
        # 添加一些示例数据
        for i in range(10):
            writer.add_scalar('Test/Loss', 1.0 / (i + 1), i)
        
        writer.close()
        
        # 检查生成的文件
        log_files = os.listdir(test_log_dir)
        print(f"生成的日志文件: {log_files}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        if 'writer' in locals():
            writer.close()

if __name__ == "__main__":
    success = test_tensorboard_graph()
    if success:
        print("\n🎉 测试成功！请启动TensorBoard查看网络架构图。")
    else:
        print("\n❌ 测试失败，请检查错误信息。")
