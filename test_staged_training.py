#!/usr/bin/env python3
# ---------------------------------------------------------------
#  分阶段训练策略测试脚本
#  
#  功能：
#  1. 验证动态权重调整机制
#  2. 测试分阶段训练策略
#  3. 模拟完整的训练流程
# ---------------------------------------------------------------

import torch
import torch.nn as nn
import torch.nn.functional as F
from wavelet_loss import MultiScaleWaveletLoss, FrequencyDomainLoss

def test_staged_training():
    """测试分阶段训练策略"""
    print("🧪 测试分阶段训练策略...")
    
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 初始化小波损失函数
    try:
        wavelet_criterion = MultiScaleWaveletLoss(
            wave='db4',
            level=3,
            loss_type='l1',
            base_weight=0.05,  # 初始权重
            decay=0.6,
            include_lowpass=False
        ).to(device)
        print("✅ 小波损失初始化成功")
    except Exception as e:
        print(f"⚠️  使用FFT替代方案: {e}")
        wavelet_criterion = FrequencyDomainLoss(
            weight=0.05,
            high_freq_boost=3.0,
            loss_type='l1'
        ).to(device)
    
    # 创建测试数据
    B, C, L = 4, 4, 200
    pred = torch.randn(B, C, L, device=device)
    target = torch.randn(B, C, L, device=device)
    
    # 定义损失函数
    criterion = nn.MSELoss()
    
    def smoothness_loss(y, lam):
        diff = y[..., 1:] - y[..., :-1]
        return lam * (diff**2).mean()
    
    def gradient_loss(y_pred, y_true, alpha):
        pred_grad = y_pred[..., 1:] - y_pred[..., :-1]
        true_grad = y_true[..., 1:] - y_true[..., :-1]
        return alpha * F.mse_loss(pred_grad, true_grad)
    
    # 模拟分阶段训练
    MAX_EPOCHS = 100
    stage_transition_epoch = 50
    
    print(f"\n📊 模拟 {MAX_EPOCHS} 个epoch的分阶段训练:")
    print("="*60)
    
    for epoch in range(0, MAX_EPOCHS, 10):  # 每10个epoch测试一次
        print(f"\nEpoch {epoch+1}/{MAX_EPOCHS}")
        
        # 分阶段权重策略
        if epoch < stage_transition_epoch:
            # 阶段一: 学习宏观结构
            grad_alpha = 0.5
            smooth_lambda = 2e-4
            wave_base_weight = 0.01
            stage_name = "宏观结构学习"
        else:
            # 阶段二: 精雕细节
            grad_alpha = 1.5
            smooth_lambda = 1e-4
            wave_base_weight = 0.15
            stage_name = "细节精雕"
        
        # 更新小波损失权重
        if hasattr(wavelet_criterion, 'update_weight'):
            wavelet_criterion.update_weight(wave_base_weight)
        
        print(f"🎯 阶段: {stage_name}")
        print(f"   梯度权重: {grad_alpha:.1f}")
        print(f"   平滑权重: {smooth_lambda:.0e}")
        print(f"   小波权重: {wave_base_weight:.3f}")
        
        # 计算各项损失
        main_loss = criterion(pred, target)
        smooth_loss = smoothness_loss(pred, smooth_lambda)
        grad_loss = gradient_loss(pred, target, grad_alpha)
        wave_loss = wavelet_criterion(pred, target)
        
        total_loss = main_loss + smooth_loss + grad_loss + wave_loss
        
        print(f"📈 损失分解:")
        print(f"   主损失: {main_loss.item():.6f}")
        print(f"   平滑损失: {smooth_loss.item():.6f}")
        print(f"   梯度损失: {grad_loss.item():.6f}")
        print(f"   小波损失: {wave_loss.item():.6f}")
        print(f"   总损失: {total_loss.item():.6f}")
        
        # 分析权重贡献
        total_val = total_loss.item()
        if total_val > 0:
            main_pct = main_loss.item() / total_val * 100
            smooth_pct = smooth_loss.item() / total_val * 100
            grad_pct = grad_loss.item() / total_val * 100
            wave_pct = wave_loss.item() / total_val * 100
            
            print(f"📊 权重贡献:")
            print(f"   主损失: {main_pct:.1f}%")
            print(f"   平滑损失: {smooth_pct:.1f}%")
            print(f"   梯度损失: {grad_pct:.1f}%")
            print(f"   小波损失: {wave_pct:.1f}%")
    
    print("\n" + "="*60)
    print("🎉 分阶段训练策略测试完成！")
    
    # 验证权重变化的合理性
    print("\n🔍 策略分析:")
    print("✅ 阶段一 (宏观结构学习):")
    print("   - 较低的梯度和小波权重，避免过早关注细节")
    print("   - 标准的平滑权重，保持基本的正则化")
    print("   - 主要让模型学习信号的整体形状和位置")
    
    print("\n✅ 阶段二 (细节精雕):")
    print("   - 提高梯度和小波权重，强化细节学习")
    print("   - 降低平滑权重，给细节重建让路")
    print("   - 在宏观结构正确的基础上精雕细琢")
    
    print("\n🎯 预期效果:")
    print("   - 更稳定的训练过程")
    print("   - 更好的细节保持能力")
    print("   - 减少训练早期的振荡")

def test_weight_update():
    """测试权重更新功能"""
    print("\n🧪 测试小波损失权重更新功能...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        # 创建小波损失函数
        wavelet_loss = MultiScaleWaveletLoss(
            wave='db4',
            level=3,
            base_weight=0.05,
            decay=0.6
        ).to(device)
        
        print(f"初始权重: {[f'{w:.4f}' for w in wavelet_loss.wl]}")
        
        # 测试权重更新
        new_weights = [0.01, 0.1, 0.2]
        for new_weight in new_weights:
            wavelet_loss.update_weight(new_weight)
            print(f"更新后权重: {[f'{w:.4f}' for w in wavelet_loss.wl]}")
        
        print("✅ 权重更新功能正常")
        
    except Exception as e:
        print(f"⚠️  权重更新测试跳过: {e}")

if __name__ == '__main__':
    test_staged_training()
    test_weight_update()
