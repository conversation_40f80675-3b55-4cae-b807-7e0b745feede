# ---------------------------------------------------------------
#  小波变换感知损失函数
#  
#  功能：
#  1. 解决MSE"和稀泥"问题，强化高频细节保持
#  2. 多尺度小波分解，重点关注高频分量
#  3. 适用于BOTDA-BFS信号的复杂变化模式
#  
#  原理：
#  - 使用离散小波变换(DWT)分解信号到多个尺度
#  - 对各尺度高频系数计算L1/L2距离
#  - 权重随尺度递减，让细节层权重更大
# ---------------------------------------------------------------

import torch
import torch.nn as nn
import traceback  # 用于打印详细的错误堆栈

# 🔧 最稳的pytorch_wavelets导入方案 - 支持多版本兼容
try:
    # 方案1：尝试从顶层导入（适用于新版本）
    from pytorch_wavelets import DWT1DForward
    WAVELETS_AVAILABLE = True
    print("✅ pytorch_wavelets 模块导入成功 (顶层导入)")
except ImportError:
    try:
        # 方案2：尝试从子模块导入（适用于旧版本）
        from pytorch_wavelets.dwt import DWT1DForward
        WAVELETS_AVAILABLE = True
        print("✅ pytorch_wavelets 模块导入成功 (子模块导入)")
    except ImportError as e:
        print("⚠️  警告: pytorch_wavelets 模块未找到")
        print("   可能的原因:")
        print("   1) 包未安装 - 运行: pip install pytorch_wavelets==1.3.0")
        print("   2) 版本过旧 - 运行: pip install git+https://github.com/fbcotter/pytorch_wavelets.git@master")
        print("   3) 环境不一致 - 确认python和pip使用同一环境")
        print("   4) 文件名冲突 - 检查项目目录是否有同名文件")
        print("   将使用FFT频域损失替代方案")
        WAVELETS_AVAILABLE = False
    except Exception as e:
        print("\n" + "="*60)
        print("❌ pytorch_wavelets 包存在但导入DWT1DForward失败")
        print(f"   错误类型: {type(e).__name__}")
        print(f"   错误信息: {e}")
        print("\n--- 详细错误追溯 ---")
        traceback.print_exc()
        print("="*60)
        print("   建议解决方案:")
        print("   1) 更新到最新版本: pip install git+https://github.com/fbcotter/pytorch_wavelets.git@master")
        print("   2) 或使用FFT替代方案（已自动启用）")
        print("="*60 + "\n")
        WAVELETS_AVAILABLE = False


class MultiScaleWaveletLoss(nn.Module):
    """
    多尺度 1-D 小波感知损失:
        - 对每个输出通道 (B,C,L) 逐通道做 DWT 分解
        - 只计算各尺度高频 (Yh) 系数的 L1/L2 距离
        - 权重随尺度可调, 让更细的 (高频) 权重大
    """
    def __init__(self,
                 wave='db4',          # 小波基: 'db1'/'haar'/'db4'/'sym5'/...
                 level=3,             # 分解层数 J (对200点建议用3层)
                 loss_type='l1',      # 'l1' 或 'l2'
                 base_weight=0.05,    # 最细层权重 (控制整体强度)
                 decay=0.6,           # 每向上一级权重乘 decay
                 include_lowpass=False,  # 是否把最粗层 Yl 也纳入
                 lp_weight=0.2):      # Yl 权重
        super().__init__()
        
        if not WAVELETS_AVAILABLE:
            print("🔄 使用FFT频域损失替代小波损失")
            self.use_fft_fallback = True
            self.level = level
            self.loss_fn = nn.L1Loss() if loss_type.lower() == 'l1' else nn.MSELoss()
            self.base_weight = base_weight
            return
        
        self.use_fft_fallback = False
        self.dwt = DWT1DForward(J=level, mode='zero', wave=wave)
        self.level = level
        self.loss_fn = nn.L1Loss() if loss_type.lower() == 'l1' else nn.MSELoss()
        
        # 构建各层权重: 第 0 层 = 最细 (高频最高)
        self.register_buffer('wl', torch.tensor([
            base_weight * (decay ** j) for j in range(level)
        ]).float())
        
        self.include_lowpass = include_lowpass
        self.lp_weight = lp_weight
        
        print(f"✅ 小波损失初始化: {wave}, {level}层, 权重={[f'{w:.4f}' for w in self.wl]}")

    def fft_fallback_loss(self, pred, target):
        """
        FFT频域损失替代方案
        重点关注高频分量
        """
        # FFT变换
        pred_fft = torch.fft.rfft(pred, dim=-1)
        target_fft = torch.fft.rfft(target, dim=-1)
        
        # 计算频率权重：高频权重更大
        freqs = torch.fft.rfftfreq(pred.size(-1), device=pred.device)
        # 权重随频率增加：低频权重小，高频权重大
        freq_weights = 1.0 + 3.0 * freqs  # 高频权重是低频的4倍
        
        # 计算加权频域损失
        fft_loss = self.loss_fn(
            pred_fft * freq_weights.unsqueeze(0).unsqueeze(0),
            target_fft * freq_weights.unsqueeze(0).unsqueeze(0)
        )
        
        return self.base_weight * fft_loss

    def forward(self, pred, target):
        """
        pred / target: (B, C, L)  — 你的 BFS 预测输出
        返回一个可梯度回传的标量 loss
        """
        if self.use_fft_fallback:
            return self.fft_fallback_loss(pred, target)
        
        # DWT 返回: Yl (B,C,L/2**J)  +  Yh = [ (B,C,L/2), (B,C,L/4), ... ]
        Yl_p, Yh_p = self.dwt(pred)
        Yl_t, Yh_t = self.dwt(target)

        loss = 0.0
        for j in range(self.level):             # j = 0..J-1
            loss += self.wl[j] * self.loss_fn(Yh_p[j], Yh_t[j])

        if self.include_lowpass:
            loss += self.lp_weight * self.loss_fn(Yl_p, Yl_t)

        return loss


class FrequencyDomainLoss(nn.Module):
    """
    纯FFT频域损失 - 作为小波损失的轻量级替代
    重点惩罚高频分量的缺失
    """
    def __init__(self, 
                 weight=0.05,         # 整体权重
                 high_freq_boost=3.0, # 高频增强倍数
                 loss_type='l1'):     # 'l1' 或 'l2'
        super().__init__()
        self.weight = weight
        self.high_freq_boost = high_freq_boost
        self.loss_fn = nn.L1Loss() if loss_type.lower() == 'l1' else nn.MSELoss()
        
        print(f"✅ 频域损失初始化: 权重={weight}, 高频增强={high_freq_boost}x")

    def forward(self, pred, target):
        """
        pred / target: (B, C, L)
        """
        # FFT变换
        pred_fft = torch.fft.rfft(pred, dim=-1)
        target_fft = torch.fft.rfft(target, dim=-1)
        
        # 计算频率权重：高频权重更大
        freqs = torch.fft.rfftfreq(pred.size(-1), device=pred.device)
        freq_weights = 1.0 + self.high_freq_boost * freqs
        
        # 计算加权频域损失
        weighted_pred = pred_fft * freq_weights.unsqueeze(0).unsqueeze(0)
        weighted_target = target_fft * freq_weights.unsqueeze(0).unsqueeze(0)
        
        fft_loss = self.loss_fn(weighted_pred, weighted_target)
        
        return self.weight * fft_loss


# 测试函数
if __name__ == '__main__':
    print("🧪 测试小波损失函数...")
    
    # 创建测试数据
    B, C, L = 16, 4, 200  # 批次=4, 通道=4, 长度=200 (对应你的BFS数据)
    pred = torch.randn(B, C, L)
    target = torch.randn(B, C, L)
    
    # 测试小波损失
    if WAVELETS_AVAILABLE:
        wave_loss = MultiScaleWaveletLoss(
            wave='db4',
            level=3,
            loss_type='l1',
            base_weight=0.05,
            decay=0.6
        )
        w_loss = wave_loss(pred, target)
        print(f"✅ 小波损失: {w_loss.item():.6f}")
    
    # 测试频域损失
    freq_loss = FrequencyDomainLoss(weight=0.05, high_freq_boost=3.0)
    f_loss = freq_loss(pred, target)
    print(f"✅ 频域损失: {f_loss.item():.6f}")
    
    print("🎉 测试完成！")
