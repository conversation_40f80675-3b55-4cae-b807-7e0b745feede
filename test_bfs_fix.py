#!/usr/bin/env python3
"""
测试BFS统计信息修复
"""

import scipy.io as sio
import numpy as np

def test_bfs_stats():
    """测试BFS统计信息加载"""
    stats_file = "./BOTDA_Dataset/bfs_stats.mat"
    
    try:
        stats_data = sio.loadmat(stats_file)
        print(f"BFS统计文件键: {list(stats_data.keys())}")

        # 修复：正确的访问方式
        if 'stats' in stats_data:
            # 新的数据结构：stats包含一个元组(min_array, max_array)
            stats = stats_data['stats'][0][0]  # 获取第一个元素
            print(f"stats内容: {stats}")
            print(f"stats类型: {type(stats)}")
            print(f"stats长度: {len(stats)}")
            
            bfs_min = float(stats[0][0][0])  # 第一个数组是min
            bfs_max = float(stats[1][0][0])  # 第二个数组是max
            
            print(f"✅ 成功加载BFS统计信息: min={bfs_min:.2f} MHz, max={bfs_max:.2f} MHz")
            return bfs_min, bfs_max
        else:
            print("❌ 无法找到stats键")
            return -40.0, 50.0
            
    except Exception as e:
        print(f"❌ 加载失败: {e}")
        import traceback
        traceback.print_exc()
        return -40.0, 50.0

if __name__ == "__main__":
    test_bfs_stats()
