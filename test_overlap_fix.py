#!/usr/bin/env python3
"""
测试重叠处理修复
验证新的重叠处理逻辑是否正确
"""

def simulate_test_data():
    """
    模拟测试数据：3个文件，每个文件9段，窗口大小5
    """
    # 模拟参数
    num_files = 3
    num_segments = 10  # 改为10段以产生向前扩展窗口
    window_size = 5
    space_points_per_segment = 10  # 简化：每段10个点

    # 计算窗口信息
    stride = window_size - 1  # 4
    standard_windows = 0
    pos = 0
    while pos + window_size <= num_segments:
        standard_windows += 1
        pos += stride

    # 检查是否需要向前扩展
    last_segment_idx = num_segments - 1
    has_extra_window = False
    if standard_windows > 0:
        last_window_start = (standard_windows - 1) * stride
        last_window_end = last_window_start + window_size - 1

        if last_segment_idx > last_window_end:
            has_extra_window = True

    windows_per_sample = standard_windows + (1 if has_extra_window else 0)

    print(f"模拟数据参数:")
    print(f"  文件数: {num_files}")
    print(f"  每文件段数: {num_segments}")
    print(f"  窗口大小: {window_size}")
    print(f"  标准窗口数: {standard_windows}")
    print(f"  是否有向前扩展: {has_extra_window}")
    print(f"  每文件窗口数: {windows_per_sample}")

    # 生成模拟预测数据（使用列表代替numpy）
    total_windows = num_files * windows_per_sample
    predictions = []
    targets = []

    for w in range(total_windows):
        window_pred = []
        window_target = []
        for s in range(4):  # 4段
            segment_pred = [1.0] * space_points_per_segment
            segment_target = [1.0] * space_points_per_segment
            window_pred.append(segment_pred)
            window_target.append(segment_target)
        predictions.append(window_pred)
        targets.append(window_target)
    
    # 为了便于观察重叠处理效果，给重叠段设置特殊值
    if has_extra_window:
        for file_idx in range(num_files):
            file_start_idx = file_idx * windows_per_sample

            # 最后标准窗口和向前扩展窗口
            last_standard_idx = file_start_idx + standard_windows - 1
            extra_window_idx = file_start_idx + standard_windows

            # 设置重叠段的值便于验证
            # 假设段5,6,7重叠（根据9段数据的计算）
            overlap_in_standard = [1, 2, 3]  # 在最后标准窗口中的位置
            overlap_in_extra = [0, 1, 2]     # 在向前扩展窗口中的位置

            for i, (std_pos, extra_pos) in enumerate(zip(overlap_in_standard, overlap_in_extra)):
                # 设置不同的值以便观察平均效果
                val1 = file_idx * 10 + i + 1  # 如：11, 12, 13
                val2 = file_idx * 10 + i + 5  # 如：15, 16, 17

                for j in range(space_points_per_segment):
                    predictions[last_standard_idx][std_pos][j] = val1
                    predictions[extra_window_idx][extra_pos][j] = val2
                    targets[last_standard_idx][std_pos][j] = val1
                    targets[extra_window_idx][extra_pos][j] = val2
    
    return predictions, targets, {
        'num_files': num_files,
        'windows_per_sample': windows_per_sample,
        'window_size': window_size,
        'num_segments': num_segments,
        'has_extra_window': has_extra_window,
        'standard_windows': standard_windows
    }

def handle_overlapping_predictions_test(predictions, targets, info):
    """
    测试版本的重叠处理函数
    """
    print("\n处理重叠预测...")
    
    num_files = info['num_files']
    windows_per_sample = info['windows_per_sample']
    window_size = info['window_size']
    num_segments = info['num_segments']
    has_extra_window = info['has_extra_window']
    standard_windows = info['standard_windows']
    
    if not has_extra_window:
        print("无重叠段，直接返回原始结果")
        return predictions, targets
    
    # 计算重叠段
    stride = window_size - 1
    last_window_start = (standard_windows - 1) * stride
    extra_window_start = num_segments - window_size
    
    last_standard_predicted = list(range(last_window_start, last_window_start + 4))
    extra_window_predicted = list(range(extra_window_start, extra_window_start + 4))
    overlap_segments = list(set(last_standard_predicted) & set(extra_window_predicted))
    
    print(f"最后标准窗口预测段: {last_standard_predicted}")
    print(f"向前扩展窗口预测段: {extra_window_predicted}")
    print(f"重叠段: {overlap_segments}")
    
    # 处理重叠预测
    processed_predictions = []
    processed_targets = []

    for file_idx in range(num_files):
        file_start_idx = file_idx * windows_per_sample
        file_end_idx = file_start_idx + windows_per_sample

        # 深拷贝文件数据
        file_predictions = []
        file_targets = []
        for i in range(file_start_idx, file_end_idx):
            window_pred = []
            window_target = []
            for j in range(4):
                segment_pred = predictions[i][j][:]  # 复制列表
                segment_target = targets[i][j][:]
                window_pred.append(segment_pred)
                window_target.append(segment_target)
            file_predictions.append(window_pred)
            file_targets.append(window_target)
        
        print(f"\n处理文件{file_idx}:")
        print(f"  窗口索引范围: {file_start_idx}-{file_end_idx-1}")
        
        if windows_per_sample == standard_windows + 1:  # 有向前扩展窗口
            # 最后两个窗口需要处理重叠
            last_standard_idx = standard_windows - 1
            extra_window_idx = standard_windows
            
            print(f"  最后标准窗口索引: {last_standard_idx}")
            print(f"  向前扩展窗口索引: {extra_window_idx}")
            
            # 对重叠段取平均值
            for seg_idx in overlap_segments:
                # 计算在标准窗口和扩展窗口中的相对位置
                std_relative_idx = seg_idx - last_window_start
                extra_relative_idx = seg_idx - extra_window_start
                
                if 0 <= std_relative_idx < 4 and 0 <= extra_relative_idx < 4:
                    # 显示处理前的值
                    old_std_val = file_predictions[last_standard_idx][std_relative_idx][0]
                    old_extra_val = file_predictions[extra_window_idx][extra_relative_idx][0]

                    # 取平均值
                    for point_idx in range(len(file_predictions[last_standard_idx][std_relative_idx])):
                        avg_pred = (file_predictions[last_standard_idx][std_relative_idx][point_idx] +
                                   file_predictions[extra_window_idx][extra_relative_idx][point_idx]) / 2
                        avg_target = (file_targets[last_standard_idx][std_relative_idx][point_idx] +
                                     file_targets[extra_window_idx][extra_relative_idx][point_idx]) / 2

                        # 更新两个窗口的值
                        file_predictions[last_standard_idx][std_relative_idx][point_idx] = avg_pred
                        file_predictions[extra_window_idx][extra_relative_idx][point_idx] = avg_pred
                        file_targets[last_standard_idx][std_relative_idx][point_idx] = avg_target
                        file_targets[extra_window_idx][extra_relative_idx][point_idx] = avg_target

                    new_val = file_predictions[last_standard_idx][std_relative_idx][0]
                    print(f"    段{seg_idx}: {old_std_val:.1f} + {old_extra_val:.1f} → {new_val:.1f}")
        
        processed_predictions.append(file_predictions)
        processed_targets.append(file_targets)
    
    # 重新组合所有文件的结果
    final_predictions = []
    final_targets = []
    for file_pred, file_target in zip(processed_predictions, processed_targets):
        final_predictions.extend(file_pred)
        final_targets.extend(file_target)

    print(f"\n重叠处理完成: {len(predictions)} -> {len(final_predictions)} 窗口")
    return final_predictions, final_targets

def test_overlap_handling():
    """
    测试重叠处理
    """
    print("=== 重叠处理测试 ===")
    
    # 生成模拟数据
    predictions, targets, info = simulate_test_data()
    
    print(f"\n原始数据窗口数: {len(predictions)}")

    # 显示处理前的重叠段值
    if info['has_extra_window']:
        print(f"\n处理前重叠段值:")
        for file_idx in range(info['num_files']):
            file_start = file_idx * info['windows_per_sample']
            last_std_idx = file_start + info['standard_windows'] - 1
            extra_idx = file_start + info['standard_windows']

            print(f"  文件{file_idx}:")
            vals1 = [predictions[last_std_idx][i][0] for i in [1,2,3]]
            vals2 = [predictions[extra_idx][i][0] for i in [0,1,2]]
            print(f"    最后标准窗口[1,2,3]: {vals1}")
            print(f"    向前扩展窗口[0,1,2]: {vals2}")

    # 处理重叠
    processed_pred, processed_target = handle_overlapping_predictions_test(
        predictions, targets, info
    )

    # 显示处理后的结果
    if info['has_extra_window']:
        print(f"\n处理后重叠段值:")
        for file_idx in range(info['num_files']):
            file_start = file_idx * info['windows_per_sample']
            last_std_idx = file_start + info['standard_windows'] - 1
            extra_idx = file_start + info['standard_windows']

            print(f"  文件{file_idx}:")
            vals1 = [processed_pred[last_std_idx][i][0] for i in [1,2,3]]
            vals2 = [processed_pred[extra_idx][i][0] for i in [0,1,2]]
            print(f"    最后标准窗口[1,2,3]: {vals1}")
            print(f"    向前扩展窗口[0,1,2]: {vals2}")

    print(f"\n最终数据窗口数: {len(processed_pred)}")
    print("测试完成！")

if __name__ == "__main__":
    test_overlap_handling()
