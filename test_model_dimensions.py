#!/usr/bin/env python3
"""
测试HybridUNetTCN模型的维度匹配
验证新的滑窗策略和输入输出维度
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append('.')

try:
    import torch
    import torch.nn as nn
    import numpy as np
    from botda_model import HybridUNetTCN
    
    print("=== HybridUNetTCN模型维度测试 ===\n")
    
    # 模型参数（基于BOTDA仿真设置）
    WINDOW_SIZE = 5
    TIME_POINTS_PER_SEGMENT = 400  # 200ns / 0.5ns
    SPACE_POINTS_PER_SEGMENT = 200  # 20m / 0.1m
    FREQ_CHANNELS = 3
    OUTPUT_SEGMENTS = WINDOW_SIZE - 1  # 4段
    
    print("模型配置参数:")
    print(f"  窗口大小: {WINDOW_SIZE}段")
    print(f"  输出段数: {OUTPUT_SEGMENTS}段")
    print(f"  每段时间点数: {TIME_POINTS_PER_SEGMENT}")
    print(f"  每段空间点数: {SPACE_POINTS_PER_SEGMENT}")
    print(f"  频率通道数: {FREQ_CHANNELS}")
    
    # 计算输入输出维度
    input_length = WINDOW_SIZE * TIME_POINTS_PER_SEGMENT
    output_length = OUTPUT_SEGMENTS * SPACE_POINTS_PER_SEGMENT
    
    print(f"\n计算的维度:")
    print(f"  输入维度: ({FREQ_CHANNELS}, {input_length})")
    print(f"  输出维度: (1, {output_length})")
    print(f"  输入/输出比例: {input_length/output_length:.2f}")
    
    # 创建模型
    print(f"\n创建HybridUNetTCN模型...")
    model = HybridUNetTCN(
        input_freq_channels=FREQ_CHANNELS,
        window_segments=WINDOW_SIZE,
        time_points_per_segment=TIME_POINTS_PER_SEGMENT,
        output_segments=OUTPUT_SEGMENTS,
        space_points_per_segment=SPACE_POINTS_PER_SEGMENT,
        base_channels=64,
        depth=4,
        bottleneck_attn_heads=8
    )
    
    # 计算模型参数
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"模型参数总数: {total_params:,}")
    print(f"可训练参数: {trainable_params:,}")
    
    # 创建测试输入
    batch_size = 4
    test_input = torch.randn(batch_size, FREQ_CHANNELS, input_length)
    print(f"\n测试输入形状: {test_input.shape}")
    
    # 前向传播测试
    print("执行前向传播...")
    model.eval()
    with torch.no_grad():
        output = model(test_input)
    
    print(f"输出形状: {output.shape}")
    
    # 验证维度
    expected_shape = (batch_size, 1, output_length)
    if output.shape == expected_shape:
        print(f"✅ 维度匹配成功！")
        print(f"   期望: {expected_shape}")
        print(f"   实际: {output.shape}")
    else:
        print(f"❌ 维度不匹配！")
        print(f"   期望: {expected_shape}")
        print(f"   实际: {output.shape}")
    
    # 测试不同的批次大小
    print(f"\n测试不同批次大小:")
    for bs in [1, 2, 8, 16]:
        test_input_bs = torch.randn(bs, FREQ_CHANNELS, input_length)
        with torch.no_grad():
            output_bs = model(test_input_bs)
        expected_bs = (bs, 1, output_length)
        status = "✅" if output_bs.shape == expected_bs else "❌"
        print(f"  批次大小 {bs}: {output_bs.shape} {status}")
    
    print(f"\n=== 新滑窗策略说明 ===")
    print(f"输入: 第1,2,3,4,5段的斯托克斯信号拼接")
    print(f"输出: 只预测前4段的BFS分布（第1,2,3,4段）")
    print(f"步长: window_size-1={WINDOW_SIZE-1}（非重叠滑窗）")
    print(f"原因: 第5段可能受第6段开头频移影响，不预测")
    
    print(f"\n模型测试完成！")
    
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保已安装PyTorch")
except Exception as e:
    print(f"测试过程中出现错误: {e}")
    import traceback
    traceback.print_exc()
