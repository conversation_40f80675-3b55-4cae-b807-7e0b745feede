%% 分布式布里渊光纤传感仿真（二阶声场）
clc
clear
close all


% 获取当前系统时间
currentTime = datetime('now', 'Format', 'HH:mm:ss');
% 显示当前时间在命令行中
disp(['程序启动时间: ', char(currentTime)]);


format long eng; %输出格式为长格式（小数点后15位），并以科学计数法显示
rng('shuffle'); %根据当前时间设置随机数种子


% 获取当前并行池
pool = gcp('nocreate');
% 检查并行池状态
if isempty(pool)
    disp('并行池未开启。尝试启动新的并行池...');
    % 如果没有并行池，启动一个新的并行池
    parpool('IdleTimeout', 120); % 设置空闲超时时间为 120 分钟
    disp('新的并行池已启动。');
else
    % 如果已存在并行池，检查其状态
    disp('并行池已开启。');
    disp(['并行池大小: ', num2str(pool.NumWorkers), ' 个工作者']);
    disp(['当前空闲超时时间: ', num2str(pool.IdleTimeout), ' 分钟']);
    
    % 确保其空闲超时时间为 120 分钟
    if pool.IdleTimeout ~= 120
        pool.IdleTimeout = 120;
        disp('空闲超时时间已重新设置为 120 分钟。');
    end
end

tic %计时器，开始计时


%% 控制开关
% 损耗项
Loss_item = 1;

% 动画开关
anim_active = 0;      % 1 = produce animation, 0 = skip animation
title1 = 'SBS_solver_animation_noise.mp4';   % Animation title

%% 参数设置
% 物理常数
c = 3e8;                      % 光速 (m/s)
n = 1.5;                      % 光纤折射率
epsilon0 = 8.85e-12;          % 真空介电常数 (F/m)
kB = 1.38e-23;                % 玻尔兹曼常数
Temperature = 300;            % 波导温度 (K)
va = 5960;                    % 光纤中的声速 (m/s)
vc = c/n;                     % 光纤中的光速
nf = 1.5;                     % 光纤模式的模态指数
eta = c*epsilon0*nf/2;

% 光纤参数
L = 40;                       % 光纤长度 (m)
Fiber_Aeff = 50e-12;          % 有效模场面积 (m^2)

% 是否有损耗
switch Loss_item
    case 0
        loss = 0;
        alpha = 0;
    case 1 
        loss = 0.2;           % 1550nm波长在光纤中的损耗 (dB/km)
        alpha_power_per_m = loss * log(10) / (10 * 1000); % 功率衰减系数，单位：Np/m
        alpha = alpha_power_per_m / 2; % 幅度衰减系数，单位：Np/m
        % 推导的公式中应该用的是功率衰减系数，因为公式中除以了2，说明是想把转化为幅度衰减系数；
        % 也可以直接乘以exp(-alpha*dx)，因为指数很小的情况下，根据泰勒展开，exp(-alpha*dx)≈1-alpha*dx，即分步法中看到的(1-alpha*dx)A_p其实就是A_p*exp(-alpha*dx)
end


% 仿真参数
SBS_freq_max = 1e9;             % Maximum frequency for SBS response computation [Hz] 自己设定的此系统最大能响应的频率
t_max = 2.5*n*L/c;
Delta_freq = 1/t_max;
Delta_t = 1/(2*SBS_freq_max);
f_2 = -SBS_freq_max:Delta_freq:SBS_freq_max;  % 正负频率轴
f_ang_2 = 2*pi*f_2; % 正负角频率轴
Nf = length(f_ang_2);
t = 0:Delta_t:t_max; 
Nt = length(t);

if (1/Delta_freq)-(1/SBS_freq_max)<(n*L/c)  %传输整根光纤所需的时间
    error('invalid step size combination')
end

dz = c*Delta_t/n;
z = 0:dz:L;
Nz = length(z);


% 布里渊参数
tao_a = 10e-9;                % 声子寿命 (s)
Gamma = 1/tao_a;              % 再除以pi后，才是布里渊线宽35MHz (Hz)
gamma = Gamma/2;
strain_coef = 0.0483e6;       % 应变系数 (Hz/μϵ)
gB = 5e-11;                   % 布里渊峰值增益系数 (m/W)
g0 = gB/Fiber_Aeff;
Omega_B_base = 2*pi*10.8e9;        % 基础布里渊频移 (rad/s)

% 定义多个应变区域及其对应的频移值
% 每一行格式: [起始位置(m), 结束位置(m), 频移值(MHz)]
strain_regions = [
    15, 16, 0;    % 第一个应变区域: 15-25m, 频移10MHz
    22, 25, 0;    % 第二个应变区域: 30-35m, 频移15MHz
    26, 27, 0     % 第三个应变区域: 40-45m, 频移-5MHz(压缩应变)
];

% 定义布里渊频移分布
Omega_B = ones(1, Nz) * Omega_B_base;

% 遍历每个应变区域并应用相应的频移
for i = 1:size(strain_regions, 1)
    region_start = strain_regions(i, 1);
    region_end = strain_regions(i, 2);
    region_shift = strain_regions(i, 3);
    
    % 找到该应变区域对应的索引
    region_indices = find(z >= region_start & z <= region_end);
    
    % 应用频移
    Omega_B(region_indices) = Omega_B_base + 2*pi*region_shift * 1e6;
end


% 脉冲参数 - 泵浦光
Pump_peak_P_0 = 40e-3;           % 泵浦光峰值功率 (W)
% 若要在泵浦探测未相遇前就看到泵浦与噪声作用产生的斯托克斯光，就需要拉大泵浦以增大产生的斯托克斯幅度，减小探测以在探测图中观察到斯托克斯
Pump_peak_E_0 = sqrt(Pump_peak_P_0); % 泵浦光峰值振幅
Pump_ER = 20;                 % 泵浦光消光比 (dB)
Pump_leak_P_0=Pump_peak_P_0*10^(-Pump_ER/10);   % 泵浦光泄露功率 (W)
Pump_leak_P_0 = 0;            % 泵浦光泄露功率 (W)
Pump_leak_E_0=sqrt(Pump_leak_P_0); % 泵浦光泄露振幅
Pump_rise_time = 0.1e-9;      % 泵浦光上升时间 (s)
Pump_a1 = 0.45*Pump_rise_time;
Pump_fall_time = 0.1e-9;      % 泵浦光下降时间 (s)
Pump_a2 = 0.45*Pump_fall_time;
Pump_width = 50e-9;           % 泵浦光脉宽 (s)

t0_p = 0e-9;                 % 泵浦光入射时间 (s)
t1_p=(t-t0_p)/Pump_a1;
t2_p=(t-t0_p-Pump_width)/Pump_a2;
Pump_t=(Pump_peak_E_0-Pump_leak_E_0)*sqrt((tanh(t1_p)-tanh(t2_p))/2)+Pump_leak_E_0; % 泵浦光时域信号

% 脉冲参数 - 探测光
Probe_peak_P_L = 1e-3;         % 探测光峰值功率 (W)
Probe_peak_E_L = sqrt(Probe_peak_P_L); % 探测光峰值振幅
Probe_ER = 20;                % 探测光消光比 (dB)
Probe_leak_P_L=Probe_peak_P_L*10^(-Probe_ER/10);
Probe_leak_P_L = 0;           % 探测光泄露功率 (W)
Probe_leak_E_L = sqrt(Probe_leak_P_L); % 探测光泄露振幅
Probe_rise_time = 0.1e-9;     % 探测光上升时间 (s)
Probe_a1 = 0.45*Probe_rise_time;
Probe_fall_time = 0.1e-9;     % 探测光下降时间 (s)
Probe_a2 = 0.45*Probe_fall_time;
Probe_width = 200e-9;          % 探测光脉宽 (s)   探测脉宽最好是泵浦脉宽的整数倍，这样窗口数量才是自然数

t0_s = 0e-9;                % 探测光入射时间 (s)
t1_s=(t-t0_s)/Probe_a1;
t2_s=(t-t0_s-Probe_width)/Probe_a2;
Probe_t=(Probe_peak_E_L-Probe_leak_E_L)*sqrt((tanh(t1_s)-tanh(t2_s))/2)+Probe_leak_E_L; % 探测光时域信号


% 扫频参数  扫频范围为140MHz，在10.8GHz左边70MHz，右边70MHz，扫频步长为5MHz
Omega_start = 2*pi*10.73e9;        % 起始扫频点 (rad/s)  转化为角频率
Omega_end = 2*pi*10.87e9;          % 结束扫频点 (rad/s)
Omega_step = 2*pi*5e6;             % 扫频步长 (rad/s)
Omega_list = Omega_start:Omega_step:Omega_end;  % 扫频列表
n_sweep = length(Omega_list);


% 结果存储
BGS = zeros(1, length(Omega_list));  % 布里渊增益谱


%% 线性传播预计算
% 计算泵浦脉冲仅线性传播的结果
% 计算探测脉冲仅线性传播（含光纤损耗）到z=0的功率，用于后面得到由于SBS导致的探测光功率变化（增益后功率减去原功率）
disp('线性传播预计算...');

A1_linear = zeros(Nz, Nt);
A1_linear(1,:) = Pump_t; % 设置初始条件

A2_linear = zeros(Nz, Nt);
A2_linear(end,:) = Probe_t; % 设置初始条件

% 执行线性传播计算
for i = 1:Nt-1
    % 线性传播
    A1_linear(2:Nz,i+1) = A1_linear(1:Nz-1,i);
    
    % 考虑损耗
    A1_linear(:,i+1) = A1_linear(:,i+1) * exp(-alpha*dz); % 这里下一个时间点正好对应所有点移动dz的空间距离
    
    % 恢复边界值
    A1_linear(1,i+1) = Pump_t(i+1);

    % 线性传播
    A2_linear(1:Nz-1,i+1) = A2_linear(2:Nz,i);
    
    % 考虑损耗
    A2_linear(:,i+1) = A2_linear(:,i+1) * exp(-alpha*dz); % 这里下一个时间点正好对应所有点移动dz的空间距离
    
    % 恢复边界值
    A2_linear(end,i+1) = Probe_t(i+1);
end

% 提取探测光仅线性传播后在z=0处的时间演化
A2_linear_receiver = A2_linear(1, :);

% 计算线性传播到z=0的功率
power_time_linear = abs(A2_linear_receiver).^2;

disp('线性传播预计算完成');


%% 主循环 - 扫频
% 存储每一次扫频的结果
A1_store_3d = zeros(Nz, Nt, n_sweep);
A2_store_3d = zeros(Nz, Nt, n_sweep);
q_store_3d = zeros(Nz, Nt, n_sweep);

% 用于存放z=0处斯托克斯光/探测光功率随时间的变化
% SBS耦合方程方法
probe_map_SBS = zeros(Nt, n_sweep);
stokes_map_SBS = zeros(Nt, n_sweep);
% 真实响应求和求解方法
probe_map_theory = zeros(Nt, n_sweep);
stokes_map_theory = zeros(Nt, n_sweep);

q_store_theory = zeros(Nz, Nt, n_sweep);  % 存储真实响应方法的声场

h_t_save = cell(1, n_sweep);
% h_t_conv_save = cell(1, n_sweep);

% 找到目标扫频频率索引，用于绘制在此扫频频率下h_t和h_t_conv
target_freq = 2*pi*10.87e9; % 目标频率10.87GHz
[~, target_idx_1] = min(abs(Omega_list - target_freq)); % 找到最接近的频率索引


% 预生成传递函数
H_tab = zeros(Nz,Nt);            % 每个 z 一条 h_t
for j = 1:Nz
    Omega_eff = sqrt(Omega_B(j)^2 - gamma^2); % 使用未化简的g(t)，得到√(Ω_B^2-γ^2 )
    H_tab(j,:) = sqrt(2*pi)/Omega_eff .* ...
                 exp(-gamma*t).*sin(Omega_eff*t); % 此时 H_tab 还没有包含扫频频率 Omega，只包含了布里渊频移 Omega_B，后续在扫频中只需要乘以exp(1i*Omega*t)即可
end


% 使用parfor进行并行计算
parfor freq_idx = 1:n_sweep

    Omega = Omega_list(freq_idx); % 此时的扫频频率差
    expOm = exp(1i*Omega*t);
    fprintf('模拟扫频点 %d/%d: Omega = %.3f GHz\n', freq_idx, length(Omega_list), Omega/(2*pi)/1e9);

    %% ======== 真实响应求和求解方法 ========
    % 使用这个方法的近似条件：
    % ① 由于这种方法没有考虑由于泵浦和探测相互作用而导致幅度的变化，计算声场时仅仅只采用它们线性传播的结果，所以只适用于短脉冲的情况，这种情况下幅度增益和损失较小，可以认为相互作用而导致幅度的变化很小，这样的话计算声场结果就是较准确的

    a_s_theory_contributions_at_z0_time = zeros(Nz, Nt); % 储存每个空间位置Nz产生的布里渊信号再传播到z=0的布里渊时间序列

    % 同样也可以像下面使用BSS耦合方程中的方法，去掉这个空间位置的循环，一次性处理所有空间点，然后用滑动求和来代替conv
    % （但是会引入时间循环，似乎没办法同时去掉时间和空间循环：只要各z-点的核g_ac_iz_kernel_theory彼此不同，就不可能在纯时域里用一次矩阵乘就“同时”完成）
    % 如果每根声学传递函数 g_{iz}(t) 都不完全相同（它和Ω_B(iz) 直接相关），那么 最少必须在"空间”或“时间”里保留一重循环。
    % 这是卷积本身的结构决定的，只要 g_{iz} 随 iz 变化，就不可能用一次 conv2 把 所有 iz都卷完，而既不在空间 loop、又不在时间 loop、还不用频域乘积。
    for iz = 1:Nz
        current_Pump_at_z_iz_global_t  = A1_linear(iz,:); % 泵浦光在iz位置，全局时间t下，随时间的变化
        current_Probe_at_z_iz_global_t = A2_linear(iz,:); % 探测光在iz位置，全局时间t下，随时间的变化

        Omega_eff_iz = sqrt(Omega_B(iz)^2 - gamma^2); % 使用未化简的g(t)，得到√(Ω_B^2-γ^2 )

        g_ac_iz_kernel_theory = (sqrt(2*pi) / Omega_eff_iz) ...
                     .* exp(1i * Omega * t) ... 
                     .* exp(-gamma * t) ...
                     .* sin(Omega_eff_iz * t); % 得到未化简的在位置iz下的g(t)，是一个iz位置处的时间向量
        K_factor_S_Q_theory = -1i * g0 * gamma * Omega_B(iz); % 驱动项S_Q_at_z_iz_global_t_theory的一部分
        S_Q_at_z_iz_global_t_theory = K_factor_S_Q_theory ...
                             .* current_Pump_at_z_iz_global_t ...
                             .* conj(current_Probe_at_z_iz_global_t); % 位置iz处的驱动项，是一个时间向量

        % 位置iz处的声场，也是一个时间向量，仅通过线性传播的泵浦和探测得到，没有考虑探测光增益后和泵浦损耗后的影响
        Q_at_z_iz_global_t_conv_theory = Delta_t * conv(g_ac_iz_kernel_theory, S_Q_at_z_iz_global_t_theory, 'full');
        Q_at_z_iz_global_t_theory = [0, Q_at_z_iz_global_t_conv_theory(1:Nt-1)]; % 其实这个得到的声场就是和SBS耦合方法得到的声场是一样的了，包括形状和幅度
        % 本来应该是Q_at_z_iz_global_t_theory = Q_at_z_iz_global_t_conv_theory(1:Nt)，前面这里补零是为了让真实响应结果和SBS耦合结果对齐，不然真实响应结果会超前一个时间点

        q_store_theory(iz, :, freq_idx) = Q_at_z_iz_global_t_theory; % 存储真实响应方法产生的声场

        % 在位置iz处斯托克斯光的产生速率，即三波耦合方程右侧g_0/2 A_p Q^*这一项
        source_rate_delta_as_at_z_iz_global_t_theory = (g0 / 2) ...
                           .* current_Pump_at_z_iz_global_t ...
                           .* conj(Q_at_z_iz_global_t_theory);

        delta_As_field_generated_at_z_iz_theory = source_rate_delta_as_at_z_iz_global_t_theory * dz; % 在位置iz新产生的斯托克斯光，也是一个时间变量，随时间变化
        % 乘以dz后，可以认为是iz位置处dz这一小段，产生的真实布里渊响应

        shifted_and_attenuated_signal_theory = zeros(1, Nt);
        % 用来存储从当前空间微元段 iz 产生的、并且已经传播（含损耗）到光纤始端 z=0 的斯托克斯光场贡献
        delay_to_receiver_theory = round(z(iz)/vc / Delta_t); % 从当前位置iz传播回到z=0所需要的时间点数
        atten_factor_as_prop_to_z0_theory = exp(-alpha * z(iz)); % 从当前位置iz传播回到z=0所对应的衰减系数

        shifted_and_attenuated_signal_theory(1+delay_to_receiver_theory : Nt) = ... % 因为iz位置处产生的斯托克斯信号还需要delay_to_receiver_theory个时间点数才能回到z=0，所以从1+delay_to_receiver_theory开始才能看到斯托克斯信号
                    delta_As_field_generated_at_z_iz_theory(1 : Nt - delay_to_receiver_theory) * atten_factor_as_prop_to_z0_theory; 
        % delta_As_field_generated_at_z_iz_theory本来是1:end的，但由于delta_As_field_generated_at_z_iz_theory是在iz处的信号，还需要传播回z=0，需要delay_to_receiver_theory个时间点数，
        % 所以肯定就不能取完整个仿真时间，只能取1 : Nt - delay_to_receiver_theory，减掉的delay_to_receiver_theory时间点数用来返回到z=0

        a_s_theory_contributions_at_z0_time(iz, :) = shifted_and_attenuated_signal_theory; 
        % 得到iz位置产生的布里渊信号（是一个时间序列），经过衰减后，在z=0处测量得到的iz位置处产生的布里渊时间信号序列结果

    end % 得到每个空间点产生的真实布里渊时间序列信号再传播回z=0的信号

    % 当前扫频下得到的斯托克斯光在z=0处随着时间的变化情况
    current_total_sbs_field_at_z0_theory = sum(a_s_theory_contributions_at_z0_time, 1); % 把同一时刻回到z=0的不同空间位置产生的布里渊信号叠加起来，得到一个时间行向量

    total_theoretical_A2_at_z0 = A2_linear_receiver + current_total_sbs_field_at_z0_theory; % 得到z=0接收到的总信号幅度（探测脉冲+斯托克斯光）
    total_power_theory = abs(total_theoretical_A2_at_z0).^2; % 得到z=0接收到的总信号功率（探测脉冲+斯托克斯光）

    probe_map_theory(:, freq_idx) = total_power_theory(:); % 得到z=0处的探测光功率   (:)确保列向量
    stokes_map_theory(:, freq_idx) = total_power_theory(:) - power_time_linear(:); 
    % 注意，斯托克斯光的功率是通过总功率-线性传播探测光功率得到的，而不是直接通过斯托克斯光幅度的平方得到的；因为现在幅值是一个复数，两者是有差别的，如果是实数就没有差别


    %% ======== SBS三波耦合方程求解方法 ========
    A1_store = zeros(Nz, Nt);
    A2_store = zeros(Nz, Nt);
    q_store = zeros(Nz, Nt);
    rho = zeros(Nz,Nt); % 确定声场部分的卷积积分

    A1_store(1,:) = Pump_t;
    A2_store(end,:) = Probe_t;
    q_store(:,1) = 0; % 初始时刻所有空间位置上的声场假设都是0

    
    % 时间演化
    for i = 1:Nt-1

        % 线性传播部分  得到i+1时刻线性传播的值   但得到的还不是i+1时刻真正的值，下面先计算声场，再进行非线性作用
        A1_store(2:Nz,i+1) = A1_store(1:Nz-1,i); 
        A2_store(1:Nz-1,i+1) = A2_store(2:Nz,i); 


        % 计算i+1时刻的声场
        % 先计算i+1时刻的卷积积分rho
%         for j = 1:Nz
% 
%             h_t = -1i*sqrt(2*pi)/2*exp(-gamma*t) .* ( exp(1i*(Omega+Omega_B(j))*t) - exp(1i*(Omega-Omega_B(j))*t) ); % 在位置索引为j的传递函数
% 
% %             h_w = fftshift(fft(h_t)) / Nf; % 传递函数的频域表达式
% %  
% %             Pump_w = fftshift(fft(Pump_t)) / Nf; % 当泵浦宽度越宽时，由于能量是守恒的，并且时域变宽，频谱会缩窄，因此会导致Pump_w峰值的增大，进而导致h_w_conv峰值变得很大
% %             Pump_s = abs(Pump_w).^2; % 泵浦功率谱
% % 
% %             % 归一化泵浦功率谱
% %             total_power = sum(Pump_s) * Delta_freq; % 计算总能量
% %             Pump_s_normalized = Pump_s / total_power;  % 积分应该等于1
% %             % --- 验证积分 ---
% %             integral = sum(Pump_s_normalized) * Delta_freq; % 应等于或接近1
% % 
% %             % 频域卷积
% %             h_w_conv = Delta_freq * conv(h_w,Pump_s_normalized,'same'); % 泵浦宽度越宽，h_w_conv峰值越高
% %             % ？？？？？ 这里想像时域卷积那样在conv前面乘以频率间隔Delta_freq，但是发现乘了之后峰值变得很大
% % 
% %             % h(w)和泵浦功率谱卷积后的时域h(t)
% %             h_t_conv = ifft(ifftshift(h_w_conv)) * Nf;
% % 
% %             h_w_conv_full = Delta_freq * conv(h_w,Pump_s_normalized,'full'); % 全卷积结果   和same做一个对比
% 
% %             % 结果绘图
% %             % 对比泵浦功率谱和归一化泵浦功率谱
% %             figure(9)
% %             plot(f_2, Pump_s, 'LineWidth',2);
% %             hold on
% %             plot(f_2, Pump_s_normalized, 'LineWidth',2);
% %             legend('泵浦功率谱', "归一化泵浦功率谱");
% % 
% %             % 对比全卷积和用same取了一半的卷积结果
% %             figure(8)
% %             subplot(2,1,1)
% %             plot(-2*SBS_freq_max:Delta_freq:2*SBS_freq_max, h_w_conv_full, 'LineWidth',2);
% %             legend('h(w) 全卷积结果');
% %             subplot(2,1,2)
% %             plot(f_2, h_w_conv, 'LineWidth',2);
% %             legend('h(w) same卷积结果');
% % 
% %             % 对比与泵浦功率谱卷积后的h(t)与卷积前的h(t)
% %             figure(6)
% %             plot(t*1e9, abs(h_t_conv), 'b', 'LineWidth', 1.5, 'LineWidth',2); 
% %             hold on;
% %             plot(t*1e9, abs(h_t), 'r--', 'LineWidth', 1.5, 'LineWidth',2);
% %             xlabel('时间 t (ns)');
% %             ylabel('h(t)');
% %             legend('卷积后的 h(t)', '原始 h(t)');
% % 
% %             % 对比与泵浦功率谱卷积后的h_w_conv频谱和卷积前的h(w)频谱
% %             figure(7)
% %             plot(f_2,h_w_conv, 'LineWidth',2);
% %             hold on
% %             plot(f_2,h_w, 'LineWidth',2);
% %             legend('卷积后频谱 h\_w\_conv', '原始频谱 h(w)');
% 
% 
%             if freq_idx==target_idx_1 && i==round(Nt/2) && j==round(Nz/2)  % 仅仅只在扫频频率为10.8GHz、光纤中点、仿真时间中点处才绘制h(t)，
%                 % 虽然在仿真时间中点处脉冲可能已经传输完光纤了，但是h(t)和时间无关，这是任选的一个时间点
% 
%                 h_t_save{freq_idx} = h_t;         % 存到 cell 数组第 freq_idx 个单元格中，循环外绘图
% %                 h_t_conv_save{freq_idx} = h_t_conv;
% 
%             end
% 
%             % 计算卷积   得到i+1时刻所有位置的确定积分部分
%             
%             % 确定声场部分
%             Integral = Delta_t * conv(h_t, A1_store(j,:).*conj(A2_store(j,:)));
%             rho(j,i+1) = Integral(i+1); % 取卷积后对应的i+1时刻的值
% 
%         end

        X = A1_store(:,1:i) .* conj(A2_store(:,1:i));      % 截取到当前时刻即可，驱动项   可以从一个空间点来考虑
        H = H_tab(:,1:i) .* expOm(1:i);        % h_t 同样截取到当前时刻，还需要乘以此时的扫频频率
        rho(:,i+1) = Delta_t * sum(H .* fliplr(X),2); % 翻转 X 后，再和传递函数对应点相乘，然后按第二维度累加求和，等效于 conv
        % (sum 代替 conv ,因长度相同+滑窗法则)


        % 卷积积分前面乘以系数才是i+1时刻的声场
        q_store(:,i+1) = -1i*g0*gamma*Omega_B(:).*rho(:,i+1); % i+1时刻声场
        % q_store(:,i+1)是一个列向量

        % i+1时刻A1和A2真正的值
%         A1_store(:,i+1) = (1 - vc*alpha/2*Delta_t)*A1_store(:,i+1) - g0/2*vc*A2_store(:,i+1).*q_store(:,i+1)*Delta_t;
%         A2_store(:,i+1) = (1 - vc*alpha/2*Delta_t)*A2_store(:,i+1) + g0/2*vc*A1_store(:,i+1).*conj(q_store(:,i+1))*Delta_t;

        A1_store(:,i+1) = A1_store(:,i+1)*exp(-alpha*dz) - g0/2*vc*A2_store(:,i+1).*q_store(:,i+1)*Delta_t;
        A2_store(:,i+1) = A2_store(:,i+1)*exp(-alpha*dz) + g0/2*vc*A1_store(:,i+1).*conj(q_store(:,i+1))*Delta_t;
        % 这里没有像分步法公式那样来加上损耗，因为分步法中的(1-vc*alpha/2*Delta_t)其实就是exp(-alpha*dz)泰勒展开后的结果，
        % 而且(1-vc*alpha/2*Delta_t)中的是功率衰减系数，我这里e指数里面用的是幅度衰减系数，所以(1-vc*alpha/2*Delta_t)实际上对应的是exp(-alpha/2*dz)，但我用幅度衰减系数的话就不用除以2了

        % 恢复边界值
        A1_store(1,i+1) = Pump_t(i+1);
        A2_store(end,i+1) = Probe_t(i+1);
        
    end

    % 存储每次扫频后的结果
    A1_store_3d(:,:,freq_idx) = A1_store;
    A2_store_3d(:,:,freq_idx) = A2_store;
    q_store_3d(:,:,freq_idx) = q_store;

    % 提取探测光场在z=0处的时间演化
    A2_at_receiver = A2_store(1, :);
    
    % 计算功率
    power_time = abs(A2_at_receiver).^2;
    
    % 存储在不同扫描频率下在z=0处得到的探测光功率随时间的演化
    probe_map_SBS(:, freq_idx) = power_time(:); % 用:把power_time转化为一个列向量
    % 存储斯托克斯光功率变化
    stokes_map_SBS(:, freq_idx) = power_time(:) - power_time_linear(:);

end

delete(gcp('nocreate')); % 显式关闭并行池
toc


%% --------- 验证：SBS耦合方程求解结果 vs 真实响应求和结果 -------------------
target_idx_plot = 9; 

figure(66);
clf;
subplot(2,1,1);
plot(t*1e9, probe_map_SBS(:,target_idx_plot), 'b', 'LineWidth',1.5); hold on;
plot(t*1e9, probe_map_theory(:,target_idx_plot), 'r--', 'LineWidth',1.5);
xlabel('t / ns'); ylabel('P_{probe}(W) at z=0');
title(sprintf('Probe Power @ z=0 (Scan Freq: %.3f GHz)', Omega_list(target_idx_plot)/(2*pi)/1e9));
legend('Full Coupled Sim','Real Response Sim'); grid on;
hold off;

subplot(2,1,2);
plot(t*1e9, stokes_map_SBS(:,target_idx_plot), 'b', 'LineWidth',1.5); hold on;
plot(t*1e9, stokes_map_theory(:,target_idx_plot), 'r--', 'LineWidth',1.5);
xlabel('t / ns'); ylabel('SBS Gain Power (W) at z=0');
title(sprintf('Stokes Power @ z=0 (Scan Freq: %.3f GHz)', Omega_list(target_idx_plot)/(2*pi)/1e9));
legend('Full Coupled Sim','Real Response Sim'); grid on;
hold off;


% 添加声场对比（选择脉冲前沿相遇点进行对比）
figure(67);
t_meet = (L + vc*(t0_p + t0_s)) / (2 * vc); % 两脉冲前沿相遇时间 (s)
z_meet = vc * (t_meet - t0_p);              % 两脉冲前沿相遇位置 (m)
idx_meet = round(z_meet/dz + 1);            % 两脉冲前沿相遇位置索引

plot(t*1e9, abs(q_store_3d(idx_meet,:,target_idx_plot)), 'b', 'LineWidth',1.5); 
hold on;
plot(t*1e9, abs(q_store_theory(idx_meet,:,target_idx_plot)), 'r--', 'LineWidth',1.5);
xlabel('t / ns'); 
ylabel('|q| (Acoustic Field Amplitude)');
title(sprintf('Acoustic Field @ z=%.1fm (Scan Freq: %.3f GHz)', z_meet, Omega_list(target_idx_plot)/(2*pi)/1e9));
legend('Full Coupled Sim','Real Response Sim'); 
grid on;
hold off;


%% BGS提取与可视化
% 计算两脉冲前沿相遇的空间位置
pump_position = @(t) vc * (t - t0_p); % 泵浦脉冲在时间t的位置
probe_position = @(t) L - vc * (t - t0_s); % 探测脉冲在时间t的位置（从L端入射）
% 求解相遇时间和位置
syms t_meet_sym
meet_eq = pump_position(t_meet_sym) == probe_position(t_meet_sym);
t_meet_sol = double(solve(meet_eq, t_meet_sym));
meeting_point_actual = pump_position(t_meet_sol);

pump_length = vc * Pump_width;   % 泵浦脉冲空间长度 (m)
probe_length = vc * Probe_width; % 探测脉冲空间长度 (m)
spatial_resolution = pump_length / 2;  % 空间分辨率 (m)

% 计算探测光脉冲前沿到达z=0的时间
t_probe_arrive = t0_s + n*L/c;  % 探测光入射时间 + 光纤传播时间

% 计算需要提取的窗口数量，即从探测脉冲中能得到多少个不同的BGS
num_windows = round(probe_length / pump_length);

% 计算窗口的时间长度，即是从探测脉冲上应该提取的时间长度
window_time = Pump_width;  % 时间窗口等于泵浦脉冲宽度
time_window = round(window_time / Delta_t);  % 时间窗口长度(点数)

% 计算探测光脉冲前沿到达z=0的时间
[~, idx_arrive] = min(abs(t - t_probe_arrive));  % 到达时间索引

% 创建存储所有BGS的矩阵
all_BGS = zeros(num_windows, n_sweep);

% 定义窗口对应的光纤位置
% 对于相遇点为20m的情况：15m-20m, 20m-25m；对于相遇点为30m的情况：25m-30m, 30m-35m
window_positions = zeros(num_windows, 2);  % 存储每个窗口的[起始,结束]位置
for w = 1:num_windows
    if w == 1
        % 第一个窗口: [相遇位置-空间分辨率, 相遇位置]
        window_positions(w, :) = [meeting_point_actual - spatial_resolution, meeting_point_actual];
    else
        % 其他窗口: [相遇位置+(w-2)*空间分辨率, 相遇位置+(w-1)*空间分辨率]
        window_positions(w, :) = [meeting_point_actual + (w-2)*spatial_resolution, meeting_point_actual + (w-1)*spatial_resolution];
    end
end

% 提取所有时间窗口的BGS
figure(50);
hold on;

for window = 1:num_windows
    % 计算当前窗口的起始和结束索引
    start_idx = idx_arrive + (window-1) * time_window;
    end_idx = min(start_idx + time_window, Nt);
    
    % 确保索引不越界
    if start_idx >= Nt || end_idx > Nt
        disp(['窗口', num2str(window), '超出数据范围，跳过']);
        continue;
    end
    
    % 获取当前窗口的位置信息
    window_start = window_positions(window, 1);
    window_end = window_positions(window, 2);
    
    % 提取时间窗口内的数据并计算平均功率
    for freq_idx = 1:n_sweep
        window_data = stokes_map_SBS(start_idx:end_idx, freq_idx);
        % 用stokes_map_SBS，而不用probe_map_SBS，是为了避免由于新产生的斯托克斯光太小，导致每个频率点的探测光幅度几乎都没有什么变化，用probe_map_SBS绘制BGS的时候就会变得很宽
        % 而且我发现用stokes时，得到的BPGR才几乎是一条直线（正确），用probe的话，得到的BPGR却是一条洛伦兹曲线（错误）
        
        all_BGS(window, freq_idx) = mean(window_data);
    end
    
    % 归一化当前窗口的BGS
    normalized_BGS = all_BGS(window, :) / max(all_BGS(window, :));

    % 根据此时窗口索引生成颜色
    current_color = indexToColor(window);
    
    % 绘制当前窗口的BGS，使用索引生成的颜色
    plot((Omega_list/(2*pi)-10.8e9)/1e6, normalized_BGS, '-o', 'LineWidth', 2, 'Color', current_color, 'MarkerFaceColor', current_color);
    
    % 找到BGS峰值频率
    [~, max_idx] = max(all_BGS(window, :));
    peak_frequency = (Omega_list(max_idx)/(2*pi)-10.8e9)/1e6;  % 相对于10.8GHz的频偏 (MHz)
    
    % 显示当前窗口的信息
    disp(['窗口', num2str(window), ' - 位置: ', num2str(window_start), 'm - ', ...
          num2str(window_end), 'm, 峰值频移: ', num2str(peak_frequency, '%.2f'), ' MHz']);
end

% 完成图形设置
xlabel('频率偏移 (MHz)');
ylabel('归一化增益');
title('多窗口时间平均提取的布里渊增益谱 (BGS)');
grid on;
% 添加图例
legend_str = cell(num_windows, 1);
for i = 1:num_windows
    legend_str{i} = [num2str(window_positions(i, 1)), 'm - ', num2str(window_positions(i, 2)), 'm'];
end
legend(legend_str, 'Location', 'best');
hold off;

% 提取并可视化布里渊频移沿光纤的分布
% 计算每个窗口的BFS
BFS_distribution = zeros(num_windows, 1);
for window = 1:num_windows
    [~, max_idx] = max(all_BGS(window, :));
    BFS_distribution(window) = (Omega_list(max_idx)/(2*pi)-10.8e9)/1e6;
end

figure(53);
hold on;

% 创建连续的测量结果曲线
measured_fiber_positions = [];
measured_shifts = [];

% 对每个窗口，创建对应区域的连续线段
for window = 1:num_windows
    window_start = window_positions(window, 1);
    window_end = window_positions(window, 2);
    
    % 在当前窗口范围内创建密集的点
    window_positions_dense = linspace(window_start, window_end, 20);
    window_shifts_dense = ones(1, length(window_positions_dense)) * BFS_distribution(window);
    
    % 添加到总数组中
    measured_fiber_positions = [measured_fiber_positions, window_positions_dense];
    measured_shifts = [measured_shifts, window_shifts_dense];
end

% 根据位置排序（确保连续性）
[measured_fiber_positions, sort_idx] = sort(measured_fiber_positions);
measured_shifts = measured_shifts(sort_idx);

% 绘制测量结果为连续线条
plot(measured_fiber_positions, measured_shifts, 'b-', 'LineWidth', 2);
% 在线条上添加标记点以显示窗口中心
plot(mean(window_positions, 2), BFS_distribution, 'bo', 'MarkerSize', 8, 'MarkerFaceColor', 'b');

% 添加理论频移分布
fiber_positions = 0:0.1:L;
theoretical_shift = zeros(size(fiber_positions));

% 遍历每个应变区域，设置对应的理论频移值
for i = 1:size(strain_regions, 1)
    region_start = strain_regions(i, 1);
    region_end = strain_regions(i, 2);
    region_shift = strain_regions(i, 3);
    
    % 找到当前应变区域对应的位置索引
    region_indices = (fiber_positions >= region_start) & (fiber_positions <= region_end);
    
    % 设置对应区域的理论频移值
    theoretical_shift(region_indices) = region_shift;
end

% 绘制理论频移分布曲线
plot(fiber_positions, theoretical_shift, 'r--', 'LineWidth', 2);

xlabel('光纤位置 (m)');
ylabel('布里渊频移 (MHz)');
title('布里渊频移分布');
legend('测量结果', '测量窗口中心', '理论值');
grid on;
xlim([0 L]);

% 显示关键参数
disp(['探测光到达z=0的时间: ', num2str(t_probe_arrive*1e9), ' ns']);
disp(['探测脉冲空间长度: ', num2str(probe_length), ' m']);
disp(['泵浦脉冲空间长度: ', num2str(pump_length), ' m']);
disp(['空间分辨率: ', num2str(spatial_resolution), ' m']);
disp(['脉冲相遇点: ', num2str(meeting_point_actual), ' m']);
disp(['需要提取的窗口数量: ', num2str(num_windows)]);


%% BPS提取与可视化
% 创建存储所有BPS的矩阵
all_BPS = zeros(num_windows, n_sweep);

% 创建一个新图窗口显示BPS
figure(51);
hold on;

% 对每个窗口进行处理
for window = 1:num_windows
    % 计算当前窗口的起始和结束索引（与BGS相同）
    start_idx = idx_arrive + (window-1) * time_window;
    end_idx = min(start_idx + time_window, Nt);
    
    % 确保索引不越界
    if start_idx >= Nt || end_idx > Nt
        disp(['窗口', num2str(window), '超出数据范围，跳过']);
        continue;
    end
    
    % 获取当前窗口的位置信息
    window_start = window_positions(window, 1);
    window_end = window_positions(window, 2);
    
    % 提取时间窗口内的相位数据，计算BPS
    % 方法一：先平均复振幅再提取相位（更稳健）
    for freq_idx = 1:n_sweep
        % 从A2_store_3d中提取接收端的复场振幅
        A2_at_receiver = A2_store_3d(1, start_idx:end_idx, freq_idx);
        A2_linear_at_receiver = A2_linear(1, start_idx:end_idx);

        % 先平均复振幅再提取相位
        mean_complex_amplitude = mean(A2_at_receiver);
        mean_linear_amplitude = mean(A2_linear_at_receiver);

        % 计算相位变化 - 使用SBS增益信号与线性信号的相位差
        all_BPS(window, freq_idx) = angle(mean_complex_amplitude / mean_linear_amplitude);
    end

%     % 方法二：先提取相位再平均（发现和上面结果也是一样的）
%     for freq_idx = 1:n_sweep
%         % 从probe_map中提取幅度数据
%         window_data_amplitude = sqrt(probe_map_SBS(start_idx:end_idx, freq_idx));
%         
%         % 从A2_store_3d中提取对应的相位数据
%         A2_at_receiver = A2_store_3d(1, start_idx:end_idx, freq_idx);
%         window_data_phase = unwrap(angle(A2_at_receiver));  % 提取相位
%         
%         % 计算加权平均相位（使用幅度作为权重）
%         weighted_phase = window_data_amplitude .* window_data_phase.'; % 转置后两个向量维度才一致
%         all_BPS(window, freq_idx) = sum(weighted_phase) / sum(window_data_amplitude);
%     end
    

    % 根据窗口索引生成颜色（与BGS绘图使用相同颜色）
    current_color = indexToColor(window);
    
    % 绘制当前窗口的BPS
    plot((Omega_list/(2*pi)-10.8e9)/1e6, all_BPS(window, :), '-o', 'LineWidth', 2, 'Color', current_color, 'MarkerFaceColor', current_color);
    
    % 找到BPS零点交叉位置（对应于布里渊频移）  根据yang-2017文献中的BPS公式：-(2g_B v_B Δv)/(v_B^2+4Δv^2 )，可以发现没有频移的时候，失谐为0时，BPS的值为0，或者说此时斜率最大
    % 寻找BPS值最接近0的点（零点交叉）
    [~, zero_idx] = min(abs(all_BPS(window, :)));
    zero_cross_freq = (Omega_list(zero_idx)/(2*pi)-10.8e9)/1e6;

    % 显示当前窗口的信息
    disp(['窗口', num2str(window), ' - 位置: ', num2str(window_start), 'm - ', ...
          num2str(window_end), 'm, BPS零点交叉频率: ', num2str(zero_cross_freq, '%.2f'), ' MHz']);
    
end

% 完成图形设置
xlabel('频率偏移 (MHz)');
ylabel('布里渊相移 (rad)');
title('多窗口时间平均提取的布里渊相移谱 (BPS)');
grid on;

% 添加图例
legend(legend_str, 'Location', 'best');
hold off;

%% 计算并显示布里渊相位增益比(BPGR)
% 创建存储BPGR的矩阵
all_BPGR = zeros(num_windows, n_sweep);

figure(52);
hold on;

for window = 1:num_windows
    % 计算BPGR
    for freq_idx = 1:n_sweep
        % 避免除以零，添加一个小值
        if abs(all_BGS(window, freq_idx)) > 1e-10
            all_BPGR(window, freq_idx) = all_BPS(window, freq_idx) / all_BGS(window, freq_idx);
        else
            all_BPGR(window, freq_idx) = 0;
        end
    end
    
    % 根据窗口索引生成颜色
    current_color = indexToColor(window);
    
    % 绘制当前窗口的BPGR
    plot((Omega_list/(2*pi)-10.8e9)/1e6, all_BPGR(window, :), '-o', 'LineWidth', 2, 'Color', current_color, 'MarkerFaceColor', current_color);
end

% 完成图形设置
xlabel('频率偏移 (MHz)');
ylabel('布里渊相位增益比 (BPGR)');
title('多窗口布里渊相位增益比谱 (BPGR)');
grid on;

% 添加图例
legend(legend_str, 'Location', 'best');
hold off;



%% 绘制三维图结果

% 对比在目标扫频频率下与泵浦功率谱卷积后的h_t_conv与卷积前的h_t
% figure(6)
% % 第一个子图：显示实部和虚部
% subplot(2,1,1)
% plot(t*1e9, real(h_t_conv_save{target_idx_1}), 'b-', 'LineWidth', 2);
% hold on;
% plot(t*1e9, imag(h_t_conv_save{target_idx_1}), 'b--', 'LineWidth', 2);
% plot(t*1e9, real(h_t_save{target_idx_1}), 'r-', 'LineWidth', 2);
% plot(t*1e9, imag(h_t_save{target_idx_1}), 'r--', 'LineWidth', 2);
% hold off;
% xlabel('时间 t (ns)');
% ylabel('h(t) 实部和虚部');
% legend('卷积后 h(t) 实部', '卷积后 h(t) 虚部', '原始 h(t) 实部', '原始 h(t) 虚部');
% title(sprintf('h(t) 的实部和虚部 @ %.3f GHz扫频频率', Omega_list(target_idx_1)/(2*pi)/1e9));

% 第二个子图：显示模
% subplot(2,1,2)
% plot(t*1e9, abs(h_t_conv_save{target_idx_1}), 'b', 'LineWidth', 2);
% hold on;
% plot(t*1e9, abs(h_t_save{target_idx_1}), 'r--', 'LineWidth', 2);
% hold off;
% xlabel('时间 t (ns)');
% ylabel('|h(t)|');
% legend('卷积后的 |h(t)|', '原始 |h(t)|');
% title(sprintf('h(t) 的模 @ %.3f GHz扫频频率', Omega_list(target_idx_1)/(2*pi)/1e9));


% 绘制在不同扫描频率下接收端新产生的斯托克斯光功率随时间的演化的三线图
figure(10)
hold on; % 保持当前图形
for idx = 1:n_sweep
    % 对于第 idx 个扫频点，其频率（GHz）为：
    freq_val = Omega_list(idx) / (2*pi*1e9); 
    % 构造与时间向量等长的常数数组，用作 y 轴
    y_val = freq_val * ones(size(t));
    color = [rand, rand, rand]; % 随机颜色
    plot3(t*1e9, y_val, stokes_map_SBS(:, idx), 'Color', color, 'LineWidth', 2);
end
hold off;
% 设置标签
xlabel('时间 (ns)');
ylabel('频率 (GHz)');
zlabel('Stokes 功率 (W)');
grid on; % 显示网格
view(3);  % 默认 3D 视角，不加的话就只会显示二维图

% 绘制在相遇点不同扫描频率下声场随时间的演化的三线图
figure(11)
t_meet = (L + vc*(t0_p + t0_s)) / (2 * vc); % 两脉冲前沿相遇时间 (s)
z_meet = vc * (t_meet - t0_p);              % 两脉冲前沿相遇位置 (m)
idx_meet = round(z_meet/dz + 1);            % 两脉冲前沿相遇位置索引
q_meet = squeeze(q_store_3d(idx_meet,:,:)); % 取出相遇点在所有扫描频率下声场随时间的变化
hold on; % 保持当前图形
for idx = 1:n_sweep
    % 对于第 idx 个扫频点，其频率（GHz）为：
    freq_val = Omega_list(idx) / (2*pi*1e9); 
    % 构造与时间向量等长的常数数组，用作 y 轴
    y_val = freq_val * ones(size(t));
    color = [rand, rand, rand]; % 随机颜色
    plot3(t*1e9, y_val, abs(q_meet(:, idx)), 'Color', color, 'LineWidth', 2);
end
hold off;
% 设置标签
xlabel('时间 (ns)');
ylabel('频率 (GHz)');
zlabel('声场幅度');
grid on; % 显示网格
view(3);  % 默认 3D 视角，不加的话就只会显示二维图


% 找到最接近10.8GHz的频率索引进行绘图

target_freq = 2*pi*10.8e9; % 目标频率10.8GHz
[~, target_idx] = min(abs(Omega_list - target_freq)); % 找到最接近的频率索引
actual_Omega = Omega_list(target_idx); % 实际使用的角频率

fprintf('显示扫频频率为 %.6f GHz 的场分布\n', actual_Omega/(2*pi)/1e9);

% 提取对应频率的场数据
A1_target = A1_store_3d(:,:,target_idx); % 泵浦场
A2_target = A2_store_3d(:,:,target_idx); % 斯托克斯场
q_target = q_store_3d(:,:,target_idx);   % 声场

% 创建网格用于3D绘图
[T, Z] = meshgrid(t*1e9, z); % 转换为ns和m的网格

% 绘制泵浦场的3D图
figure(1);
surf(T, Z, abs(A1_target).^2);
title(sprintf('泵浦场功率分布 @ %.3f GHz扫频频率', actual_Omega/(2*pi)/1e9));
xlabel('时间 (ns)');
ylabel('位置 (m)');
zlabel('功率 (W)');
colormap('jet');
shading interp;
colorbar;
view(30, 30); % 设置视角
grid on;

% 绘制斯托克斯场的3D图
figure(2);
surf(T, Z, abs(A2_target).^2);
title(sprintf('斯托克斯场功率分布 @ %.3f GHz扫频频率', actual_Omega/(2*pi)/1e9));
xlabel('时间 (ns)');
ylabel('位置 (m)');
zlabel('功率 (W)');
colormap('jet');
shading interp;
colorbar;
view(30, 30);
grid on;

% 绘制声场的3D图
figure(3);
surf(T, Z, abs(q_target));
title(sprintf('声场幅度分布 @ %.3f GHz扫频频率', actual_Omega/(2*pi)/1e9));
xlabel('时间 (ns)');
ylabel('位置 (m)');
zlabel('声场幅度');
colormap('jet');
shading interp;
colorbar;
view(30, 30);
grid on;

%% 绘制2D热图
figure(4);

% 泵浦场热图
subplot(3,1,1);
imagesc(t*1e9, z, abs(A1_target).^2);
title(sprintf('泵浦场功率分布 @ %.3f GHz扫频频率', actual_Omega/(2*pi)/1e9));
xlabel('时间 (ns)');
ylabel('位置 (m)');
colorbar;
axis xy; % 确保y轴方向正确

% 斯托克斯场热图
subplot(3,1,2);
imagesc(t*1e9, z, abs(A2_target).^2);
title(sprintf('斯托克斯场功率分布 @ %.3f GHz扫频频率', actual_Omega/(2*pi)/1e9));
xlabel('时间 (ns)');
ylabel('位置 (m)');
colorbar;
axis xy;

% 声场热图
subplot(3,1,3);
imagesc(t*1e9, z, abs(q_target));
title(sprintf('声场幅度分布 @ %.3f GHz扫频频率', actual_Omega/(2*pi)/1e9));
xlabel('时间 (ns)');
ylabel('位置 (m)');
colorbar;
axis xy;

% 调整子图之间的间距
set(gcf, 'Color', 'w');
tight_subplot = false; % 如果有tight_subplot函数可以设为true
if tight_subplot && exist('tight_subplot', 'file')
    tight_subplot(3,1,[0.08 0.03],[0.1 0.05],[0.1 0.05]);
end


%% 动画设置
if anim_active == 1
%     % 关闭所有图形窗口
%     close all;

    % 设置字体和图形参数
    fontS = 18;
    FS = '\fontname{Palatino} ';
    x_ticks = linspace(0, L, 10);

    % 视角设置
    az_angle = 60;
    el_angle = 60;
    fontSS = fontS;

    % 动画速度设置
    step1 = 1;  % 动画速度，增大会加快，因为每次绘制的间隔帧数变大，会漏掉一些图像
    vid1 = VideoWriter(title1, 'MPEG-4'); % 视频文件名
    vid1.Quality = 100;
    open(vid1);
    
    % 创建图形窗口
    figure(5);
    set(gcf, 'color', 'w');
    % 获取当前屏幕尺寸
    screen_size = get(0, 'ScreenSize');
    % 计算窗口的居中位置
    left_position = (screen_size(3) - 1400) / 2; % 1400：窗口的宽度
    bottom_position = (screen_size(4) - 800) / 2; % 800：窗口的高度
    % 仅设置宽度和高度，位置自动居中
    set(gcf, 'Position', [left_position, bottom_position, 1400, 800]);

    % 添加标题
    sgtitle(sprintf('受激布里渊散射过程中的光声场演化 @ %.3f GHz扫频频率', actual_Omega/(2*pi)/1e9));

    % 计算最大值以设置 y 轴范围
    maxP1 = max(max(abs(A1_target).^2));  % 泵浦场最大值
    maxP2 = max(max(abs(A2_target).^2));  % 斯托克斯场最大值
    maxPa = max(max(abs(q_target).^2));   % 声场最大值

    % 动画循环
    for n = 1:step1:Nt
        % 创建上下两行布局
        subplot(2,1,1); % 上半部分用于原有三个子图
        
        % 绘制泵浦场
        subplot(2,3,1);
        h1 = area(z, abs(A1_target(:, n)).^2);
        h1.LineWidth = 0.5;
        h1.FaceColor = 'blue';
        h1.FaceAlpha = 0.5;
        h1.EdgeColor = 'black';
        xlim([0 L]);
        ylim([0 maxP1]);
        xlabel([FS 'z (m)']);
        ylabel([FS 'Pump power (W)']);
        title([FS 't = ' sprintf('%.2f', 1e9 * t(n)) ' (ns)']);
        set(gca, 'FontSize', fontS);
        grid on;
        
        % 绘制斯托克斯场
        subplot(2,3,2);
        h2 = area(z, abs(A2_target(:, n)).^2);
        h2.LineWidth = 0.5;
        h2.FaceColor = 'red';
        h2.FaceAlpha = 0.5;
        h2.EdgeColor = 'black';
        xlim([0 L]);
        ylim([0 maxP2]);
        xlabel([FS 'z (m)']);
        ylabel([FS 'Stokes power (W)']);
        set(gca, 'FontSize', fontS);
        grid on;
        
        % 绘制声场
        subplot(2,3,3);
        h3 = area(z, abs(q_target(:, n)).^2);
        h3.LineWidth = 0.5;
        h3.FaceColor = 'green';
        h3.FaceAlpha = 0.5;
        h3.EdgeColor = 'black';
        xlim([0 L]);
        ylim([0 maxPa]);
        xlabel([FS 'z (m)']);
        ylabel([FS 'Acoustic power (W)']);
        set(gca, 'FontSize', fontS);
        grid on;
        
        % 添加综合视图在底部
        subplot(2,1,2);
        % 使用相同的区域填充风格
        h1b = area(z, abs(A1_target(:, n)).^2);
        h1b.LineWidth = 0.5;
        h1b.FaceColor = 'blue';
        h1b.FaceAlpha = 0.5;
        h1b.EdgeColor = 'black';
        hold on;
        
        h2b = area(z, abs(A2_target(:, n)).^2);
        h2b.LineWidth = 0.5;
        h2b.FaceColor = 'red';
        h2b.FaceAlpha = 0.5;
        h2b.EdgeColor = 'black';
        
        h3b = area(z, abs(q_target(:, n)).^2);
        h3b.LineWidth = 0.5;
        h3b.FaceColor = 'green';
        h3b.FaceAlpha = 0.5;
        h3b.EdgeColor = 'black';
        
        hold off;
        xlim([0 L]);
        ylim([0 max([maxP1, maxP2, maxPa])]);
        xlabel([FS 'z (m)']);
        ylabel([FS 'Power (W)']);
        legend('Pump', 'Stokes', 'Acoustic', 'Location', 'northeast');
        set(gca, 'FontSize', fontS);
        grid on;
        
        % 导出每帧到视频
        drawnow;
        frame = getframe(gcf);
        writeVideo(vid1, frame);
    end
    close(vid1);
end



% %% 绘制结果
% % 绘制布里渊增益谱
% figure;
% plot(Omega_list/1e9, BGS/max(BGS), 'LineWidth', 2);
% xlabel('频率差 (GHz)');
% ylabel('归一化布里渊增益');
% title('布里渊增益谱 (BGS)');
% grid on;
% 
% % 绘制声场演化
% figure;
% [T, Z] = meshgrid(t*1e9, z);
% imagesc(t*1e9, z, abs(final_rho));
% xlabel('时间 (ns)');
% ylabel('位置 (m)');
% title('声场幅度演化');
% colorbar;
% axis xy;
% 
% % 绘制特定时刻的声场分布
% figure;
% selected_time = 200e-9;  % 选择一个特定时刻
% [~, time_idx] = min(abs(t - selected_time));
% plot(z, abs(final_rho(:, time_idx)), 'LineWidth', 2);
% xlabel('位置 (m)');
% ylabel('声场幅度');
% title(['声场分布 @ t = ', num2str(selected_time*1e9), ' ns']);
% grid on;
% 
% % 绘制特定位置的声场时间演化
% figure;
% selected_pos = 21;  % 选择应变区域中的一个位置
% [~, pos_idx] = min(abs(z - selected_pos));
% plot(t*1e9, abs(final_rho(pos_idx, :)), 'LineWidth', 2);
% xlabel('时间 (ns)');
% ylabel('声场幅度');
% title(['声场时间演化 @ z = ', num2str(selected_pos), ' m']);
% grid on;
% 
% % 绘制泵浦光和探测光的传播
% figure;
% subplot(2,1,1);
% imagesc(t*1e9, z, abs(final_A1).^2);
% xlabel('时间 (ns)');
% ylabel('位置 (m)');
% title('泵浦光功率分布');
% colorbar;
% axis xy;
% 
% subplot(2,1,2);
% imagesc(t*1e9, z, abs(final_A2).^2);
% xlabel('时间 (ns)');
% ylabel('位置 (m)');
% title('探测光功率分布');
% colorbar;
% axis xy;




% 定义基于索引的颜色生成函数
% 使用HSV颜色空间：H(色相)由索引确定，S(饱和度)和V(亮度)固定
function color = indexToColor(idx)
    % 使用黄金比例确保色相分布均匀
    golden_ratio_conjugate = 0.618033988749895;
    h = mod(idx * golden_ratio_conjugate, 1); % 色相 (0-1)
    s = 0.85;  % 饱和度
    v = 0.9;   % 亮度
    color = hsv2rgb([h s v]); % 转换为RGB
end


% % 基于索引的颜色生成函数
% function color = indexToColor(idx)
%     % 通过索引计算RGB各通道值
%     % 使用正弦函数生成周期性但互不相同的RGB值
%     R = abs(sin(0.7 * idx));
%     G = abs(sin(0.5 * idx + 2.1));
%     B = abs(sin(0.3 * idx + 4.2));
%     color = [R G B];
% end





