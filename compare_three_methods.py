#!/usr/bin/env python3
# ---------------------------------------------------------------
#  三种方法BFS分布对比分析
#  
#  功能：
#  1. 读取深度学习预测结果
#  2. 读取真实BFS分布
#  3. 读取MATLAB传统方法结果
#  4. 绘制对比图和局部放大图
# ---------------------------------------------------------------

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
from scipy.signal import find_peaks

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_botda_test_results():
    """读取botda_test.py保存的真实BFS和预测结果"""

    # 读取botda_test.py保存的数据
    python_data_path = 'test_results/bfs_data_file_0.npz'

    if not os.path.exists(python_data_path):
        raise FileNotFoundError(f"未找到botda_test保存的结果: {python_data_path}")

    # 加载数据
    data = np.load(python_data_path)
    prediction = data['prediction'].flatten()  # 模型预测的BFS分布
    target = data['target'].flatten()          # 真实的BFS分布
    bfs_min = float(data['bfs_min'])           # BFS最小值
    bfs_max = float(data['bfs_max'])           # BFS最大值

    print(f"✅ botda_test结果加载完成:")
    print(f"   真实BFS数据点数: {len(target)}")
    print(f"   预测BFS数据点数: {len(prediction)}")
    print(f"   BFS范围: [{bfs_min:.1f}, {bfs_max:.1f}] MHz")
    print(f"   真实BFS范围: [{np.min(target):.3f}, {np.max(target):.3f}] MHz")
    print(f"   预测BFS范围: [{np.min(prediction):.3f}, {np.max(prediction):.3f}] MHz")

    # 创建空间坐标轴 (0-160m，点数为target长度)
    fiber_length = 160  # botda_test的数据对应0-160m
    positions = np.linspace(0, fiber_length, len(target))

    print(f"   空间范围: {positions[0]:.1f} - {positions[-1]:.1f} m")
    print(f"   空间步长: {(positions[-1] - positions[0]) / (len(positions) - 1):.3f} m/点")

    return prediction, target, positions

def load_matlab_results(target_length):
    """读取MATLAB传统方法结果并截取到指定长度"""
    csv_path = 'results/matlab_bfs_measurement.csv'
    mat_path = 'results/matlab_bfs_measurement.mat'

    # 优先尝试读取CSV文件
    if os.path.exists(csv_path):
        print(f"📖 读取MATLAB CSV结果: {csv_path}")
        data = pd.read_csv(csv_path, header=None)
        positions = data.iloc[:, 0].values
        bfs_values = data.iloc[:, 1].values

    elif os.path.exists(mat_path):
        print(f"📖 读取MATLAB MAT结果: {mat_path}")
        import scipy.io as sio
        data = sio.loadmat(mat_path)
        positions = data['sorted_positions'].flatten()  # 修正变量名
        bfs_values = data['sorted_BFS'].flatten()       # 修正变量名

    else:
        raise FileNotFoundError(f"未找到MATLAB结果文件: {csv_path} 或 {mat_path}")

    print(f"✅ MATLAB结果加载完成:")
    print(f"   原始数据点数: {len(bfs_values)}")
    print(f"   原始空间范围: {positions[0]:.1f} - {positions[-1]:.1f} m")

    # 🔥 直接截取前target_length个点（MATLAB结果肯定更长）
    if len(bfs_values) >= target_length:
        bfs_values_truncated = bfs_values[:target_length]
        print(f"🔧 截取前{target_length}个数据点 (0-160m区域)")
        print(f"   截取后BFS范围: [{np.min(bfs_values_truncated):.3f}, {np.max(bfs_values_truncated):.3f}] MHz")
    else:
        raise ValueError(f"MATLAB数据长度({len(bfs_values)})小于目标长度({target_length})")

    return bfs_values_truncated

def detect_strain_regions(bfs_data, positions, threshold_factor=2.0):
    """检测频移区域（应变区域）"""
    
    # 计算BFS的基线（中位数）
    baseline = np.median(bfs_data)
    
    # 计算标准差
    std_dev = np.std(bfs_data)
    
    # 设置阈值
    threshold = threshold_factor * std_dev
    
    # 找到超过阈值的区域
    strain_mask = np.abs(bfs_data - baseline) > threshold
    
    # 找到连续的应变区域
    strain_regions = []
    in_region = False
    region_start = 0
    
    for i, is_strain in enumerate(strain_mask):
        if is_strain and not in_region:
            # 开始新的应变区域
            region_start = i
            in_region = True
        elif not is_strain and in_region:
            # 结束当前应变区域
            region_end = i - 1
            if region_end > region_start:  # 确保区域有效
                strain_regions.append((region_start, region_end))
            in_region = False
    
    # 处理最后一个区域
    if in_region:
        strain_regions.append((region_start, len(strain_mask) - 1))
    
    # 转换为位置坐标
    position_regions = []
    for start_idx, end_idx in strain_regions:
        start_pos = positions[start_idx]
        end_pos = positions[end_idx]
        center_pos = (start_pos + end_pos) / 2
        position_regions.append((start_pos, end_pos, center_pos))
    
    print(f"🔍 检测到 {len(position_regions)} 个应变区域:")
    for i, (start, end, center) in enumerate(position_regions):
        print(f"   区域{i+1}: {start:.1f} - {end:.1f} m (中心: {center:.1f} m)")
    
    return position_regions

def plot_comparison(prediction, target, matlab_bfs, positions):
    """绘制三种方法的对比图"""

    # 创建主对比图
    plt.figure(figsize=(16, 10))

    # 绘制三条曲线
    plt.plot(positions, target, 'b-', linewidth=2, label='Truth BFS', alpha=0.8)
    plt.plot(positions, prediction, 'r--', linewidth=2, label='Deep Learning Prediction', alpha=0.8)
    plt.plot(positions, matlab_bfs, 'g:', linewidth=2, label='Traditional MATLAB Method', alpha=0.8)

    plt.xlabel('Fiber Position (m)', fontsize=14)
    plt.ylabel('BFS (MHz)', fontsize=14)
    plt.title('BFS Distribution Comparison of Three Methods', fontsize=16, fontweight='bold')
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)

    # 添加统计信息
    stats_text = f"""Statistics:
Truth: {np.mean(target):.3f} ± {np.std(target):.3f} MHz
Deep Learning: {np.mean(prediction):.3f} ± {np.std(prediction):.3f} MHz
Traditional Method: {np.mean(matlab_bfs):.3f} ± {np.std(matlab_bfs):.3f} MHz

Prediction Error (MAE): {np.mean(np.abs(prediction - target)):.4f} MHz
Traditional Error (MAE): {np.mean(np.abs(matlab_bfs - target)):.4f} MHz"""

    plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
             fontsize=10)

    plt.tight_layout()
    plt.savefig('results/three_methods_comparison.png', dpi=300, bbox_inches='tight')
    print("✅ 主对比图已保存: results/three_methods_comparison.png")
    plt.show()

def plot_strain_regions(prediction, target, matlab_bfs, positions, strain_regions):
    """绘制应变区域的放大图"""

    if not strain_regions:
        print("⚠️  未检测到应变区域，跳过放大图绘制")
        return

    # 为每个应变区域创建单独的图窗
    for i, (start_pos, end_pos, center_pos) in enumerate(strain_regions):

        # 定义放大区域（中心±2.5m，总共5m范围）
        zoom_start = max(0, center_pos - 2.5)
        zoom_end = min(positions[-1], center_pos + 2.5)

        # 找到对应的索引范围
        start_idx = np.argmin(np.abs(positions - zoom_start))
        end_idx = np.argmin(np.abs(positions - zoom_end))

        # 提取放大区域的数据
        zoom_positions = positions[start_idx:end_idx+1]
        zoom_target = target[start_idx:end_idx+1]
        zoom_prediction = prediction[start_idx:end_idx+1]
        zoom_matlab = matlab_bfs[start_idx:end_idx+1]

        # 创建放大图
        plt.figure(figsize=(12, 8))

        plt.plot(zoom_positions, zoom_target, 'b-', linewidth=3, label='Truth BFS', marker='o', markersize=4)
        plt.plot(zoom_positions, zoom_prediction, 'r--', linewidth=3, label='Deep Learning Prediction', marker='s', markersize=4)
        plt.plot(zoom_positions, zoom_matlab, 'g:', linewidth=3, label='Traditional MATLAB Method', marker='^', markersize=4)

        plt.xlabel('Fiber Position (m)', fontsize=14)
        plt.ylabel('BFS (MHz)', fontsize=14)
        plt.title(f'Strain Region {i+1} Zoom-in ({zoom_start:.1f} - {zoom_end:.1f} m)', fontsize=16, fontweight='bold')
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)

        # 设置x轴刻度（每1m一个刻度）
        x_ticks = np.arange(np.ceil(zoom_start), np.floor(zoom_end) + 1, 1)
        plt.xticks(x_ticks)

        # 添加区域统计信息
        region_stats = f"""Region {i+1} Statistics ({zoom_start:.1f} - {zoom_end:.1f} m):
Truth: {np.mean(zoom_target):.3f} ± {np.std(zoom_target):.3f} MHz
Deep Learning: {np.mean(zoom_prediction):.3f} ± {np.std(zoom_prediction):.3f} MHz
Traditional Method: {np.mean(zoom_matlab):.3f} ± {np.std(zoom_matlab):.3f} MHz

Prediction Error: {np.mean(np.abs(zoom_prediction - zoom_target)):.4f} MHz
Traditional Error: {np.mean(np.abs(zoom_matlab - zoom_target)):.4f} MHz"""

        plt.text(0.02, 0.98, region_stats, transform=plt.gca().transAxes,
                 verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                 fontsize=10)

        plt.tight_layout()
        save_path = f'results/strain_region_{i+1}_zoom.png'
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"✅ 应变区域{i+1}放大图已保存: {save_path}")
        plt.show()

def main():
    """主函数"""
    print("🔍 开始三种方法BFS分布对比分析...")
    
    # 确保results目录存在
    if not os.path.exists('results'):
        os.makedirs('results')
    
    try:
        # 1. 读取botda_test保存的真实BFS和预测结果
        print("\n📖 读取botda_test保存的真实BFS和预测结果...")
        prediction, target, positions = load_botda_test_results()

        # 确定统一的数据长度（以botda_test结果为准）
        target_length = len(target)
        print(f"\n🎯 统一数据长度: {target_length} 点 (0-160m)")

        # 2. 读取MATLAB传统方法结果并截取到相同长度
        print("\n📖 读取MATLAB传统方法结果...")
        matlab_bfs_aligned = load_matlab_results(target_length=target_length)

        # 3. 最终数据长度确认
        print(f"\n✅ 数据长度对齐完成:")
        print(f"   真实BFS: {len(target)} 点")
        print(f"   预测BFS: {len(prediction)} 点")
        print(f"   MATLAB BFS: {len(matlab_bfs_aligned)} 点")
        print(f"   空间范围: {positions[0]:.1f} - {positions[-1]:.1f} m")
        
        # 4. 检测应变区域
        print("\n🔍 检测应变区域...")
        strain_regions = detect_strain_regions(target, positions)

        # 5. 绘制主对比图
        print("\n📊 绘制主对比图...")
        plot_comparison(prediction, target, matlab_bfs_aligned, positions)

        # 6. 绘制应变区域放大图
        print("\n🔍 绘制应变区域放大图...")
        plot_strain_regions(prediction, target, matlab_bfs_aligned, positions, strain_regions)
        
        print(f"\n🎉 对比分析完成！共生成 {1 + len(strain_regions)} 张图片")
        
    except Exception as e:
        print(f"❌ 对比分析失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
