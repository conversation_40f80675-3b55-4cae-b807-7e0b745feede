# ---------------------------------------------------------------
#  测试小波损失函数在训练中的集成
#  
#  功能：
#  1. 验证小波损失函数能正确集成到训练循环
#  2. 测试损失函数的梯度计算
#  3. 验证训练和验证的损失一致性
# ---------------------------------------------------------------

import torch
import torch.nn as nn
import torch.nn.functional as F
from wavelet_loss import MultiScaleWaveletLoss, FrequencyDomainLoss

def test_wavelet_integration():
    """测试小波损失在训练中的集成"""
    print("🧪 测试小波损失训练集成...")
    
    # 设备设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建简单的测试模型
    class SimpleModel(nn.Module):
        def __init__(self):
            super().__init__()
            self.conv = nn.Conv1d(4, 4, kernel_size=3, padding=1)
            
        def forward(self, x):
            return self.conv(x)
    
    model = SimpleModel().to(device)
    
    # 初始化损失函数
    criterion = nn.MSELoss()
    
    try:
        wavelet_criterion = MultiScaleWaveletLoss(
            wave='db4',
            level=3,
            loss_type='l1',
            base_weight=0.05,
            decay=0.6,
            include_lowpass=False
        ).to(device)
        print("✅ 小波损失初始化成功")
    except Exception as e:
        print(f"⚠️  小波损失初始化失败，使用频域损失替代: {e}")
        wavelet_criterion = FrequencyDomainLoss(
            weight=0.05,
            high_freq_boost=3.0,
            loss_type='l1'
        ).to(device)
    
    # 创建测试数据
    B, C, L = 2, 4, 200  # 模拟BFS数据维度
    input_data = torch.randn(B, C, L).to(device)
    target_data = torch.randn(B, C, L).to(device)
    
    # 测试前向传播
    model.train()
    pred = model(input_data)
    
    # 计算各种损失
    main_loss = criterion(pred, target_data)
    
    # 平滑性损失
    def smoothness_loss(y, lam=2e-4):
        diff = y[..., 1:] - y[..., :-1]
        return lam * (diff**2).mean()
    
    # 梯度损失
    def gradient_loss(y_pred, y_true, alpha=100.0):
        pred_grad = y_pred[..., 1:] - y_pred[..., :-1]
        true_grad = y_true[..., 1:] - y_true[..., :-1]
        return alpha * F.mse_loss(pred_grad, true_grad)
    
    smooth_loss = smoothness_loss(pred, 2e-4)
    grad_loss = gradient_loss(pred, target_data, alpha=100.0)
    wave_loss = wavelet_criterion(pred, target_data)
    
    # 总损失
    total_loss = main_loss + smooth_loss + grad_loss + wave_loss
    
    print(f"📊 损失分解:")
    print(f"  主损失 (MSE): {main_loss.item():.6f}")
    print(f"  平滑损失: {smooth_loss.item():.6f}")
    print(f"  梯度损失: {grad_loss.item():.6f}")
    print(f"  小波损失: {wave_loss.item():.6f}")
    print(f"  总损失: {total_loss.item():.6f}")
    
    # 测试反向传播
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3, eps=1e-6)
    
    optimizer.zero_grad()
    total_loss.backward()
    
    # 检查梯度
    grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), 0.05)
    print(f"  梯度范数: {grad_norm:.6f}")
    
    optimizer.step()
    
    print("✅ 训练步骤测试成功")
    
    # 测试验证模式
    model.eval()
    with torch.no_grad():
        val_pred = model(input_data)
        val_main_loss = criterion(val_pred, target_data)
        val_smooth_loss = smoothness_loss(val_pred, 2e-4)
        val_grad_loss = gradient_loss(val_pred, target_data, alpha=100.0)
        val_wave_loss = wavelet_criterion(val_pred, target_data)
        val_total_loss = val_main_loss + val_smooth_loss + val_grad_loss + val_wave_loss
        
        print(f"📊 验证损失:")
        print(f"  验证总损失: {val_total_loss.item():.6f}")
    
    print("✅ 验证步骤测试成功")
    
    # 测试小波损失的高频敏感性
    print("\n🔍 测试小波损失的高频敏感性...")
    
    # 创建平滑信号和高频信号
    smooth_signal = torch.sin(torch.linspace(0, 4*3.14159, L)).unsqueeze(0).unsqueeze(0).repeat(B, C, 1).to(device)
    noisy_signal = smooth_signal + 0.1 * torch.randn_like(smooth_signal)
    
    smooth_wave_loss = wavelet_criterion(smooth_signal, smooth_signal)
    noisy_wave_loss = wavelet_criterion(noisy_signal, smooth_signal)
    
    print(f"  平滑信号小波损失: {smooth_wave_loss.item():.6f}")
    print(f"  噪声信号小波损失: {noisy_wave_loss.item():.6f}")
    print(f"  高频敏感性比率: {noisy_wave_loss.item() / (smooth_wave_loss.item() + 1e-8):.2f}x")
    
    if noisy_wave_loss.item() > smooth_wave_loss.item() * 2:
        print("✅ 小波损失对高频变化敏感")
    else:
        print("⚠️  小波损失对高频变化不够敏感")
    
    print("\n🎉 所有测试完成！")

if __name__ == '__main__':
    test_wavelet_integration()
