# 快速测试数据加载
import os
from botda_train import BOTDADataset

print("检查数据目录...")
data_dirs = ["./DL_DATA", "./BOTDA_Dataset", "./data"]

data_dir = None
for d in data_dirs:
    if os.path.exists(d):
        print(f"✅ 找到数据目录: {d}")
        data_dir = d
        break

if data_dir is None:
    print("❌ 未找到数据目录，请确保以下目录之一存在:")
    for d in data_dirs:
        print(f"  - {d}")
    print("\n或者运行MATLAB脚本生成数据")
    exit(1)

try:
    print(f"尝试从 {data_dir} 加载数据...")
    # 创建数据集实例，这会触发_get_data_info方法
    dataset = BOTDADataset(data_dir, "train", window_size=5, max_samples=1)
    print("✅ 数据集创建成功")
    print(f"数据集长度: {len(dataset)}")

    # 尝试获取第一个样本
    if len(dataset) > 0:
        stokes, bfs = dataset[0]
        print(f"✅ 样本获取成功")
        print(f"斯托克斯信号形状: {stokes.shape}")
        print(f"BFS标签形状: {bfs.shape}")
    else:
        print("⚠️ 数据集为空")

except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
