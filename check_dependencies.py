#!/usr/bin/env python3
# ---------------------------------------------------------------
#  BOTDA 深度学习系统依赖检查脚本
#  
#  功能：
#  1. 检查所有必需依赖是否正确安装
#  2. 验证pytorch_wavelets小波库功能
#  3. 测试GPU可用性
#  4. 提供安装建议
# ---------------------------------------------------------------

import sys
import importlib
import subprocess

def check_package(package_name, import_name=None, version_attr=None):
    """检查包是否安装并获取版本信息"""
    if import_name is None:
        import_name = package_name
    
    try:
        module = importlib.import_module(import_name)
        if version_attr:
            version = getattr(module, version_attr, 'Unknown')
        elif hasattr(module, '__version__'):
            version = module.__version__
        else:
            version = 'Unknown'
        return True, version
    except ImportError:
        return False, None

def check_pytorch_wavelets():
    """专门检查pytorch_wavelets的功能"""
    try:
        from pytorch_wavelets import DWT1DForward
        import torch
        
        # 测试基本功能
        dwt = DWT1DForward(J=3, wave='db4', mode='zero')
        test_signal = torch.randn(1, 1, 200)
        Yl, Yh = dwt(test_signal)
        
        return True, f"功能正常 (输入{test_signal.shape} -> 低频{Yl.shape} + 高频{len(Yh)}层)"
    except ImportError:
        return False, "未安装"
    except Exception as e:
        return False, f"功能异常: {e}"

def check_gpu():
    """检查GPU可用性"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
            return True, f"{gpu_count}个GPU可用, 主GPU: {gpu_name}"
        else:
            return False, "CUDA不可用"
    except:
        return False, "PyTorch未安装"

def main():
    print("🔍 BOTDA 深度学习系统依赖检查")
    print("=" * 50)
    
    # 核心依赖检查
    core_packages = [
        ("torch", "torch", "__version__"),
        ("torchvision", "torchvision", "__version__"),
        ("numpy", "numpy", "__version__"),
        ("scipy", "scipy", "__version__"),
        ("matplotlib", "matplotlib", "__version__"),
        ("tensorboard", "tensorboard", "__version__"),
        ("tqdm", "tqdm", "__version__"),
        ("scikit-learn", "sklearn", "__version__"),
    ]
    
    print("\n📦 核心依赖包检查:")
    all_core_ok = True
    for package, import_name, version_attr in core_packages:
        installed, version = check_package(package, import_name, version_attr)
        status = "✅" if installed else "❌"
        version_str = f"v{version}" if version else "未安装"
        print(f"  {status} {package:<15} {version_str}")
        if not installed:
            all_core_ok = False
    
    # 专门检查pytorch_wavelets
    print("\n🌊 小波变换库检查:")
    wavelets_ok, wavelets_info = check_pytorch_wavelets()
    status = "✅" if wavelets_ok else "❌"
    print(f"  {status} pytorch_wavelets  {wavelets_info}")
    
    # GPU检查
    print("\n🖥️  GPU支持检查:")
    gpu_ok, gpu_info = check_gpu()
    status = "✅" if gpu_ok else "⚠️ "
    print(f"  {status} CUDA支持        {gpu_info}")
    
    # 小波损失函数测试
    print("\n🧪 小波损失函数测试:")
    try:
        from wavelet_loss import MultiScaleWaveletLoss, WAVELETS_AVAILABLE
        
        if WAVELETS_AVAILABLE:
            print("  ✅ 小波损失函数可用 (使用真正的小波变换)")
            
            # 功能测试
            import torch
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            loss_fn = MultiScaleWaveletLoss(
                wave='db4', level=3, base_weight=0.05
            ).to(device)
            
            test_data = torch.randn(2, 4, 200).to(device)
            loss = loss_fn(test_data, test_data)
            print(f"  ✅ 功能测试通过 (损失值: {loss.item():.6f})")
            
        else:
            print("  ⚠️  小波损失函数使用FFT替代方案")
            
    except Exception as e:
        print(f"  ❌ 小波损失函数测试失败: {e}")
    
    # 总结和建议
    print("\n" + "=" * 50)
    if all_core_ok and wavelets_ok:
        print("🎉 所有依赖检查通过！系统已准备就绪。")
    else:
        print("⚠️  发现问题，请按以下建议修复：")
        
        if not all_core_ok:
            print("\n📥 安装核心依赖:")
            print("   pip install -r requirements.txt")
        
        if not wavelets_ok:
            print("\n🌊 安装小波变换库:")
            print("   pip install pytorch_wavelets==1.3.0")
            print("   如果安装失败，系统会自动使用FFT替代方案")
        
        if not gpu_ok:
            print("\n🖥️  GPU支持 (可选):")
            print("   安装CUDA版本的PyTorch以获得更好性能")
            print("   访问: https://pytorch.org/get-started/locally/")
    
    print("\n🚀 启动训练:")
    print("   python botda_train.py")

if __name__ == '__main__':
    main()
