# BOTDA 深度学习信号处理系统

基于深度学习的布里渊光时域分析（BOTDA）信号处理系统，用于从斯托克斯信号中预测布里渊频移（BFS）分布。

## 🎯 项目概述

本项目实现了一个混合U-Net与TCN的深度学习网络（HybridUNetTCN），专门用于BOTDA信号处理：

- **输入**：多段拼接的斯托克斯时间序列信号 `(batch, 3, 2000)`
- **输出**：对应的布里渊频移（BFS）空间分布 `(batch, 4, 200)`
- **创新点**：结合U-Net、TCN和注意力机制，实现高精度的信号到空间映射

## 🏗️ 模型架构

### HybridUNetTCN 网络结构

#### 1. U-Net 骨架
- **编码器**：4层下采样，提取多尺度特征
- **解码器**：4层上采样，恢复空间分辨率  
- **跳跃连接**：保持细节信息

#### 2. TCN 模块
- **ResDilatedBlock**：残差空洞卷积块
- **多尺度感受野**：空洞率 [1, 2, 4, 8, 16]
- **残差连接**：稳定梯度流动

#### 3. 注意力瓶颈
- **SelfAttentionBlock**：全局依赖建模
- **多头注意力**：8个注意力头
- **LayerNorm + 残差**：训练稳定性

#### 4. 精确输出层
- **卷积特征提取**：Conv1d(64→32) + GELU
- **全连接层**：Linear(64000→800) 精确维度控制
- **重新整形**：(B,800) → (B,4,200) 符合物理意义

### 数据流示意图

```
输入: (B, 3, 2000)     # B=批大小, 3=频率通道, 2000=时间点
  ↓ Stem (7×1 Conv)
(B, 64, 2000)         # 初始特征提取
  ↓ Encoder (4层)
(B, 1024, 125)        # 编码压缩 (4次下采样)
  ↓ Bottleneck + Attention
(B, 1024, 125)        # 瓶颈层 + 自注意力
  ↓ Decoder (4层)
(B, 64, 2000)         # 解码重建 + Skip连接
  ↓ Head (Conv + FC)
输出: (B, 4, 200)      # BFS预测 (4段×200空间点)
```

## 📁 项目结构

```
SBS/
├── botda_model.py              # 核心模型定义
├── botda_train.py              # 训练脚本
├── botda_test.py               # 测试和评估脚本
├── generate_yhbfuuju_qudnjixm_test.m  # MATLAB数据生成脚本
├── BOTDA_Dataset/              # 数据集目录
│   ├── train/                  # 训练数据
│   ├── val/                    # 验证数据
│   ├── test/                   # 测试数据
│   └── bfs_stats.mat          # BFS统计信息
├── checkpoints/                # 模型检查点
├── test_results/              # 测试结果和可视化
└── requirements.txt           # 依赖包列表
```

## 🚀 快速开始

### 1. 环境配置

```bash
# 创建虚拟环境
conda create -n botda_env python=3.9
conda activate botda_env

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据生成

使用MATLAB生成训练数据：

```matlab
% 在MATLAB中运行
run('generate_yhbfuuju_qudnjixm_test.m')
```

### 3. 模型训练

```bash
python botda_train.py
```

### 4. 模型测试

```bash
python botda_test.py
```

## 📊 数据格式

### 输入数据
- **斯托克斯信号**：`(3, 2000)` - 3个频率通道，5段×400时间点
- **窗口策略**：滑动窗口，窗口大小5段，步长4段

### 输出数据  
- **BFS分布**：`(4, 200)` - 4段×200空间点
- **归一化范围**：[-40, 50] MHz

## 🎛️ 训练配置

### 主要超参数
- **学习率**：1e-4
- **批大小**：32
- **优化器**：AdamW (weight_decay=1e-4)
- **损失函数**：MSE Loss
- **学习率调度**：ReduceLROnPlateau

### 数据增强
- **噪声注入**：高斯噪声 (std=0.01)
- **信号缩放**：随机缩放 [0.95, 1.05]

## 📈 性能评估

### 评估指标
- **RMSE**：均方根误差
- **MAE**：平均绝对误差  
- **R²**：决定系数
- **Max Error**：最大误差

### 可视化输出
- 完整光纤BFS分布对比图
- 分段预测结果图
- 预测vs真实值散点图
- 误差分布直方图

## 🔧 模型特性

### 技术亮点
1. **混合架构**：U-Net + TCN + Attention
2. **多尺度特征**：空洞卷积捕捉不同尺度模式
3. **全局建模**：注意力机制建立长距离依赖
4. **精确输出**：全连接层确保维度精确匹配
5. **稳定训练**：残差连接和归一化保证收敛

### 创新点
- **物理约束**：输出维度严格对应物理空间栅格
- **滑窗策略**：处理任意长度光纤的BFS预测
- **端到端**：从原始信号直接预测BFS分布

## 📋 系统要求

- **Python**: 3.8+
- **PyTorch**: 1.12+
- **CUDA**: 11.0+ (可选，用于GPU加速)
- **MATLAB**: R2020a+ (用于数据生成)

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
