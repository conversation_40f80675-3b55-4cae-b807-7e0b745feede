#!/usr/bin/env python3
# ---------------------------------------------------------------
#  pytorch_wavelets 问题诊断和修复脚本
#  基于专家建议的最全面解决方案
# ---------------------------------------------------------------

import sys
import os
import subprocess
import pprint

def step1_diagnose():
    """第一步：诊断到底是包没找到还是类没找到"""
    print("🔍 第一步：诊断包和类的可用性")
    print("="*50)
    
    try:
        import pytorch_wavelets as pw
        print("✅ package found at:", pw.__file__)
        
        try:
            import pkg_resources
            version = pkg_resources.get_distribution("pytorch_wavelets").version
            print("   version:", version)
        except:
            print("   version: Unknown")
        
        # 检查DWT1DForward是否存在
        has_top = hasattr(pw, "DWT1DForward")
        print("   DWT1DForward exists (top level):", has_top)
        
        # 检查子模块
        try:
            from pytorch_wavelets.dwt import DWT1DForward
            has_sub = True
            print("   DWT1DForward exists (dwt submodule): True")
        except:
            has_sub = False
            print("   DWT1DForward exists (dwt submodule): False")
        
        return True, has_top or has_sub
        
    except ImportError as e:
        print("❌ ImportError:", e)
        print("   sys.path (TOP 5) ->")
        pprint.pprint(sys.path[:5])
        return False, False

def step2_check_conflicts():
    """第二步：检查文件名冲突"""
    print("\n🔍 第二步：检查文件名冲突")
    print("="*50)
    
    # 检查当前目录和上级目录
    search_paths = ['.', '..']
    conflicts_found = False
    
    for path in search_paths:
        try:
            result = subprocess.run(['find', path, '-maxdepth', '2', '-name', 'pytorch_wavelets*'], 
                                  capture_output=True, text=True)
            if result.stdout.strip():
                print(f"⚠️  在 {path} 发现可能的冲突文件:")
                for line in result.stdout.strip().split('\n'):
                    print(f"   {line}")
                conflicts_found = True
        except:
            # 如果find命令不可用，手动检查
            try:
                items = os.listdir(path)
                conflicts = [item for item in items if 'pytorch_wavelets' in item]
                if conflicts:
                    print(f"⚠️  在 {path} 发现可能的冲突文件:")
                    for item in conflicts:
                        print(f"   {os.path.join(path, item)}")
                    conflicts_found = True
            except:
                pass
    
    if not conflicts_found:
        print("✅ 未发现文件名冲突")
    
    return conflicts_found

def step3_check_environment():
    """第三步：检查环境一致性"""
    print("\n🔍 第三步：检查环境一致性")
    print("="*50)
    
    # 检查python和pip路径
    try:
        python_path = subprocess.run(['which', 'python'], capture_output=True, text=True).stdout.strip()
        pip_path = subprocess.run(['which', 'pip'], capture_output=True, text=True).stdout.strip()
        
        print(f"Python路径: {python_path}")
        print(f"Pip路径: {pip_path}")
        
        # 检查是否一致
        python_dir = os.path.dirname(python_path)
        pip_dir = os.path.dirname(pip_path)
        
        if python_dir == pip_dir:
            print("✅ Python和pip使用同一环境")
            return True
        else:
            print("⚠️  Python和pip可能使用不同环境")
            return False
            
    except Exception as e:
        print(f"❌ 检查环境时出错: {e}")
        return False

def step4_fix_installation():
    """第四步：修复安装"""
    print("\n🔧 第四步：修复安装")
    print("="*50)
    
    print("尝试方案1: 完全重新安装...")
    try:
        # 卸载
        subprocess.run([sys.executable, '-m', 'pip', 'uninstall', 'pytorch_wavelets', '-y'], 
                      capture_output=True)
        
        # 清理缓存
        subprocess.run([sys.executable, '-m', 'pip', 'cache', 'purge'], 
                      capture_output=True)
        
        # 重新安装
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 'pytorch_wavelets==1.3.0', 
                               '--no-cache-dir', '--force-reinstall'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 重新安装成功")
            return test_import()
        else:
            print("❌ 重新安装失败:")
            print(result.stderr)
    except Exception as e:
        print(f"❌ 重新安装过程出错: {e}")
    
    print("\n尝试方案2: 安装最新master版本...")
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', 
                               'git+https://github.com/fbcotter/pytorch_wavelets.git@master'],
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 安装master版本成功")
            return test_import()
        else:
            print("❌ 安装master版本失败:")
            print(result.stderr)
    except Exception as e:
        print(f"❌ 安装master版本过程出错: {e}")
    
    return False

def test_import():
    """测试导入"""
    try:
        # 方案1：顶层导入
        from pytorch_wavelets import DWT1DForward
        print("✅ 顶层导入成功")
        return True
    except ImportError:
        try:
            # 方案2：子模块导入
            from pytorch_wavelets.dwt import DWT1DForward
            print("✅ 子模块导入成功")
            return True
        except ImportError:
            print("❌ 两种导入方式都失败")
            return False

def step5_test_functionality():
    """第五步：测试功能"""
    print("\n🧪 第五步：测试功能")
    print("="*50)
    
    try:
        # 尝试导入
        try:
            from pytorch_wavelets import DWT1DForward
        except ImportError:
            from pytorch_wavelets.dwt import DWT1DForward
        
        # 测试创建实例
        dwt = DWT1DForward(J=3, wave='db4', mode='zero')
        print("✅ DWT1DForward实例创建成功")
        
        # 测试功能
        import torch
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        test_signal = torch.randn(2, 4, 200, device=device)
        
        dwt = dwt.to(device)
        Yl, Yh = dwt(test_signal)
        
        print(f"✅ 小波变换测试成功")
        print(f"   输入: {test_signal.shape}")
        print(f"   低频输出: {Yl.shape}")
        print(f"   高频层数: {len(Yh)}")
        print(f"   设备: {device}")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 pytorch_wavelets 问题诊断和修复工具")
    print("基于专家建议的最全面解决方案")
    print("="*60)
    
    # 第一步：诊断
    package_found, class_found = step1_diagnose()
    
    if package_found and class_found:
        print("\n🎉 pytorch_wavelets 已正常工作！")
        step5_test_functionality()
        return
    
    # 第二步：检查冲突
    conflicts = step2_check_conflicts()
    
    # 第三步：检查环境
    env_ok = step3_check_environment()
    
    # 第四步：修复安装
    if not package_found or not class_found:
        success = step4_fix_installation()
        if success:
            step5_test_functionality()
            return
    
    # 如果所有方案都失败
    print("\n" + "="*60)
    print("⚠️  所有修复方案都失败，建议使用FFT替代方案")
    print("FFT方案同样能够:")
    print("✅ 解决MSE'和稀泥'问题")
    print("✅ 强化高频细节保持")
    print("✅ 完全集成到训练循环")
    print("\n直接运行训练即可:")
    print("python botda_train.py")
    print("="*60)

if __name__ == '__main__':
    main()
